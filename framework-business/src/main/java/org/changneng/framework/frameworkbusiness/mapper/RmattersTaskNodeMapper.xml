<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.RmattersTaskNodeMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="R_TASK_ID" property="rTaskId" jdbcType="VARCHAR" />
    <result column="NODE_CODE" property="nodeCode" jdbcType="DECIMAL" />
    <result column="NODE_NAME" property="nodeName" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_ID" property="departmentId" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_NAME" property="departmentName" jdbcType="VARCHAR" />
    <result column="IS_OPEN" property="isOpen" jdbcType="DECIMAL" />
    <result column="START_HANDLE_DATE" property="startHandleDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, R_TASK_ID, NODE_CODE, NODE_NAME, DEPARTMENT_ID, DEPARTMENT_NAME, IS_OPEN, START_HANDLE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from R_MATTERS_TASK_NODE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectInfoByTaskId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from R_MATTERS_TASK_NODE
    where R_TASK_ID = #{rTaskId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from R_MATTERS_TASK_NODE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    insert into R_MATTERS_TASK_NODE (ID, R_TASK_ID, NODE_CODE, 
      NODE_NAME, DEPARTMENT_ID, DEPARTMENT_NAME, 
      IS_OPEN, START_HANDLE_DATE)
    values (#{id,jdbcType=VARCHAR}, #{rTaskId,jdbcType=VARCHAR}, #{nodeCode,jdbcType=DECIMAL}, 
      #{nodeName,jdbcType=VARCHAR}, #{departmentId,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR}, 
      #{isOpen,jdbcType=DECIMAL}, #{startHandleDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into R_MATTERS_TASK_NODE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="rTaskId != null" >
        R_TASK_ID,
      </if>
      <if test="nodeCode != null" >
        NODE_CODE,
      </if>
      <if test="nodeName != null" >
        NODE_NAME,
      </if>
      <if test="departmentId != null" >
        DEPARTMENT_ID,
      </if>
      <if test="departmentName != null" >
        DEPARTMENT_NAME,
      </if>
      <if test="isOpen != null" >
        IS_OPEN,
      </if>
      <if test="startHandleDate != null" >
        START_HANDLE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rTaskId != null" >
        #{rTaskId,jdbcType=VARCHAR},
      </if>
      <if test="nodeCode != null" >
        #{nodeCode,jdbcType=DECIMAL},
      </if>
      <if test="nodeName != null" >
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null" >
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="isOpen != null" >
        #{isOpen,jdbcType=DECIMAL},
      </if>
      <if test="startHandleDate != null" >
        #{startHandleDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- a -->
    <update id="updateInfoByTaskId" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    update R_MATTERS_TASK_NODE
    <set >
      <if test="isOpen != null" >
        IS_OPEN = #{isOpen,jdbcType=DECIMAL},
      </if>
      <if test="startHandleDate != null" >
        START_HANDLE_DATE = #{startHandleDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where R_TASK_ID = #{rTaskId,jdbcType=VARCHAR} AND  NODE_CODE = #{nodeCode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    update R_MATTERS_TASK_NODE
    <set >
      <if test="rTaskId != null" >
        R_TASK_ID = #{rTaskId,jdbcType=VARCHAR},
      </if>
      <if test="nodeCode != null" >
        NODE_CODE = #{nodeCode,jdbcType=DECIMAL},
      </if>
      <if test="nodeName != null" >
        NODE_NAME = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null" >
        DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="isOpen != null" >
        IS_OPEN = #{isOpen,jdbcType=DECIMAL},
      </if>
      <if test="startHandleDate != null" >
        START_HANDLE_DATE = #{startHandleDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
    update R_MATTERS_TASK_NODE
    set R_TASK_ID = #{rTaskId,jdbcType=VARCHAR},
      NODE_CODE = #{nodeCode,jdbcType=DECIMAL},
      NODE_NAME = #{nodeName,jdbcType=VARCHAR},
      DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      IS_OPEN = #{isOpen,jdbcType=DECIMAL},
      START_HANDLE_DATE = #{startHandleDate,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
   <update id="updateByTaskId"  parameterType="java.lang.String" >
    update R_MATTERS_TASK_NODE
    <set >
      <if test="isOpen != null" >
        IS_OPEN = #{isOpen,jdbcType=DECIMAL},
      </if>
      <if test="startHandleDate != null" >
        START_HANDLE_DATE = #{startHandleDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where R_TASK_ID = #{id,jdbcType=VARCHAR} AND  NODE_CODE = #{nodeCode,jdbcType=DECIMAL}
  </update>
    <update id="updateInfosByTaskId" parameterType="org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode" >
           update R_MATTERS_TASK_NODE set
        IS_OPEN = 0, START_HANDLE_DATE = null
    where R_TASK_ID = #{rTaskId,jdbcType=VARCHAR} AND  NODE_CODE = #{nodeCode,jdbcType=DECIMAL}
  </update>
</mapper>