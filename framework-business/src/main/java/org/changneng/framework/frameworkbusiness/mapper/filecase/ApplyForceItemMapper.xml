<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.ApplyForceItemMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceItem">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="ITEM_TYPE" jdbcType="DECIMAL" property="itemType" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, NAME, OBJECT_ID, UPDATE_DATE, CREATE_DATE, ITEM_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APPLY_FORCE_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </select>
   <select id="selectByObjId" resultType="String">
  	select id
  	from APPLY_FORCE_ITEM
  	where object_id=#{objectId, jdbcType=VARCHAR}
  </select>
  <delete id="deleteByObjId">
  	delete from APPLY_FORCE_ITEM
  	where object_id = #{objectId, jdbcType=VARCHAR}
  </delete>
  <update id="updateObjectId" >
  	update APPLY_FORCE_ITEM set object_id=#{objectId, jdbcType=VARCHAR} where id=#{id, jdbcType=VARCHAR}
  </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from APPLY_FORCE_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceItem">
    insert into APPLY_FORCE_ITEM (ID, NAME, OBJECT_ID, 
      UPDATE_DATE, CREATE_DATE, ITEM_TYPE
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP}, #{itemType,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceItem">
    insert into APPLY_FORCE_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="objectId != null">
        OBJECT_ID,
      </if>
      <if test="updateDate != null">
        UPDATE_DATE,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="itemType != null">
        ITEM_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceItem">
    update APPLY_FORCE_ITEM
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null">
        ITEM_TYPE = #{itemType,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceItem">
    update APPLY_FORCE_ITEM
    set NAME = #{name,jdbcType=VARCHAR},
      OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      ITEM_TYPE = #{itemType,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>