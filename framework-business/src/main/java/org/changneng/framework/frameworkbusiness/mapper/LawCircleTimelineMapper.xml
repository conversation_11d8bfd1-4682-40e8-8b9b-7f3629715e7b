<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.LawCircleTimelineMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CREAT_DATE" jdbcType="TIMESTAMP" property="creatDate" />
    <result column="CREAT_USER_ID" jdbcType="VARCHAR" property="creatUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CREAT_DATE, CREAT_USER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from LAW_CIRCLE_TIMELINE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from LAW_CIRCLE_TIMELINE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
    insert into LAW_CIRCLE_TIMELINE (ID, CREAT_DATE, CREAT_USER_ID
      )
    values (#{id,jdbcType=VARCHAR}, #{creatDate,jdbcType=TIMESTAMP}, #{creatUserId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
  	<selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into LAW_CIRCLE_TIMELINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="creatDate != null">
        CREAT_DATE,
      </if>
      <if test="creatUserId != null">
        CREAT_USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="creatDate != null">
        #{creatDate,jdbcType=TIMESTAMP},
      </if>
      <if test="creatUserId != null">
        #{creatUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
    update LAW_CIRCLE_TIMELINE
    <set>
      <if test="creatDate != null">
        CREAT_DATE = #{creatDate,jdbcType=TIMESTAMP},
      </if>
      <if test="creatUserId != null">
        CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
    update LAW_CIRCLE_TIMELINE
    set CREAT_DATE = #{creatDate,jdbcType=TIMESTAMP},
      CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByCreatUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
  		select 
  		<include refid="Base_Column_List"/>
  		from LAW_CIRCLE_TIMELINE where CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR}
  		order by CREAT_DATE desc 
  </select>
  <delete id="deleteByCreatDateAndUserId" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
  		delete from LAW_CIRCLE_TIMELINE 
  		where CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR} and to_char(CREAT_DATE,'yyyy-MM-dd') = to_char(#{creatDate},'yyyy-MM-dd')
  </delete>
  <select id="selectByCreatUserIdAndCreatDate" parameterType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline" resultType="org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline">
  		select 
  		<include refid="Base_Column_List"/>
  		from LAW_CIRCLE_TIMELINE where CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR} and to_char(CREAT_DATE,'yyyy-MM-dd') = to_char(#{creatDate},'yyyy-MM-dd')
  </select>
</mapper>