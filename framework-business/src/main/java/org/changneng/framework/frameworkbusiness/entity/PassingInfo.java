package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

import org.hibernate.validator.constraints.NotBlank;

public class PassingInfo {
    private String id;
    
    @NotBlank(message="办理人姓名不能为空")
    private String handleUserName;
    
    @NotBlank(message="办理人id不能为空")
    private String handleUserId;
    
    @NotBlank(message="办理人区划code不能为空")
    private String handleUserCode;
    
    @NotBlank(message="办理人区划名称不能为空")
    private String handleUserCodeName;
    
    @NotBlank(message="办理人部门id不能为空")
    private String handleUserDeptId;

    @NotBlank(message="办理人部门编码不能为空")
    private String handleUserDeptNum;
    
    @NotBlank(message="办理部门名称不能为空")
    private String handleDepartmentName;
    
    @NotBlank(message="执法对象ID不能为空")
    private String lawObjectId;
    
    @NotBlank(message="执法对象名称不能为空")
    private String lawObjectName;
    
    @NotBlank(message="执法对象类型不能为空")
    private String lawObjectType;

    private Date passDate;
    
    @NotBlank(message="当前地理坐标x不能为空")
    private String gisCoordinateX;
    
    @NotBlank(message="当前地理坐标y不能为空")
    private String gisCoordinateY;
    
    @NotBlank(message="设备ID不能为空")
    private String deviceId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getHandleUserName() {
        return handleUserName;
    }

    public void setHandleUserName(String handleUserName) {
        this.handleUserName = handleUserName == null ? null : handleUserName.trim();
    }

    public String getHandleUserId() {
        return handleUserId;
    }

    public void setHandleUserId(String handleUserId) {
        this.handleUserId = handleUserId == null ? null : handleUserId.trim();
    }

    public String getHandleUserCode() {
        return handleUserCode;
    }

    public void setHandleUserCode(String handleUserCode) {
        this.handleUserCode = handleUserCode == null ? null : handleUserCode.trim();
    }

    public String getHandleUserCodeName() {
        return handleUserCodeName;
    }

    public void setHandleUserCodeName(String handleUserCodeName) {
        this.handleUserCodeName = handleUserCodeName == null ? null : handleUserCodeName.trim();
    }

    public String getHandleUserDeptId() {
        return handleUserDeptId;
    }

    public void setHandleUserDeptId(String handleUserDeptId) {
        this.handleUserDeptId = handleUserDeptId == null ? null : handleUserDeptId.trim();
    }

    public String getHandleUserDeptNum() {
        return handleUserDeptNum;
    }

    public void setHandleUserDeptNum(String handleUserDeptNum) {
        this.handleUserDeptNum = handleUserDeptNum == null ? null : handleUserDeptNum.trim();
    }

    public String getHandleDepartmentName() {
        return handleDepartmentName;
    }

    public void setHandleDepartmentName(String handleDepartmentName) {
        this.handleDepartmentName = handleDepartmentName == null ? null : handleDepartmentName.trim();
    }

    public String getLawObjectId() {
        return lawObjectId;
    }

    public void setLawObjectId(String lawObjectId) {
        this.lawObjectId = lawObjectId == null ? null : lawObjectId.trim();
    }

    public String getLawObjectName() {
        return lawObjectName;
    }

    public void setLawObjectName(String lawObjectName) {
        this.lawObjectName = lawObjectName == null ? null : lawObjectName.trim();
    }

    public String getLawObjectType() {
        return lawObjectType;
    }

    public void setLawObjectType(String lawObjectType) {
        this.lawObjectType = lawObjectType == null ? null : lawObjectType.trim();
    }

    public Date getPassDate() {
        return passDate;
    }

    public void setPassDate(Date passDate) {
        this.passDate = passDate;
    }

    public String getGisCoordinateX() {
        return gisCoordinateX;
    }

    public void setGisCoordinateX(String gisCoordinateX) {
        this.gisCoordinateX = gisCoordinateX == null ? null : gisCoordinateX.trim();
    }

    public String getGisCoordinateY() {
        return gisCoordinateY;
    }

    public void setGisCoordinateY(String gisCoordinateY) {
        this.gisCoordinateY = gisCoordinateY == null ? null : gisCoordinateY.trim();
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId == null ? null : deviceId.trim();
    }
}