package org.changneng.framework.frameworkbusiness.service.swingtag.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagAdjunctMapper;
import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagMapper;
import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagSnapInMapper;
import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagSubjectMapper;
import org.changneng.framework.frameworkbusiness.entity.LocalCheckItem;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTag;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagAdjunct;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagManageSeach;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagRelieve;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagSnapIn;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagSubject;
import org.changneng.framework.frameworkbusiness.service.swingtag.SwingTagService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
@Service
@Transactional
public class SwingTagServiceImpl implements SwingTagService {

	
	@Autowired
	private SwingTagMapper swingTagMapper;
	
	@Autowired
	private SwingTagSnapInMapper  swingTagSnapInMapper;
	
	@Autowired
	private SwingTagSubjectMapper swingTagSubjectMapper;
	
	@Autowired
	private SwingTagAdjunctMapper swingTagAdjunctMapper;
	
	@Override
	public PageBean<SwingTag> swingtagmanageList(
			SwingTagManageSeach swingTagManageSeach) {
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		PageHelper.startPage(swingTagManageSeach.getPageNum(), swingTagManageSeach.getPageSize());
		swingTagManageSeach.setDepmentId(sysUser.getBelongDepartmentId());
		return new PageBean<SwingTag>(swingTagMapper.swingtagmanageList(swingTagManageSeach));
	}

	/**
	 * 挂牌管理 xls下载
	 */
	@Override
	public List<SwingTag> swingtagmanageDownList(SwingTagManageSeach swingTagManageSeach) {
		List<SwingTag> swingTag=null;
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			swingTagManageSeach.setDepmentId(sysUser.getBelongDepartmentId());
			swingTag = swingTagMapper.swingtagmanageList(swingTagManageSeach);
			transferAttr(swingTag);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return swingTag;
	}
	
	/**
	 * 导出属性转换
	 * 
	 * @param swingTagList
	 * @return
	 */
	private List<SwingTag> transferAttr(List<SwingTag> swingTagList) {
		for (int i = 0; i < swingTagList.size(); i++) {
			SwingTag swingTag = swingTagList.get(i);
			if (swingTag.getReleaseStatus() != null) {
				if (swingTag.getReleaseStatus() == 1) {
					swingTag.setReleaseStatusName("已发起");
				} else {
					swingTag.setReleaseStatusName("暂存中");
				}
			}
			if (swingTag.getReleaseDate() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				swingTag.setReleaseDateTr(sdf.format(swingTag.getReleaseDate()));
			}
		}
		
		return swingTagList;
	}
	
	@Override
	public SwingTag getSwingTag(String swingtag_id,SysUsers sysUser) {
		SwingTag swingTag = new SwingTag();
		if(!ChangnengUtil.isNull(swingtag_id)){
			//修改页面
			swingTag= swingTagMapper.selectByPrimaryKey(swingtag_id);
			//获取新办理单位信息
			List<SwingTagSnapIn> swingTagSnapInList  = swingTagSnapInMapper.getSwingTagSnapInBySwinTagId(swingtag_id);
			if(swingTagSnapInList!= null && swingTagSnapInList.size()>0){
				//获取办理单位下的违法主体
				for(SwingTagSnapIn swingTagSnapIn : swingTagSnapInList){
					List<SwingTagSubject> swingTagSubjectList = swingTagSubjectMapper.getSwingTagSubjectBySnapInId(swingTagSnapIn.getId());
					if(swingTagSubjectList !=null && swingTagSubjectList.size()>0){
						swingTagSnapIn.setSwingTagSubjectList(swingTagSubjectList);
					}
				}
				swingTag.setSwingTagSnapInList(swingTagSnapInList);
			}
			//查询挂牌附件
			List<SwingTagAdjunct> swingTagAdjunctUpList = swingTagAdjunctMapper.getSwingTagAdjunctListBySwingTagId(swingtag_id);
			if(swingTagAdjunctUpList != null && swingTagAdjunctUpList.size()>0){
				swingTag.setSwingTagAdjunctUpList(swingTagAdjunctUpList);
			}
		}else{
			//新增页面
			//获取当前登录人的单位信息
			swingTag.setReleaseUnit(sysUser.getBelongDepartmentName());
			swingTag.setReleaseUnitId(sysUser.getBelongDepartmentId());
		}
		return swingTag;
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson saveSwingTag(SwingTag swingTag) {
		ResponseJson json = new ResponseJson();
		Map<String , String > map = new HashMap<String, String>();
		if(!ChangnengUtil.isNull(swingTag.getId())){
			//修改页面]
			try {
				swingTag.setUpdatetime(new Date());
				swingTagMapper.updateByPrimaryKeySelective(swingTag);
				String adjunctList = swingTag.getAdjunctList();
				map.put("swingTapId", swingTag.getId());
				if(!ChangnengUtil.isNull(adjunctList)){
					List<SwingTagAdjunct> swingTagAdjunctList = JacksonUtils.toCollection(adjunctList, new TypeReference<List<SwingTagAdjunct>>() {});
					for(SwingTagAdjunct swingTagAdjunct :swingTagAdjunctList){
						if(ChangnengUtil.isNull(swingTagAdjunct.getId())){
							swingTagAdjunct.setCreateDate(new Date());
							swingTagAdjunct.setDelmark(1);
							String fileName = swingTagAdjunct.getFileName();
							String prefix=fileName.substring(fileName.lastIndexOf(".")+1);
							if(prefix.equals("pdf")){
								swingTagAdjunct.setFileType(0);
							}else{
								swingTagAdjunct.setFileType(1);
							}
							swingTagAdjunct.setIsSwingTag(swingTag.getIsSwingTag());
							swingTagAdjunct.setSwingTagId(swingTag.getId());
							swingTagAdjunctMapper.insertSelective(swingTagAdjunct);
						}
					}
				}
				String snapInList = swingTag.getSnapInList();
				if(!ChangnengUtil.isNull(snapInList)){
					List<SwingTagSnapIn> swingTagSnapInList = JacksonUtils.toCollection(snapInList, new TypeReference<List<SwingTagSnapIn>>() {});
					for(SwingTagSnapIn swingTagSnapIn:swingTagSnapInList){
						if(ChangnengUtil.isNull(swingTagSnapIn.getId())){
							swingTagSnapIn.setDelmark(1);
							swingTagSnapIn.setSwingTagId(swingTag.getId());
							Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), swingTagSnapIn.getLimitTime());
							swingTagSnapInMapper.insertSelective(swingTagSnapIn);
							List<SwingTagSubject> swingTagSubjectList = swingTagSnapIn.getSwingTagSubjectList();
							if(swingTagSubjectList !=  null && swingTagSnapInList.size()>0){
								for(SwingTagSubject swingTagSubject :swingTagSubjectList){
									swingTagSubject.setSnapInId(swingTagSnapIn.getId());
									swingTagSubject.setSwingTagId(swingTag.getId());
									swingTagSubject.setIsSwingTag(swingTag.getIsSwingTag());
									swingTagSubject.setLimitTime(swingTagSnapIn.getLimitTime());
									swingTagSubject.setNodeTimeoutState(code);
									swingTagSubjectMapper.insertSelective(swingTagSubject);
								}
							}
						}else{
							
							SwingTagSnapIn swingTagSnapIn1 = new SwingTagSnapIn();
							swingTagSnapIn1.setId(swingTagSnapIn.getId());
							swingTagSnapIn1.setLimitTime(swingTagSnapIn.getLimitTime());
							swingTagSnapInMapper.updateByPrimaryKeySelective(swingTagSnapIn1);
							List<SwingTagSubject> swingTagSubjectList = swingTagSnapIn.getSwingTagSubjectList();
							if(swingTagSubjectList !=  null && swingTagSnapInList.size()>0){
								for(SwingTagSubject swingTagSubject :swingTagSubjectList){
									Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), swingTagSnapIn.getLimitTime());
									if(ChangnengUtil.isNull(swingTagSubject.getId())){
										swingTagSubject.setSnapInId(swingTagSnapIn.getId());
										swingTagSubject.setSwingTagId(swingTag.getId());
										swingTagSubject.setIsSwingTag(swingTag.getIsSwingTag());
										swingTagSubject.setLimitTime(swingTagSnapIn.getLimitTime());
										swingTagSubject.setNodeTimeoutState(code);
										swingTagSubjectMapper.insertSelective(swingTagSubject);
									}else{
										SwingTagSubject record = new SwingTagSubject();
										record.setId(swingTagSubject.getId());
										record.setLimitTime(swingTagSnapIn.getLimitTime());
										swingTagSubjectMapper.updateByPrimaryKeySelective(record );
									}
								}
							}
						}
					}
					if(swingTag.getSendAttId().length()>=1){
					// 去掉最后一个-
					String strSend = (swingTag.getSendAttId()).substring(0, swingTag.getSendAttId().length() - 1);
					// 拆分附件id
					String[] adjuctIds = strSend.split("-");
					System.out.println("附件---------------开始");
					for (int i = 0; i < adjuctIds.length; i++) {
						SwingTagAdjunct record = new SwingTagAdjunct();
						record.setId(adjuctIds[i]);
						record.setSwingTagId(swingTag.getId());
						record.setStatus(1);
						// 根据附件id 添加挂牌督办id
						int count = swingTagAdjunctMapper.updateByPrimaryKeySelective(record);
					}
					System.out.println("附件--------------结束");
					}
				}
				json = json.success(HttpStatus.OK.toString(), SystemStatusCode.UP_SUCCESS.toString(), "修改成功", "修改挂牌成功", map);
			} catch (Exception e) {
				 e.printStackTrace();
				 throw e;
			}
			
		}else{
			//保存页面
			try {
				SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
				swingTag.setCreatetime(new Date());
				swingTag.setCreatUserId(sysUser.getId());
				swingTag.setCreatUserName(sysUser.getLoginname());
				swingTag.setDelmark(1);
				swingTag.setIsSwingTag(swingTag.getIsSwingTag());
				swingTag.setUnitAreaCode(sysUser.getBelongAreaId());
				swingTag.setUnitAreaName(sysUser.getBelongAreaName());
				swingTag.setUpdatetime(new Date());
				swingTagMapper.insertSelective(swingTag);
				map.put("swingTapId", swingTag.getId());
				String adjunctList = swingTag.getAdjunctList();
				if(!ChangnengUtil.isNull(adjunctList)){
					List<SwingTagAdjunct> swingTagAdjunctList = JacksonUtils.toCollection(adjunctList, new TypeReference<List<SwingTagAdjunct>>() {});
					for(SwingTagAdjunct swingTagAdjunct :swingTagAdjunctList){
						swingTagAdjunct.setCreateDate(new Date());
						swingTagAdjunct.setDelmark(1);
						String fileName = swingTagAdjunct.getFileName();
						String prefix=fileName.substring(fileName.lastIndexOf(".")+1);
						  if(prefix.equals("pdf")){
							  swingTagAdjunct.setFileType(0);
	                       }else{
	                        	swingTagAdjunct.setFileType(1);
	                       }
						  swingTagAdjunct.setIsSwingTag(swingTag.getIsSwingTag());
						  swingTagAdjunct.setSwingTagId(swingTag.getId());
						  swingTagAdjunctMapper.insertSelective(swingTagAdjunct);
					}
				}
				String snapInList = swingTag.getSnapInList();
				if(!ChangnengUtil.isNull(snapInList)){
					List<SwingTagSnapIn> swingTagSnapInList = JacksonUtils.toCollection(snapInList, new TypeReference<List<SwingTagSnapIn>>() {});
					for(SwingTagSnapIn swingTagSnapIn:swingTagSnapInList){
						swingTagSnapIn.setDelmark(1);
						swingTagSnapIn.setSwingTagId(swingTag.getId());
						//swingTagSnapIn.setManageUnitId(swingTag.);
						//swingTagSnapIn.setManageUnitName();
						swingTagSnapInMapper.insertSelective(swingTagSnapIn);
						Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), swingTagSnapIn.getLimitTime());
						List<SwingTagSubject> swingTagSubjectList = swingTagSnapIn.getSwingTagSubjectList();
						if(swingTagSubjectList !=  null && swingTagSnapInList.size()>0){
							for(SwingTagSubject swingTagSubject :swingTagSubjectList){
								swingTagSubject.setSnapInId(swingTagSnapIn.getId());
								swingTagSubject.setSwingTagId(swingTag.getId());
								swingTagSubject.setIsSwingTag(swingTag.getIsSwingTag());
								swingTagSubject.setLimitTime(swingTagSnapIn.getLimitTime());
								swingTagSubject.setNodeTimeoutState(code);
								swingTagSubjectMapper.insertSelective(swingTagSubject);
							}
						}
					}
					if(swingTag.getSendAttId().length()>=1){
						// 去掉最后一个-
						String strSend = (swingTag.getSendAttId()).substring(0, swingTag.getSendAttId().length() - 1);
						// 拆分附件id
						String[] adjuctIds = strSend.split("-");
						System.out.println("附件---------------开始");
						for (int i = 0; i < adjuctIds.length; i++) {
							SwingTagAdjunct record = new SwingTagAdjunct();
							record.setId(adjuctIds[i]);
							record.setSwingTagId(swingTag.getId());
							record.setStatus(1);
							// 根据附件id 添加挂牌督办id
							int count = swingTagAdjunctMapper.updateByPrimaryKeySelective(record);
						}
						System.out.println("附件--------------结束");
					}else{
						 
					}
				}
				json = json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功",
						"保存挂牌成功", map);
			} catch (Exception e) {
				 e.printStackTrace();
				 throw e;
			}
		}
		return json;
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson deleteSnapIn(String swingTagId, String swingTagSnapId) {
		ResponseJson json = new ResponseJson();
		if(!ChangnengUtil.isNull(swingTagSnapId) && !ChangnengUtil.isNull(swingTagId)){
			try {
				SwingTagSnapIn swingTagSnapIn = new SwingTagSnapIn();
				swingTagSnapIn.setId(swingTagSnapId);
				swingTagSnapIn.setDelmark(0);
				swingTagSnapInMapper.updateByPrimaryKeySelective(swingTagSnapIn );
				swingTagSubjectMapper.updateDelByStIdAndSnapInId(swingTagId,swingTagSnapId);
				return json.success(HttpStatus.OK.toString(), SystemStatusCode.DELETE_SUCCESS.toString(), "删除成功", "删除办理单位成功", null);
			} catch (Exception e) {
				//return json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(), null);
				 e.printStackTrace();
				 throw e;
			}
		}else{
			return json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", "主键id不全", null);
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson deleteSubject(String swingTagId,
			String swingTagSubjectId) {
		ResponseJson json = new ResponseJson();
		if(!ChangnengUtil.isNull(swingTagSubjectId) && !ChangnengUtil.isNull(swingTagId)){
			try {
				swingTagSubjectMapper.updateDelByStIdAndId(swingTagId,swingTagSubjectId);
				return json.success(HttpStatus.OK.toString(), SystemStatusCode.DELETE_SUCCESS.toString(), "删除成功", "删除违法主体成功", null);
			} catch (Exception e) {
				//return json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(), null);
				 e.printStackTrace();
				 throw e;
			}
		}else{
			return json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", "主键id不全", null);
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson deleteSwingTag(String swingTagId) {
		ResponseJson json = new ResponseJson();
			try {
				if(!ChangnengUtil.isNull(swingTagId)){
					SwingTag swingTag = new SwingTag();
					swingTag.setId(swingTagId);
					swingTag.setDelmark(0);
					swingTagMapper.updateByPrimaryKeySelective(swingTag);
					swingTagSnapInMapper.updateDelmarkByTagId(swingTagId);
					swingTagSubjectMapper.updateDelmarkByTagId(swingTagId);
					swingTagAdjunctMapper.updateDelmarkByTagId(swingTagId);
					return json.success(HttpStatus.OK.toString(), SystemStatusCode.DELETE_SUCCESS.toString(), "删除成功", "删除违法主体成功", null);
				}else{
					return json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", "主键id不存在", null);
				}
			} catch (Exception e) {
				 e.printStackTrace();
				 throw e;
			}
	}
	@Override
	public List<SwingTag> checkSwingTagMark(
			String swingTagMark) {
		return swingTagMapper.checkSwingTagMark(swingTagMark);
	}

	@Override
	public ResponseJson deleteAdjuct(String id) {
		ResponseJson json  = new ResponseJson();
		if(!ChangnengUtil.isNull(id)){
			try {
				SwingTagAdjunct record = new SwingTagAdjunct();
				record.setId(id);
				record.setStatus(2);//置状态为脏数据
				record.setDelmark(0);//状态删除
				swingTagAdjunctMapper.updateByPrimaryKeySelective(record);
				json.success(HttpStatus.OK.toString(), SystemStatusCode.DELETE_SUCCESS.toString(), "删除成功", "删除附件成功",null);
				
			} catch (Exception e) {
				json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(),null);
			}
		}else{
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", "附件id为空！",null);
		}
		return json;
	}


}
