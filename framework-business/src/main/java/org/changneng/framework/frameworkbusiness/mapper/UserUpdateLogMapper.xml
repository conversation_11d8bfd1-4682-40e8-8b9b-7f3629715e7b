<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.UserUpdateLogMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.UserUpdateLog">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CURRENT_USER_ID" jdbcType="VARCHAR" property="currentUserId" />
    <result column="CURRENT_USER_NAME" jdbcType="VARCHAR" property="currentUserName" />
    <result column="CURRENT_AREA_CODE" jdbcType="VARCHAR" property="currentAreaCode" />
    <result column="CURRENT_AREA_NAME" jdbcType="VARCHAR" property="currentAreaName" />
    <result column="CURRENT_DEPARTMENT_ID" jdbcType="VARCHAR" property="currentDepartmentId" />
    <result column="CURRENT_DEPARTMENT_NAME" jdbcType="VARCHAR" property="currentDepartmentName" />
    <result column="UPDATE_COLUMN" jdbcType="VARCHAR" property="updateColumn" />
    <result column="UPDATE_COLUMN_NAME" jdbcType="VARCHAR" property="updateColumnName" />
    <result column="BEFOR_VALUE" jdbcType="VARCHAR" property="beforValue" />
    <result column="AFTER_VALUE" jdbcType="VARCHAR" property="afterValue" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="UPDATE_DEPARTMENT_ID" jdbcType="VARCHAR" property="updateDepartmentId" />
    <result column="UPDATE_DEPARTMENT_NAME" jdbcType="VARCHAR" property="updateDepartmentName" />
    <result column="UPDATE_AREA_CODE" jdbcType="VARCHAR" property="updateAreaCode" />
    <result column="UPDATE_AREA_NAME" jdbcType="VARCHAR" property="updateAreaName" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CURRENT_USER_ID, CURRENT_USER_NAME, CURRENT_AREA_CODE, CURRENT_AREA_NAME, CURRENT_DEPARTMENT_ID, 
    CURRENT_DEPARTMENT_NAME, UPDATE_COLUMN, UPDATE_COLUMN_NAME, BEFOR_VALUE, AFTER_VALUE, 
    UPDATE_TIME, UPDATE_USER_ID, UPDATE_USER_NAME, UPDATE_DEPARTMENT_ID, UPDATE_DEPARTMENT_NAME, 
    UPDATE_AREA_CODE, UPDATE_AREA_NAME
  </sql>
  
  
  <select id="queryUserLogList" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from USER_UPDATE_LOG
      <where>
      <if test="updateLog.currentUserName != null and updateLog.currentUserName != ''">
        AND CURRENT_USER_NAME LIKE '%'||#{updateLog.currentUserName}||'%'
      </if>
      <if test="updateLog.updateUserName != null and updateLog.updateUserName != ''">
        AND UPDATE_USER_NAME LIKE '%'||#{updateLog.updateUserName}||'%'
      </if>
      <if test="updateLog.areaCode != null and updateLog.areaCode !=''">
        AND CURRENT_AREA_CODE LIKE #{updateLog.areaCode}||'%'
      </if>
      <if test="updateLog.searchStartTime != null and updateLog.searchStartTime != ''">
        AND UPDATE_TIME&gt;= to_date(#{updateLog.searchStartTime},'yyyy-MM-dd hh24:mi:ss')
      </if>
      <if test="updateLog.searchEndTime != null and updateLog.searchEndTime !=''">
        AND UPDATE_TIME&lt;= to_date(#{updateLog.searchEndTime},'yyyy-MM-dd hh24:mi:ss')
      </if>
    </where>
        ORDER BY UPDATE_TIME DESC, ID asc
  </select>
  
  
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from USER_UPDATE_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from USER_UPDATE_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.UserUpdateLog">
    insert into USER_UPDATE_LOG (ID, CURRENT_USER_ID, CURRENT_USER_NAME, 
      CURRENT_AREA_CODE, CURRENT_AREA_NAME, CURRENT_DEPARTMENT_ID, 
      CURRENT_DEPARTMENT_NAME, UPDATE_COLUMN, UPDATE_COLUMN_NAME, 
      BEFOR_VALUE, AFTER_VALUE, UPDATE_TIME, 
      UPDATE_USER_ID, UPDATE_USER_NAME, UPDATE_DEPARTMENT_ID, 
      UPDATE_DEPARTMENT_NAME, UPDATE_AREA_CODE, UPDATE_AREA_NAME
      )
    values (#{id,jdbcType=VARCHAR}, #{currentUserId,jdbcType=VARCHAR}, #{currentUserName,jdbcType=VARCHAR}, 
      #{currentAreaCode,jdbcType=VARCHAR}, #{currentAreaName,jdbcType=VARCHAR}, #{currentDepartmentId,jdbcType=VARCHAR}, 
      #{currentDepartmentName,jdbcType=VARCHAR}, #{updateColumn,jdbcType=VARCHAR}, #{updateColumnName,jdbcType=VARCHAR}, 
      #{beforValue,jdbcType=VARCHAR}, #{afterValue,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{updateUserName,jdbcType=VARCHAR}, #{updateDepartmentId,jdbcType=VARCHAR}, 
      #{updateDepartmentName,jdbcType=VARCHAR}, #{updateAreaCode,jdbcType=VARCHAR}, #{updateAreaName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.UserUpdateLog">
      <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into USER_UPDATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="currentUserId != null">
        CURRENT_USER_ID,
      </if>
      <if test="currentUserName != null">
        CURRENT_USER_NAME,
      </if>
      <if test="currentAreaCode != null">
        CURRENT_AREA_CODE,
      </if>
      <if test="currentAreaName != null">
        CURRENT_AREA_NAME,
      </if>
      <if test="currentDepartmentId != null">
        CURRENT_DEPARTMENT_ID,
      </if>
      <if test="currentDepartmentName != null">
        CURRENT_DEPARTMENT_NAME,
      </if>
      <if test="updateColumn != null">
        UPDATE_COLUMN,
      </if>
      <if test="updateColumnName != null">
        UPDATE_COLUMN_NAME,
      </if>
      <if test="beforValue != null">
        BEFOR_VALUE,
      </if>
      <if test="afterValue != null">
        AFTER_VALUE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="updateUserName != null">
        UPDATE_USER_NAME,
      </if>
      <if test="updateDepartmentId != null">
        UPDATE_DEPARTMENT_ID,
      </if>
      <if test="updateDepartmentName != null">
        UPDATE_DEPARTMENT_NAME,
      </if>
      <if test="updateAreaCode != null">
        UPDATE_AREA_CODE,
      </if>
      <if test="updateAreaName != null">
        UPDATE_AREA_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="currentUserId != null">
        #{currentUserId,jdbcType=VARCHAR},
      </if>
      <if test="currentUserName != null">
        #{currentUserName,jdbcType=VARCHAR},
      </if>
      <if test="currentAreaCode != null">
        #{currentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="currentAreaName != null">
        #{currentAreaName,jdbcType=VARCHAR},
      </if>
      <if test="currentDepartmentId != null">
        #{currentDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="currentDepartmentName != null">
        #{currentDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateColumn != null">
        #{updateColumn,jdbcType=VARCHAR},
      </if>
      <if test="updateColumnName != null">
        #{updateColumnName,jdbcType=VARCHAR},
      </if>
      <if test="beforValue != null">
        #{beforValue,jdbcType=VARCHAR},
      </if>
      <if test="afterValue != null">
        #{afterValue,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentId != null">
        #{updateDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentName != null">
        #{updateDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateAreaCode != null">
        #{updateAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="updateAreaName != null">
        #{updateAreaName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.UserUpdateLog">
    update USER_UPDATE_LOG
    <set>
      <if test="currentUserId != null">
        CURRENT_USER_ID = #{currentUserId,jdbcType=VARCHAR},
      </if>
      <if test="currentUserName != null">
        CURRENT_USER_NAME = #{currentUserName,jdbcType=VARCHAR},
      </if>
      <if test="currentAreaCode != null">
        CURRENT_AREA_CODE = #{currentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="currentAreaName != null">
        CURRENT_AREA_NAME = #{currentAreaName,jdbcType=VARCHAR},
      </if>
      <if test="currentDepartmentId != null">
        CURRENT_DEPARTMENT_ID = #{currentDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="currentDepartmentName != null">
        CURRENT_DEPARTMENT_NAME = #{currentDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateColumn != null">
        UPDATE_COLUMN = #{updateColumn,jdbcType=VARCHAR},
      </if>
      <if test="updateColumnName != null">
        UPDATE_COLUMN_NAME = #{updateColumnName,jdbcType=VARCHAR},
      </if>
      <if test="beforValue != null">
        BEFOR_VALUE = #{beforValue,jdbcType=VARCHAR},
      </if>
      <if test="afterValue != null">
        AFTER_VALUE = #{afterValue,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentId != null">
        UPDATE_DEPARTMENT_ID = #{updateDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentName != null">
        UPDATE_DEPARTMENT_NAME = #{updateDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateAreaCode != null">
        UPDATE_AREA_CODE = #{updateAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="updateAreaName != null">
        UPDATE_AREA_NAME = #{updateAreaName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.UserUpdateLog">
    update USER_UPDATE_LOG
    set CURRENT_USER_ID = #{currentUserId,jdbcType=VARCHAR},
      CURRENT_USER_NAME = #{currentUserName,jdbcType=VARCHAR},
      CURRENT_AREA_CODE = #{currentAreaCode,jdbcType=VARCHAR},
      CURRENT_AREA_NAME = #{currentAreaName,jdbcType=VARCHAR},
      CURRENT_DEPARTMENT_ID = #{currentDepartmentId,jdbcType=VARCHAR},
      CURRENT_DEPARTMENT_NAME = #{currentDepartmentName,jdbcType=VARCHAR},
      UPDATE_COLUMN = #{updateColumn,jdbcType=VARCHAR},
      UPDATE_COLUMN_NAME = #{updateColumnName,jdbcType=VARCHAR},
      BEFOR_VALUE = #{beforValue,jdbcType=VARCHAR},
      AFTER_VALUE = #{afterValue,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR},
      UPDATE_DEPARTMENT_ID = #{updateDepartmentId,jdbcType=VARCHAR},
      UPDATE_DEPARTMENT_NAME = #{updateDepartmentName,jdbcType=VARCHAR},
      UPDATE_AREA_CODE = #{updateAreaCode,jdbcType=VARCHAR},
      UPDATE_AREA_NAME = #{updateAreaName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>