package org.changneng.framework.frameworkbusiness.entity.filecase;

import java.util.Date;

public class IllegalTypesTable {
    private String id;

    private Integer illegaCode;

    private String illegaName;

    private Integer isOpen;

    private String createUserId;

    private Date createDate;

    private Date lastUpdateDate;

    private String case1;

    private String case2;

    private String case3;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public Integer getIllegaCode() {
        return illegaCode;
    }

    public void setIllegaCode(Integer illegaCode) {
        this.illegaCode = illegaCode;
    }

    public String getIllegaName() {
        return illegaName;
    }

    public void setIllegaName(String illegaName) {
        this.illegaName = illegaName == null ? null : illegaName.trim();
    }

    public Integer getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getCase1() {
        return case1;
    }

    public void setCase1(String case1) {
        this.case1 = case1 == null ? null : case1.trim();
    }

    public String getCase2() {
        return case2;
    }

    public void setCase2(String case2) {
        this.case2 = case2 == null ? null : case2.trim();
    }

    public String getCase3() {
        return case3;
    }

    public void setCase3(String case3) {
        this.case3 = case3 == null ? null : case3.trim();
    }
}