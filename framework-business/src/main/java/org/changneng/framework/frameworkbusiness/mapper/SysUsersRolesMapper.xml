<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SysUsersRolesMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SysUsersRoles">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="ROLEID" jdbcType="VARCHAR" property="roleid" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, USERID, ROLEID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SYS_USERS_ROLES
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SYS_USERS_ROLES
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  
  <delete id="deleteByUserid" parameterType="java.lang.String">
  	 delete from SYS_USERS_ROLES
     where USERID = #{userid,jdbcType=VARCHAR}
  </delete>
  
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsersRoles">
    insert into SYS_USERS_ROLES (ID, USERID, ROLEID
      )
    values (#{id,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{roleid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsersRoles">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into SYS_USERS_ROLES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="roleid != null">
        ROLEID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="roleid != null">
        #{roleid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsersRoles">
    update SYS_USERS_ROLES
    <set>
      <if test="userid != null">
        USERID = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="roleid != null">
        ROLEID = #{roleid,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsersRoles">
    update SYS_USERS_ROLES
    set USERID = #{userid,jdbcType=VARCHAR},
      ROLEID = #{roleid,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>