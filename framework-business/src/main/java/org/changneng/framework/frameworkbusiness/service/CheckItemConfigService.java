package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;

/**
 * 环境监管一件事-检查项配置 Service接口
 * 简单的两级树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigService {

    /**
     * 获取完整的两级树形结构数据
     * 一级节点的PARENT_ID为"0"，二级节点的PARENT_ID为对应一级节点的ID
     *
     * @return 两级树形结构的检查项配置VO列表
     */
    List<CheckItemConfigTreeVO> getTreeStructure();

    /**
     * 根据层级标识码获取CHECK_ITEM_CONFIG表的真实主键ID
     * 层级标识码格式：父级索引_子级索引（如：0_0、0_1、2_1等）
     *
     * @param itemLevelCode 层级标识码
     * @return CHECK_ITEM_CONFIG表的真实主键ID，如果找不到则返回null
     */
    String getRealConfigIdByLevelCode(String itemLevelCode);

    /**
     * 批量根据层级标识码获取CHECK_ITEM_CONFIG表的真实主键ID
     *
     * @param itemLevelCodes 层级标识码列表
     * @return 层级标识码到真实ID的映射Map
     */
    java.util.Map<String, String> getRealConfigIdsByLevelCodes(java.util.List<String> itemLevelCodes);
}
