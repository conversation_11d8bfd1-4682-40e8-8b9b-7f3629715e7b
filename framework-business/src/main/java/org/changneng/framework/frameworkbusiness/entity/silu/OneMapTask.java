package org.changneng.framework.frameworkbusiness.entity.silu;

import java.math.BigDecimal;

/**
 * @ClassName OneMapTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/910:10
 * @Version 1.0
 **/
public class OneMapTask {
    private String name;
    /**
     * 总案件数
     */
    private Integer allCaseFinalNumber;

    /**
     * 总罚款金额（万元）
     */
    private BigDecimal allCaseFinalPrice;

    private String checkDate;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAllCaseFinalNumber() {
        return allCaseFinalNumber;
    }

    public void setAllCaseFinalNumber(Integer allCaseFinalNumber) {
        this.allCaseFinalNumber = allCaseFinalNumber;
    }

    public BigDecimal getAllCaseFinalPrice() {
        return allCaseFinalPrice;
    }

    public void setAllCaseFinalPrice(BigDecimal allCaseFinalPrice) {
        this.allCaseFinalPrice = allCaseFinalPrice;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }
}
