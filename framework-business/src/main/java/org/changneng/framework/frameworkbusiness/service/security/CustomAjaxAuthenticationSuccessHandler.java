package org.changneng.framework.frameworkbusiness.service.security;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;

import org.apache.commons.collections4.map.HashedMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.AreaMapper;
import org.changneng.framework.frameworkbusiness.dao.SysDepartmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysLogMapper;
import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.dao.UserLoginLogMapper;
import org.changneng.framework.frameworkbusiness.entity.Area;
import org.changneng.framework.frameworkbusiness.entity.FjAvatarUploadJson;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysLog;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.UserLoginLog;
import org.changneng.framework.frameworkbusiness.entity.WyhbJson;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.FileUtil;
import org.changneng.framework.frameworkcore.utils.HttpClientUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.RequestCustomerAddressUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.transaction.annotation.Transactional;

public class CustomAjaxAuthenticationSuccessHandler extends  SavedRequestAwareAuthenticationSuccessHandler{
	
	    private static Logger logger = LogManager.getLogger(CustomAjaxAuthenticationSuccessHandler.class.getName());  
	    
	    @Autowired
	    private SysLogMapper sysLogMapper;
	    
	    @Autowired
	    private SysUsersMapper sysUsersMapper;
	    
	    @Autowired
	    private UserLoginLogMapper userLoginLogMapper;
	    
	    @Autowired
	    private SysDepartmentMapper sysDepartmentMapper;
	    @Autowired
	    private AreaMapper areaMapper;
	    
	    @Autowired
	    private  SysFilesMapper sysFilesMapper;
	    private static String  snsServer ="";
	    
	    @Autowired
	    private StringRedisTemplate redisTemplate;
	    static{
			snsServer=PropertiesHandlerUtil.getValue("snsServer","sns_server");
		}
	    @Override  
	    public void onAuthenticationSuccess(HttpServletRequest request,  
	            HttpServletResponse response, Authentication authentication)  
	            throws ServletException, IOException { 
	    	//登录成功，则把redis中用户失败的记录清除
	    	String username = (String) request.getSession().getAttribute("SPRING_SECURITY_LAST_USERNAME");
	    	redisTemplate.delete(username+"shibaicishu");
	    	logger.info("ajax login success");
	    	insertSysLog(request,authentication);
	    	SysUsers sysUsers = (SysUsers)authentication.getPrincipal();
	    	Area ar= areaMapper.queryAreaByAreacode(sysUsers.getBelongAreaId());
	    	String password = request.getParameter("password");
	    	System.out.println("----------------"+password+"----------------------");
	    	if(sysUsers.getIsSns() !=1){
	    		Map<String,String> httpMap=new HashedMap<String,String>();
				httpMap.put("uid",sysUsers.getId());
				String temp = sysUsers.getUsername();
				httpMap.put("user_name",temp);
				httpMap.put("real_name",sysUsers.getLoginname());
				httpMap.put("email", "");
				httpMap.put("mobile",sysUsers.getPhone());
				httpMap.put("sex", null);
				httpMap.put("password",password);
				httpMap.put("province", ar.getProvince());
				httpMap.put("city", ar.getCity());
				httpMap.put("avatar_file", sysUsers.getDefaultAvatarUrl());
				httpMap.put("fj_user_id", sysUsers.getId());
	    		try {
					String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fjxm_register_process/","utf-8",httpMap);
					WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
					if(wj.getErrno()!=1){
						throw new BusinessException("同步我要环保密码失败");
					}else{
						//上传选中头像
						if(!ChangnengUtil.isNull(sysUsers.getAvatarUrl() ) && sysUsers.getIsDefaultAvatar()!=1){
							SysFiles sf=new SysFiles();
							if(sysUsers.getFileId()!=null&&!"".equals(sysUsers.getFileId())){
								 sf = sysFilesMapper.selectByPrimaryKey(sysUsers.getFileId());
							}else{
								sf.setFileName(sysUsers.getAvatarUrl());
							}
			    			byte[] bye=FileUtil.getFileBytesByFDFSUtils(sysUsers.getAvatarUrl(),sf.getFileName());
							Map<String, String> map = new HashMap<String, String>();  
							map.put("uid",sysUsers.getId());  
							String res=HttpClientUtil.getInstance().httpPostUpload(snsServer+"/?/account/ajax/fj_avatar_upload/","aws_upload_file",sf.getFileName(),bye, map);
							FjAvatarUploadJson fau=JacksonUtils.toObject(res,FjAvatarUploadJson.class);
							if(!fau.getSuccess()){
								throw new BusinessException("同步我要环保头像失败");
							}
			    		}else if(!ChangnengUtil.isNull(sysUsers.getDefaultAvatarUrl()) && sysUsers.getIsDefaultAvatar() ==1){
			    			//上传默认头像	
			    			String defaultAvatarUrl = sysUsers.getDefaultAvatarUrl();
			    				  FileOutputStream fos = null;  //w文件包装输出流
			    			        ByteArrayOutputStream bos =null;
			    			        try {
			    			        	//获取用户的当前工作目录
			    			        	String property = System.getProperty("user.dir") ;
			    			        	System.out.println("目录="+property);
			    			        	property =property.replace("bin","");
			    			            //File file = new File(property+"\\src\\main\\webapp\\static\\img\\headerImg\\"+"headerImg"+avatarStatus+".png");  
			    			        	File file = new File(property+"webapps/ROOT/static/img/"+defaultAvatarUrl);  
			    			            FileInputStream fis = new FileInputStream(file);  
			    			             bos = new ByteArrayOutputStream(1000);  
			    			            byte[] b = new byte[1000];  
			    			            int n;  
			    			            while ((n = fis.read(b)) != -1) {  
			    			                bos.write(b, 0, n);  
			    			            }
			    			            byte[]  bosaa =bos.toByteArray();  
			    			            Map<String, String> map = new HashMap<String, String>();  
			    						map.put("uid",sysUsers.getId());  
			    						String res=HttpClientUtil.getInstance().httpPostUpload(snsServer+"/?/account/ajax/fj_avatar_upload/","aws_upload_file","aa.png",bosaa, map);
			    						FjAvatarUploadJson fau=JacksonUtils.toObject(res,FjAvatarUploadJson.class);
			    						if(!fau.getSuccess()){
			    							throw new BusinessException("同步我要环保头像失败");
			    						}else{
			    							System.err.println("同步我要环保头像成功！");
			    						}
			    			        } catch (Exception e) {  
			    			            e.printStackTrace();  
			    			        } finally {  
			    			            if (bos != null) {  
			    			                try {  
			    			                    bos.close();  //关闭资源
			    			                } catch (IOException e1) {  
			    			                    e1.printStackTrace();  
			    			                }  
			    			            }  
			    			            if (fos != null) {  
			    			                try {  
			    			                    fos.close();  //关闭资源
			    			                } catch (IOException e1) {  
			    			                    e1.printStackTrace();  
			    			                }  
			    			            }  
			    			        }  
			    		}
						SysUsers record = new SysUsers();
						record.setId(sysUsers.getId());
						record.setIsSns(1);
						sysUsers.setIsSns(1);
						sysUsersMapper.updateByPrimaryKeySelective(record );
					}
	    		} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
	    	}

    		Map<String,Object> returnUserinfo=new HashMap<String,Object>();
    		returnUserinfo.put("is_sns",sysUsers.getIsSns());
    		//设置头像路径
    		HttpSession session = request.getSession();  
			session.setAttribute("avarUrl",sysUsers.getAvatarUrl());
	    	try{
	    		updateLoginState(authentication);
	    	} catch (Exception e) {
				logger.error("web入口用户登录修改表信息失败",e);
			}
	    	response.setHeader("Content-Type", "application/json;charset=UTF-8");
		    response.getWriter().write(JacksonUtils.toJsonString(new ResponseJson().success("200","001","登录成功","ajax请求登录成功",returnUserinfo)));
	    	response.getWriter().close();
	    }  
	  
	    private void insertSysLog(HttpServletRequest request,  Authentication authentication){
	    	SysUsers sysUsers = (SysUsers)authentication.getPrincipal();
	    	String url = request.getScheme() //当前链接使用的协议
				    +"://" + request.getServerName()//服务器地址 
				    + ":" + request.getServerPort() //端口号 
				    + request.getContextPath() //应用名称，如果应用名称为
				    + request.getServletPath();//请求的相对url
	    	String userAgent = request.getHeader("user-agent");
	    	SysLog sl=new SysLog();
	    	sl.setLoginid(sysUsers.getUsername());
	    	sl.setUsername(sysUsers.getLoginname());
	    	sl.setIp(RequestCustomerAddressUtil.getIpAddress(request));
	    	sl.setDbtype("登录");
	    	sl.setResult("success");
	    	sl.setBusinesstype("用户登录");
	    	sl.setOptdate(new Date());
	    	sl.setMethodname("onAuthenticationSuccess");
	    	//思路日志
	    	sl.setAppCode("fjjxhzfpt");
	    	sl.setOpCode("LOGIN");
	    	sl.setUrl(url);
	    	sl.setUserAgent(userAgent);
	    	sysLogMapper.insertSelective(sl);
	    	
	    	//修改用户最后一次登录的时间
	    	SysUsers sysUsersDate = (SysUsers)authentication.getPrincipal();
	    	sysUsersDate.setLastLogin(new Date());
	    	sysUsersDate.setLoginIp(RequestCustomerAddressUtil.getIpAddress(request));
	    	sysUsersMapper.updateByPrimaryKeySelectiveDateAndIp(sysUsersDate);
	    }
	    
	    /**
	     * 用户登录更改登录状态-web入口
	     * @param authentication
	     */
	    @Transactional(rollbackFor=Exception.class)
	    private void updateLoginState(Authentication authentication) throws Exception{
	    	//web入口
	    	SysUsers sysUsers = (SysUsers)authentication.getPrincipal();
	    	SysUsers sysUser = sysUsersMapper.selectByPrimaryKey(sysUsers.getId());
	    	String isLoginToday = sysUser.getIsLoginToday();
			if (isLoginToday == null || "0".equals(isLoginToday)) {
				sysUser.setIsLoginToday("1");
				UserLoginLog userLoginLog = new UserLoginLog();
		    	userLoginLog.setLoginUserId(sysUser.getId());
		    	userLoginLog.setLoginType(sysUser.getIsLoginToday());
		    	userLoginLog.setBelongAreaCode(sysUser.getBelongAreaId());
		    	userLoginLog.setBelongAreaName(sysUser.getBelongAreaName());
		    	userLoginLog.setBelongDeptId(sysUser.getBelongDepartmentId());
		    	userLoginLog.setBelongDeptName(sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId()).getDepartmentName());
		    	userLoginLogMapper.insertSelective(userLoginLog);
			} else {
				if ("1".equals(isLoginToday) || "3".equals(isLoginToday)) {
					//如果为1或者3，表示今日已经从web端登陆过，直接返回
					return;
				} else {
					sysUsers.setIsLoginToday("3");
					//根据userID和今日日期更新userloginlog表的状态为3
					userLoginLogMapper.updateByUserIdAndDate(sysUser.getId(),DateUtil.getDateTime("yyyy-MM-dd"));
				}
			}
	    	sysUsersMapper.updateByPrimaryKeySelective(sysUser);
	    	
	    	
	    }
}		
