package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.ActionlRequireFiless;

public interface ActionlRequireFilessMapper {
    //int insert(ActionlRequireFiless record);
 
    void insertSelective(ActionlRequireFiless record);
    
    
    List<ActionlRequireFiless> selectActionRequireFilessByActionId(String actionId);
    
    void deleteActionlRequireFilessById(String id);
    
    ActionlRequireFiless selectActionRequireById(String id);
    
    void updateFileInfo(ActionlRequireFiless actionlRequireFiless);
    
    Boolean selectActionRequireByFileId(String fileId);
    
}