package org.changneng.framework.frameworkbusiness.dao;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.APollutsourceCode;


public interface APollutsourceCodeMapper {
    int insert(APollutsourceCode record);

    int insertSelective(APollutsourceCode record);
    
    APollutsourceCode getAPAPollutsourceCodeByOrgCode(@Param("orgCode")String orgCode)throws Exception;
    
    int updateSelectiveByOrgCode(APollutsourceCode record)throws Exception;
}