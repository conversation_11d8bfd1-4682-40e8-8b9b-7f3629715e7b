package org.changneng.framework.frameworkbusiness.dao.swingtag;

import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagAdjunctItem;


public interface SwingTagAdjunctItemMapper {
    int deleteByPrimaryKey(String id);

    int insert(SwingTagAdjunctItem record);

    int insertSelective(SwingTagAdjunctItem record);

    SwingTagAdjunctItem selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(SwingTagAdjunctItem record);

    int updateByPrimaryKey(SwingTagAdjunctItem record);
}