package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.SysHelpDoc;

public interface SysHelpDocMapper {
    int deleteByPrimaryKey(String id);

    int insert(SysHelpDoc record);

    int insertSelective(SysHelpDoc record);

    SysHelpDoc selectByPrimaryKey(String id);
    
    int updateByPrimaryKeySelective(SysHelpDoc record);

    int updateByPrimaryKey(SysHelpDoc record);
    
    List<SysHelpDoc> querySysHelpDocList();
    
    void sysHelpDocCount(String id);
}