package org.changneng.framework.frameworkbusiness.service.filecase;

import org.changneng.framework.frameworkbusiness.entity.filecase.AdministrativeDetentionWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.AtvSanctionCaseWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.DecisionNumberCheckout;
import org.changneng.framework.frameworkbusiness.entity.filecase.PollutionCrimeWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.LimitStopProductWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDayWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.SequestrationInfoWithBLOBs;


public interface DecisionNumberCheckoutService {
	/**
	 * 简易行政处罚决定书文号处理
	 * @param easyAtvSanction
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout administrativeEasyCheckoutService(AtvSanctionCaseWithBLOBs easyAtvSanction,String caseId);
	/**
	 * 一般行政处罚决定书文号处理
	 * @param easyAtvSanction
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout generalCaseCheckoutService(AtvSanctionCaseWithBLOBs easyAtvSanction,String caseId);

	
	/**
	 * 行政拘留决定书文号处理
	 * @param easyAtvSanction
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout detentionCheckoutService(AdministrativeDetentionWithBLOBs detentionInfo,String caseId);
	
	/**
	 * 环境污染犯罪决定书文号处理
	 * @param easyAtvSanction
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout pollutionInfoCheckoutService(PollutionCrimeWithBLOBs pollutionInfo,String caseId);

	/**
	 * 按日计罚决定书文号处理
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout penaltyDayCheckoutService(PenaltyDayWithBLOBs penalty,String caseId);
	/**
	 * 查封扣押决定书文号处理
	 * @param sequestrationInfo
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout sequestrationCheckoutService(SequestrationInfoWithBLOBs sequestrationInfo,String caseId);
	/**
	 * 申请法院强制执行决定书文号处理
	 * @param applyInfo
	 * @param caseId
	 * @return
	 */
	public DecisionNumberCheckout applyForceCheckoutService(ApplyForceWithBLOBs applyInfo,String caseId);
	
	/**
	 * 限产停产决定书文号处理
	 * @param limitStopProduct
	 * @param caseId  大案件id
	 * @param actionType  实施措施类型 0为限制生产 1为停产整治
	 * @return
	 */
	public DecisionNumberCheckout limitStopProductCheckoutService(LimitStopProductWithBLOBs limitStopProduct,String caseId,Integer actionType);
	
}
