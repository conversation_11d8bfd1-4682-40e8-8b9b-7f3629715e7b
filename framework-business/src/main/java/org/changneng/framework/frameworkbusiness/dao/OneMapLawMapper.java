package org.changneng.framework.frameworkbusiness.dao;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.SiLuSearch;
import org.changneng.framework.frameworkbusiness.entity.oneMap.LawGeneralSituation;
import org.changneng.framework.frameworkbusiness.entity.oneMap.OneMapHotInfo;
import org.changneng.framework.frameworkbusiness.entity.oneMap.OneMapLawTable;
import org.changneng.framework.frameworkbusiness.entity.oneMap.OneMapSpecialAction;

import java.util.List;

/**
 * @ClassName OneMapLawMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/2/1221:47
 * @Version 1.0
 **/
public interface OneMapLawMapper {
    LawGeneralSituation getLawGeneralSituation(SiLuSearch search);//    //概况

    //专项行动
    List<OneMapSpecialAction> getOneMapSpecialList(SiLuSearch search);


    //获取地市统计数据
    List<OneMapLawTable> getOneMapLawTable(SiLuSearch search);

    //获取热力图执法部分
    List<OneMapHotInfo> getOneMapHotInfo(SiLuSearch search);
}
