package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.service.CaseSendService;
import org.changneng.framework.frameworkbusiness.service.filecase.CaseSendHandleService;
import org.changneng.framework.frameworkbusiness.service.filecase.SendApplyForce;
import org.changneng.framework.frameworkbusiness.service.filecase.SendFirstSupportingMeasuresService;
import org.changneng.framework.frameworkbusiness.service.filecase.impl.CaseSendHandleServiceImpl;
import org.changneng.framework.frameworkbusiness.utils.ParseCronUtils;
import org.changneng.framework.frameworkcore.utils.DockingAreaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CaseSendServiceImpl implements CaseSendService {

	
	private static Logger logger = LogManager.getLogger(CaseSendServiceImpl.class.getName());
	@Autowired
	private CaseSendHandleService caseSendHandleService;
	@Autowired
	private SendFirstSupportingMeasuresService sendFirstSupportingMeasuresService;

	/**
	 * 申请法院强制执行 、处理类  &&  按日计罚 、处理类
	 */
	@Autowired
	private SendApplyForce applyForce;
	
	@Override
	public void caseSendFunction(String str) {
		try {
			DockingAreaUtil dockingUtil = new DockingAreaUtil();
			String accessToken = dockingUtil.getSendUserToken();
			boolean status = false;
			if("1".equals(str)){//参数str为“1”，每天都对接所有，str为其他，则只有每月的1号，3号对接所有，其他时间对接前一天失败的
				status = true;
			}else{
				String cron ="0 30 1 1 * ?";//正确的cron表达式，每月的一号执行
				boolean status1 = ParseCronUtils.isExecute(cron); 
				String cron2 = "0 30 1 3 * ?";//每月的三号执行
				boolean status2 = ParseCronUtils.isExecute(cron2);
				if(status1 || status2){
					//如果是一号或三号，则执行
					status = true;
				}
			}
			/**
			 * 0 基本信息、1简易行政处罚、2一般行政处罚、3 建设项目（不在发送）、4查封扣押、 5限产停产、6行政拘留、7环境污染犯罪、8申请法院强制执行、9按日计罚
			 */
			/**
			 *  0：发送基本信息,并获取失败的基本信息id集合
			 */
			Set<String> errorList  =  caseSendHandleService.SendBaseInfos(accessToken,status);
			
			/**
			 * 3建设项目包含在其中 独立发送---废弃2018-03-20
			 */
			//caseSendHandleService.SendConstructProjects(accessToken,errorList,status);
			
			/**
			 *  1简易行政处罚、2一般行政处罚、如果基本信息发送失败，就不用再发送该条信息了，下边都一样；建设项目包含在其中
			 */
			caseSendHandleService.SendAtvSanctionInfos(accessToken, errorList,status);

			/**
			 *  4查封扣押、
			 */	
			caseSendHandleService.SendSequestrationInfo(accessToken, errorList,status);
			 
			/**
			 * 5限产停产、
			 */
			sendFirstSupportingMeasuresService.sendLimitStopProductServiceInfo(accessToken, errorList,status);
			/**
			 *  6行政拘留、
			 */
			sendFirstSupportingMeasuresService.sendAdminDetentionServiceInfos(accessToken, errorList,status);
			/**
			 *  7环境污染犯罪、
			 */
			applyForce.sendPollutionCrimeFunction(accessToken, errorList,status);
			/**
			 *  8申请法院强制执行、
			 */
			applyForce.sendApplyForceFunction(accessToken, errorList,status);
			/**
			 *  9按日计罚
			 */
			applyForce.sendPenaltyDayFunction(accessToken, errorList,status);	

		} catch (Exception e) {
			logger.error("这个地方是不应该出现错误的，这是caseSendFunction(String str) 入口处，前端传入的参数是："+str+",报错原因："+e.getMessage());
		}

	}

}
