package org.changneng.framework.frameworkbusiness.dao.filecase;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.filecase.OrderOnekeyItem;

public interface OrderOnekeyItemMapper {
    int deleteByPrimaryKey(String id);

    int insert(OrderOnekeyItem record);

    int insertSelective(OrderOnekeyItem record);

    OrderOnekeyItem selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(OrderOnekeyItem record);

    int updateByPrimaryKeyWithBLOBs(OrderOnekeyItem record);

    int updateByPrimaryKey(OrderOnekeyItem record);
    /**
     * 根据objectId查询idlist
     * @param id
     * @return
     */
	List<String> selectByObjId(String objectId);

	/**
	 * 根据objectId将数据设为删除状态
	 * @param id
	 */
	void deleteByObjId(String objectId);
}