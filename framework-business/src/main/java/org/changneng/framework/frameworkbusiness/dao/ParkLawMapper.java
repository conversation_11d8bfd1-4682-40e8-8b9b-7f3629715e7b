package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.ParkLaw;
import org.changneng.framework.frameworkbusiness.entity.ParkSearchBean;
import org.changneng.framework.frameworkcore.utils.ZfdxQueryExample;

public interface ParkLawMapper {
    int deleteByPrimaryKey(String id);

    int insert(ParkLaw record);

    int insertSelective(ParkLaw record);

    ParkLaw selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ParkLaw record);

    int updateByPrimaryKey(ParkLaw record);
    /**
     * 查询执法对象信息
     * @param seachBean
     * @return
     */
    List<LawEnforceObjectWithBLOBs> selectLawEnforceObject(@Param("seachBean")ZfdxQueryExample seachBean);
    
    /**
     * 批量删除
     * @param ids
     * @param parkId
     * @return
     */
    int batchDeleteParkLawInfo(@Param("ids")String ids,@Param("parkId")String parkId);
    
    int selectByParkId(String parkId);
    /**
     * 批量新增
     * @param record
     * @return
     */
    void batchInsert(List<ParkLaw> list);
    //根据园区id查询监管对象总数；
    int selectCountById(@Param("id")String id);
    //根据园区id查询监管对象总数；
    int selectCountByParam(@Param("startTime")String startTime,@Param("endTime")String endTime,@Param("id")String id);
  //根据parkID查询使用关联的对象ID
    List<ParkLaw> selectDataByParkId(@Param("parkId")String parkId);

    /**
     * 通过园区id删除
     * @param parkId
     * @return
     */
    int deleteByParkId(String parkId);
    /**
     * 通过执法对象id删除  用于执法对象修改区划和删除
     * @param parkId
     * @return
     */
    int deleteByLawId(String lawId);

}