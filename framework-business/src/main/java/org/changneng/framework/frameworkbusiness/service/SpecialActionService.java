package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface SpecialActionService {
	
	List<SceneSysSpecialModel> selectSpecialActionListByName(String specialName);
	
	/**
	 * 添加一条专项行动信息
	 * @param specialAction 
	 */
	ResponseJson insertSelective(SceneSysSpecialModel record,List<SysFiles> list) throws Exception ;
	
	/**
	 * 根据id查询一条专项行动信息
	 */
	public SceneSysSpecialModel loadSpecialActionByIdService(String id);
	
	
	/**
	 * 根据id删除一条专项行动信息
	 * @param id
	 */
	public int deleteByPrimaryKeyService(String id);
	
	/**
	 * 根据主键修改一条专项行动信息
	 * @param record
	 * @return
	 */
	ResponseJson updateByPrimaryKeySelectiveService(SceneSysSpecialModel record,List<SysFiles> list) throws Exception;
	
	/**
	 * 分页显示根据条件筛选专项行动列表
	 * @param record
	 * @return
	 */
	PageBean<SceneSysSpecialModel> selectSpecialActionByTerm1(SceneSysSpecialModel record1,Integer pNum,Integer pageSize11);


	/**
	 * 根据专项行动的名称 查询专项行动id
	 * @param string
	 * @return
	 */
	SceneSysSpecialModel selectSpecialActionIdByName(String specialActionName);
	/**
	 * 根据专项行动id查询专项行动
	 * @param id
	 * @return
	 * <AUTHOR>
	 */
	SceneSysSpecialModel selectSpecialActionById(String id);
	/**
	 * 结束时修改专项状态
	 * @param specialAction
	 * <AUTHOR>
	 */
	void UpdateSpecialStatusBySelective(SceneSysSpecialModel specialAction);
	
	//SpecialAction selectSpecialActionByName(String name);

}
