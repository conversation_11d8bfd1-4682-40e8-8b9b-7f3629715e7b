package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;
import java.util.List;

public class AdministrativeReview {
    private String id;

    private String name;

    private String year;

    private String adminDepartCode;

    private String adminDepartName;

    private String byAdminDepartCode;

    private String byAdminDepartName;

    private Integer adminBehavior;

    private String appeal;

    private String decisionNumber;

    private Integer reviewResults;

    private Date createDate;

    private Date lastedUpdateDate;

    private String createUserId;

    private String createUserName;
    
    private String attachId;
    
    private String attachType;
    
    private String adminDepartType;
    
    private Integer isDel;
    
    private SysFiles sysFiles;
    

	public SysFiles getSysFiles() {
		return sysFiles;
	}

	public void setSysFiles(SysFiles sysFiles) {
		this.sysFiles = sysFiles;
	}

	public Integer getIsDel() {
		return isDel;
	}

	public void setIsDel(Integer isDel) {
		this.isDel = isDel;
	}

	public String getAdminDepartType() {
		return adminDepartType;
	}

	public void setAdminDepartType(String adminDepartType) {
		this.adminDepartType = adminDepartType;
	}

	public String getAttachType() {
		return attachType;
	}

	public void setAttachType(String attachType) {
		this.attachType = attachType;
	}

	public String getAttachId() {
		return attachId;
	}

	public void setAttachId(String attachId) {
		this.attachId = attachId;
	}

	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year == null ? null : year.trim();
    }

    public String getAdminDepartCode() {
        return adminDepartCode;
    }

    public void setAdminDepartCode(String adminDepartCode) {
        this.adminDepartCode = adminDepartCode == null ? null : adminDepartCode.trim();
    }

    
    public String getAdminDepartName() {
		return adminDepartName;
	}

	public void setAdminDepartName(String adminDepartName) {
		this.adminDepartName = adminDepartName;
	}

	public String getByAdminDepartCode() {
        return byAdminDepartCode;
    }

    public void setByAdminDepartCode(String byAdminDepartCode) {
        this.byAdminDepartCode = byAdminDepartCode == null ? null : byAdminDepartCode.trim();
    }

    public String getByAdminDepartName() {
        return byAdminDepartName;
    }

    public void setByAdminDepartName(String byAdminDepartName) {
        this.byAdminDepartName = byAdminDepartName == null ? null : byAdminDepartName.trim();
    }

    public Integer getAdminBehavior() {
        return adminBehavior;
    }

    public void setAdminBehavior(Integer adminBehavior) {
        this.adminBehavior = adminBehavior;
    }

    public String getAppeal() {
		return appeal;
	}

	public void setAppeal(String appeal) {
		this.appeal = appeal;
	}

	public String getDecisionNumber() {
        return decisionNumber;
    }

    public void setDecisionNumber(String decisionNumber) {
        this.decisionNumber = decisionNumber == null ? null : decisionNumber.trim();
    }

    public Integer getReviewResults() {
        return reviewResults;
    }

    public void setReviewResults(Integer reviewResults) {
        this.reviewResults = reviewResults;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getLastedUpdateDate() {
        return lastedUpdateDate;
    }

    public void setLastedUpdateDate(Date lastedUpdateDate) {
        this.lastedUpdateDate = lastedUpdateDate;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName == null ? null : createUserName.trim();
    }
}