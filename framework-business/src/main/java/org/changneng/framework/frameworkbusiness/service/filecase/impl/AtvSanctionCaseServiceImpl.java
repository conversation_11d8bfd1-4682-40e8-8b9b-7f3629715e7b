package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.CaseManageMaxNumberMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AttachmentMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AtvFilesInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AtvRecordsContentMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AtvSanctionCaseMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CaseBaseInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CaseLawEnforcementMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CaseStateMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.PenaltyDayMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.caseManageTableMapper;
import org.changneng.framework.frameworkbusiness.entity.CaseManageMaxNumber;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.AtvSanctionCaseWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseState;
import org.changneng.framework.frameworkbusiness.entity.filecase.GenerateBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDay;
import org.changneng.framework.frameworkbusiness.entity.filecase.WenHaoSearchResult;
import org.changneng.framework.frameworkbusiness.entity.filecase.caseManageTable;
import org.changneng.framework.frameworkbusiness.produceNumber.ProduceNumber;
import org.changneng.framework.frameworkbusiness.service.OperatorWenHao;
import org.changneng.framework.frameworkbusiness.service.filecase.AttachmentService;
import org.changneng.framework.frameworkbusiness.service.filecase.AtvSanctionCaseService;
import org.changneng.framework.frameworkbusiness.service.filecase.BaseInfoCaseService;
import org.changneng.framework.frameworkbusiness.service.filecase.CaseStateService;
import org.changneng.framework.frameworkbusiness.service.filecase.GetUserInfoService;
import org.changneng.framework.frameworkbusiness.utils.SubmitCaseUtil;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.Const;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AtvSanctionCaseServiceImpl implements AtvSanctionCaseService {

	private static Logger logger = LogManager.getLogger(AtvSanctionCaseServiceImpl.class.getName());

	@Autowired
	private AtvSanctionCaseMapper atvSanctionCaseMapper;
	
	@Autowired
	private AtvRecordsContentMapper atvRecordsContentMapper;
	
	@Autowired
	private AtvFilesInfoMapper atvFilesInfoMapper;
	
	@Autowired
	private ProduceNumber produceNumber;
	
	@Autowired
	private CaseStateMapper caseStateMapper;
	
	@Autowired
	private AttachmentMapper attachmentMapper;
	
	@Autowired
	private AttachmentService attachmentService;
	
	@Autowired
	private GetUserInfoService getUserInfoService;
	
	@Autowired
	private CaseStateService caseStateService;
	
	@Autowired
	private CaseBaseInfoMapper caseBaseInfoMapper;
	
	@Autowired
	private caseManageTableMapper  caseManageTableMapper;
	
	@Autowired
	private BaseInfoCaseService baseInfoCaseService;
	
	@Autowired
	private PenaltyDayMapper penaltyDayMapper;
	@Autowired
	private CaseManageMaxNumberMapper caseManageMaxNumberMapper;
	
	@Autowired
	private OperatorWenHao operatorWenHao;
	
	@Autowired
	private CaseLawEnforcementMapper cleMapper; 
	
	@Override
	public AtvSanctionCaseWithBLOBs getEasyAtvSanctionCaseByCaseId(String caseId) {
		AtvSanctionCaseWithBLOBs easyBloBs =  atvSanctionCaseMapper.getEasyAtvSanctionCaseByCaseId(caseId);
		return easyBloBs;
	}
	
	@Override
	@Transactional(rollbackFor=Exception.class)
	public void submit(String id, String saveType, Integer caseType) throws Exception {
		try {
			if (id == null || "".equals(id) || saveType == null || "".equals(saveType)
					|| caseType == null) {
				throw new Exception("行政处罚部分提交参数错误！");
			}
			AtvSanctionCaseWithBLOBs atv = atvSanctionCaseMapper.selectByPrimaryKey(id);
			if("1".equals(saveType)&& caseType == 1){
				//简易上半部分提交
				atv.setUpState(3);
				atv.setDownState(0);
				atv.setUpUpdateDate(new Date());
			} else if("2".equals(saveType) && caseType == 1){
				//简易下半部分提交
				atv.setDownState(3);
				atv.setDoweUpdateDate(new Date());
				
				// 修改表头状态
				CaseState record = new  CaseState();
				record.setCaseId(atv.getCaseId());
				record.setSmrProgramState(2);
				caseStateMapper.updateCaseStateByCaseIdForCloumn(record);
			} else if ("0".equals(saveType) && caseType == 2) {
				//一般行政处罚立案环节--没有提交
			} else if ("1".equals(saveType) && caseType == 2) {
				//一般行政处罚销案环节--没有提交
			} else if ("2".equals(saveType) && caseType == 2) {
				//一般行政处罚-行政处罚环节
				atv.setUpState(3);
				atv.setDownState(0);
				atv.setUpUpdateDate(new Date());
			} else if ("3".equals(saveType) && caseType == 2){
				//一般行政处罚结案环节
				atv.setDownState(3);
				atv.setDoweUpdateDate(new Date());
				
				// 修改表头状态
				CaseState record = new  CaseState();
				record.setCaseId(atv.getCaseId());
				record.setSmrProgramState(2);
				caseStateMapper.updateCaseStateByCaseIdForCloumn(record);
			}
			atvSanctionCaseMapper.updateByPrimaryKeySelective(atv);
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("系统错误，提交失败！");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public int insertOrUpdateEasyAtv(AtvSanctionCaseWithBLOBs easyblobs,SysUsers sysUsers,String itemIds) throws Exception {
		try{
			int countNum = 0; // 简易程序表 最后影响条数
			//维护对接逻辑 id 存在 则修改，这里要区分是上半部分还是下半部分
			String tableName = "atv_sanction_case";
			Integer dockSusStateForTable = caseStateService.getDockSusStateForTable(tableName,easyblobs.getId());
			Integer dockSusStateForTable2 = caseStateService.getDockSusStateForTable2(tableName, easyblobs.getId());
			String caseId = easyblobs.getCaseId();
			CaseState record = new  CaseState();
			// 查询 
			//SysUsers sysUsers = getUserInfoService.getUserInfo(easyblobs.getCaseId());
			if(!ChangnengUtil.isNull(easyblobs.getId())){ 
				easyblobs.setUpUpdateDate(new Date());
				if("1".equals(easyblobs.getIsUpOrDown())){
					easyblobs.setUpUpdateDate(new Date());
					easyblobs.setUpdateUserId(sysUsers.getId());
					easyblobs.setInfoStateUp(1);
					easyblobs.setUpdateUserName(sysUsers.getLoginname());
					if(dockSusStateForTable==1 || dockSusStateForTable2==2){
						easyblobs.setDockingType(2);
					}
					//如果暂存状态为空，则修改立案信息状态
					if((easyblobs.getUpState()==null || easyblobs.getUpState()<=1) && ChangnengUtil.isNull(easyblobs.getIsTmp())){
						easyblobs.setUpState(1);
					}
					if(!(easyblobs.getPunishTypeCode()).contains("2")){
						easyblobs.setPenaltyAmount(new BigDecimal("0"));
					}
					easyblobs.setLastedUpdateTime(new Date());
					// 唯一文号年份字段维护
					if (!ChangnengUtil.isNull(easyblobs.getOnlineDecisionNumber())) {
						if (easyblobs.getOnlineDecisionNumber().indexOf("〔") > -1) {
							int begIndex = easyblobs.getOnlineDecisionNumber().indexOf("〔") + 1;
							int endIndex = begIndex + 4;
							easyblobs.setOnlineDecisionYear(
									Integer.parseInt(easyblobs.getOnlineDecisionNumber().substring(begIndex, endIndex)));
						}
					}
					countNum  = atvSanctionCaseMapper.updateByPrimaryKeySelective(easyblobs);
					if(easyblobs.getLawUserList()!=null && !easyblobs.getLawUserList().isEmpty() && easyblobs.getLawUserList().size()>0){
						// 修改多出一步，先删除在插入
						cleMapper.deleteAllByModelerType(2+"", easyblobs.getId());
						// * @param modelerType 1：基本信息，2：简易行政处罚，3：一般行政处罚，4：行政命令，5：查封扣押，6：限产停产，7：行政拘留，  8：环境污染犯罪，9：申请法院强制执行，10：其他移送，11：按日计罚
						cleMapper.insertBatchCaseLawEnforcement(easyblobs.getLawUserList(),2+"", easyblobs.getId());
					}
					
				}else if("2".equals(easyblobs.getIsUpOrDown())){
					easyblobs.setUpdateUserId(sysUsers.getId());
					easyblobs.setUpdateUserName(sysUsers.getLoginname());
					if(easyblobs.getDownState()!=null && easyblobs.getDownState()==0){
						if(ChangnengUtil.isNull(easyblobs.getIsTmp())){
							//如果不是暂存，则修改结案信息状态
							easyblobs.setDownState(1);
						}
						easyblobs.setDoweCreatorDate(new Date());
					}
					 
					easyblobs.setDoweUpdateDate(new Date());
					// 执行情况 当选择
					if(easyblobs.getImplementationCode()!=null && ("6".equals(easyblobs.getImplementationCode()) || "5".equals(easyblobs.getImplementationCode()) )){
						easyblobs.setImpleOverDate(null);
					}
					if(easyblobs.getImplementationCode()!=null && "4".equals(easyblobs.getImplementationCode())){
						easyblobs.setCloseCaseDate(null);
					}
					// 复议情况 若为否
					if(easyblobs.getReviewSituation()!=null && easyblobs.getReviewSituation() == 0){
						easyblobs.setReviewResultCode(-1);
						easyblobs.setReviewResultName("");
					}
					// 诉讼情况 若为否
					if(easyblobs.getLitigationSituation() != null && easyblobs.getLitigationSituation() ==0 ){
						easyblobs.setLitigationResult(-1);
					}
					// 是否公开 若为否
					if(easyblobs.getIsOpen()!=null && easyblobs.getIsOpen()==0){
						easyblobs.setOpenTime(null);
						easyblobs.setOpenWay(-1);
						easyblobs.setOpenWebsite("");
						easyblobs.setOtherWayDesc("");
					}
					
					easyblobs.setInfoStateDown(1);
					
					if(dockSusStateForTable==1 || dockSusStateForTable2==2){
						easyblobs.setDockingType(2);
					}
					easyblobs.setLastedUpdateTime(new Date());
					// 唯一文号年份字段维护
					if (!ChangnengUtil.isNull(easyblobs.getOnlineDecisionNumber())) {
						if (easyblobs.getOnlineDecisionNumber().indexOf("〔") > -1) {
							int begIndex = easyblobs.getOnlineDecisionNumber().indexOf("〔") + 1;
							int endIndex = begIndex + 4;
							easyblobs.setOnlineDecisionYear(
									Integer.parseInt(easyblobs.getOnlineDecisionNumber().substring(begIndex, endIndex)));
						}
					}
					countNum  = atvSanctionCaseMapper.updateByPrimaryKeySelective(easyblobs);
				}else{
					throw new BusinessException("保存类型不明确！");
				}
				// 下面会修改 控制表头信息
				record.setCaseId(easyblobs.getCaseId());
				record.setUpdateDate(new Date());
			}else{
				// id 不存在则新增 新增肯定只是上半部分新增
				AtvSanctionCaseWithBLOBs blobBean =  atvSanctionCaseMapper.getAtvSanctionCaseWithBLOBsOneByCaseId(caseId);
				if(blobBean!=null){
					countNum=2;
					return countNum;
				}
				// 生成编号  预留 王志满
				String produceCaseNumber = produceNumber.ProduceCaseNumber("JYCF", sysUsers.getBelongAreaId());		
				easyblobs.setEasySanctionNumber(produceCaseNumber);
				//easyblobs.setCaseType(1);
				easyblobs.setUpCreatorDate(new Date());
				easyblobs.setUpUpdateDate(new Date());
				//如果暂存状态为空，则修改立案信息状态
				if(easyblobs.getUpState()==null && ChangnengUtil.isNull(easyblobs.getIsTmp())){
					easyblobs.setUpState(1);
				}
				if(!(easyblobs.getPunishTypeCode()).contains("2")){
					easyblobs.setPenaltyAmount(new BigDecimal("0"));
				}
				easyblobs.setLastedUpdateTime(new Date());
				easyblobs.setCreatorUserId(sysUsers.getId());
				easyblobs.setCreatorUserName(sysUsers.getLoginname());
				easyblobs.setCreatorUserArea(sysUsers.getBelongAreaId());
				//
				//维护在线申请文号年份
				// 唯一文号年份字段维护（先半部分保存维护文号年份）
				if (!ChangnengUtil.isNull(easyblobs.getOnlineDecisionNumber())) {
					if (easyblobs.getOnlineDecisionNumber().indexOf("〔") > -1) {
						int begIndex = easyblobs.getOnlineDecisionNumber().indexOf("〔") + 1;
						int endIndex = begIndex + 4;
						easyblobs.setOnlineDecisionYear(
								Integer.parseInt(easyblobs.getOnlineDecisionNumber().substring(begIndex, endIndex)));
					}
				}
				//维护在线文号申请时间
				easyblobs.setGetOnlineDecdate(new Date());
				countNum  = atvSanctionCaseMapper.insertSelective(easyblobs);
				if(easyblobs.getLawUserList()!=null && !easyblobs.getLawUserList().isEmpty() && easyblobs.getLawUserList().size()>0){
					// * @param modelerType 1：基本信息，2：简易行政处罚，3：一般行政处罚，4：行政命令，5：查封扣押，6：限产停产，7：行政拘留，  8：环境污染犯罪，9：申请法院强制执行，10：其他移送，11：按日计罚
					cleMapper.insertBatchCaseLawEnforcement(easyblobs.getLawUserList(), 2+"", easyblobs.getId());
				}
				// 修改表头状态
				record.setCaseId(easyblobs.getCaseId());
				record.setSmrProgramState(1);
				record.setCmlPunishState(3);
				record.setUpdateDate(new Date());
				if (itemIds != null && !"".equals(itemIds)) {
					String objectId = easyblobs.getId();
					String[] split = itemIds.split(",");
					for (int j = 0; j < split.length; j++) {
						atvRecordsContentMapper.updateObjectId(split[j], objectId);
						atvFilesInfoMapper.updateStateByItemId(split[j]);
					}
				}
			}
			//更新 大案件 表头控制表  
			caseStateMapper.updateCaseStateByCaseIdForCloumn(record);
			return countNum;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("系统异常，操作失败！");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public int delEasyAtvSanctionCaseById(AtvSanctionCaseWithBLOBs bloBs,Integer deleteSource) throws Exception {
		int conNum = 0;
		if(bloBs ==null || ChangnengUtil.isNull(bloBs.getId())){
			throw new BusinessException("ID为空删除失败");
		}
		//放到redis中
		putCodeToRedis(bloBs.getId());
		// 修改表头状态
		CaseState record = new  CaseState();
		record.setCaseId(bloBs.getCaseId());
		record.setSmrProgramState(0);
		record.setCmlPunishState(0);
		record.setUpdateDate(new Date());
		caseStateMapper.updateCaseStateByCaseIdForCloumn(record);
		conNum = atvSanctionCaseMapper.deleteByPrimaryKeyByID(bloBs.getId());
		attachmentService.deleteFiles("atv_sanction_case", bloBs.getId());
		//对接删除
		String tableName = "atv_sanction_case";
		Integer dockSusState = caseStateService.getDockSusStateForTable(tableName,bloBs.getId());
		int dockingState = caseStateMapper.getDockingState(tableName, bloBs.getId());
		if(dockSusState==1 || dockingState==2){
			//要把对接状态置为需要对接，且对接类型为删除
			int i = caseStateMapper.changeDeleteState(tableName, bloBs.getId());
			// 维护管理表 
			caseManageTable manageTable  = caseManageTableMapper.selectByCaseIdAndInfoIdForDockingResult(bloBs.getCaseId(), bloBs.getId());
			if(manageTable!=null){
				manageTable.setDockingResult(4); // 等待中删除
				caseManageTableMapper.updateByPrimaryKeySelective(manageTable);
			}
			conNum=i;
		}
		
		/**
		 * 本业务块独立是删除，需要去维护基本信息是否需要对接。
		 * 除本业务块其他模块是否还有需要基本信息对接的模块，如果没有，则修改基本信息为无需对接。
		 * 筛选条件是 !is_delete  and 对接状态  !0    lhl
		 */
		if(deleteSource!=null && deleteSource ==1){
			baseInfoCaseService.isLastDeleteTable(bloBs.getCaseId(), bloBs.getId(),tableName);
		}
		/**
		 * 如果对接时间为空  那么说明从未对接成功过   那么这个删除，是不需要对接的
		 */
		baseInfoCaseService.updateTablesDockingStart( bloBs.getId(),tableName);
		return conNum;
	}

	@Override
	public boolean getCheckAtvSanctionCaseNumber(String checkName, String checkId, String checkType,SysUsers sysUsers) {
		if(!ChangnengUtil.isNull(checkType)){
			StringBuffer sbf = new StringBuffer();
			// checkType 类型   1：立案号- filing_number、2：决定书文号- decision_number、3：案卷号 -close_case_number
			sbf.append(" creator_user_area = '"+sysUsers.getBelongAreaId()+"' ");
			if("1".equals(checkType)){
				sbf.append(" and filing_number = '"+checkName+"' ");
			} else if("2".equals(checkType)){
				sbf.append(" and decision_number = '"+checkName+"' ");
			}else if("3".equals(checkType)){
				sbf.append(" and close_case_number = '"+checkName+"' ");
			}else{
				return false;
			}
			if(!ChangnengUtil.isNull(checkId)){
				sbf.append(" and id != '"+checkId+"' ");
			}
			sbf.append(" and is_delete != 1 ");
			Integer countNum = 0;
			try {
				countNum  = atvSanctionCaseMapper.checkAtvSanctionCaseNumber(sbf.toString());
			} catch (Exception e) {
				e.printStackTrace();
			}
		
			if(countNum >0){ // 数据库中已经存在
				return false;
			}else{
				return true;
			}
		}
		return false;
	}
	
	@Override
	public ResponseJson judgeIfPunish(String tablesName, String objectId) {
		try {
			String caseId = atvSanctionCaseMapper.selectCaseIdById(tablesName, objectId);
			CaseState caseState = caseStateMapper.selectByCaseId(caseId);
			if (caseState.getCmlPunishState() == 2 || caseState.getSmrProgramState() == 2) {
				//表示已进行过行政处罚填报
				return new ResponseJson().success("200", "200", null, "提交后将上报环保部行政处罚系统，请确认内容是否准确。", null);
			} else {
				return new ResponseJson().success("200", "200", null, "请确认没有行政处罚，如果存在处罚，请先进行处罚信息上报并提交，否则对接环保部行政处罚可能出错。", null);
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return new ResponseJson().failure("500", "500", "操作失败", "操作失败", null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult allCaseSubmitService(GenerateBean bean) throws Exception {
		JsonResult jsonResult  = new JsonResult();
		if(ChangnengUtil.isNull(bean.getObjectId()) )  throw new  BusinessException("主干ID不存在");
		if(ChangnengUtil.isNull(bean.getTablesName())) throw new  BusinessException("表名不能为空");
		if(ChangnengUtil.isNull(bean.getUpOrDowm())) throw new  BusinessException("上下类型不明确");
		SubmitCaseUtil submitCaseUtil  = new SubmitCaseUtil();
		String[] strArr  =  submitCaseUtil.itemType.get(bean.getTablesName());
		boolean sqlTf = true; // 上、下状态数据库字段是否存在
		//  提交  
		String setBut = "";
		String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");
		// 上半部分  
		if("1".equals(bean.getUpOrDowm())){
			String setColumn = strArr[0];
			if(ChangnengUtil.isNull(setColumn)){
				sqlTf = false;
			}
			setBut = "   "+ strArr[0]+" = 3 " ;
			/**修改头部控制表状态*/
			if ("".equals(strArr[1])) {
				//只有上半部分，改case_state表的状态
				String sql = "update case_state set " + strArr[2] + " = 2 " + " where case_id='"+bean.getBaseId()+"'";
				atvFilesInfoMapper.updateTrunkTime(sql);//执行sql
			}
			
			//如果是上办部分提交，我这里要记录他第一次提交的时间
			String firstSubmitTime = strArr[9];
			if(!ChangnengUtil.isNull(firstSubmitTime)){
				// 如果查询时间存在则去看表时间是否为空，为空时，则是第一次记录
				Date sqlFirstSubmitTime =attachmentMapper.selectDate("select "+firstSubmitTime+" from "+ bean.getTablesName() +" where id='" + bean.getObjectId() + "'");
				if(ChangnengUtil.isNull(sqlFirstSubmitTime)){
					Integer  upState = attachmentMapper.selectInteger("select "+setColumn+" from "+ bean.getTablesName() +" where id='" + bean.getObjectId() + "'");
					if(2 == upState){ // 并且上半部分为2（一键生成）状态，第一次进入提交
						String updateFirstSubmitTimeSql = "update " + bean.getTablesName() + " set " + firstSubmitTime + " = to_timestamp('" + date + "'"
			            		+ ",'yyyy-MM-dd HH24:mi:ss.ff9') where id='" + bean.getObjectId() + "'";
						atvFilesInfoMapper.updateTrunkTime(updateFirstSubmitTimeSql);//更新第一次提交时间
					}
				}
			}
		}else if("2".equals(bean.getUpOrDowm())){ // 下半部分提交
			String setColumn = strArr[1];
			if(ChangnengUtil.isNull(setColumn)){
				sqlTf = false;
			}
			setBut = "   "+ strArr[1]+" = 3 " ;
			//如果是下半部分提交，直接更改头部状态
			String fieldName;
			if ("2".equals(bean.getParentSelectType())) {
				//简易程序
				fieldName = strArr[2].split(",")[0];
			} else if ("3".equals(bean.getParentSelectType())) {
				//一般行政处罚
				fieldName = strArr[2].split(",")[1];
			} else {
				fieldName = strArr[2];
			}
			
			//单独处理按日计罚状态
			boolean flag = true;//caseid下所有的按日计罚是否全部提交
			if ("11".equals(bean.getParentSelectType())) {
				List<PenaltyDay> list = penaltyDayMapper.selectSecondPartStates(bean.getBaseId());
				for (PenaltyDay penaltyDay : list) {
					if (3 != penaltyDay.getSecondPartState() && !bean.getBaseId().equals(penaltyDay.getCaseId())) {
						//存在未提交数据
						flag = false;
						break;
					}
				}
			}
			if (flag) { // 控制页面头部展示状态，标志为已经完成该模块
				String sql = "update case_state set " + fieldName + " = 2 " + " where case_id='"+bean.getBaseId()+"'";
				atvFilesInfoMapper.updateTrunkTime(sql);//执行sql
			}
			/** 更新提交时间  上半部分提交时间字段  统一做为最后提交时间*/
			String upSubTime = strArr[3]; 
			if(!ChangnengUtil.isNull(upSubTime)){
				String upSubTimeSql = "update " + bean.getTablesName() + " set " + upSubTime + " = to_timestamp('" + date + "'"
	            		+ ",'yyyy-MM-dd HH24:mi:ss.ff9') where id='" + bean.getObjectId() + "'";
				atvFilesInfoMapper.updateTrunkTime(upSubTimeSql);//执行sql
			}
		}else{
			 throw new  BusinessException("上下类型不明确");
		}
		/**修改业务表按钮状态影响条数。*/
		int countNum = 0;
		if(sqlTf){
			// 修改该表的按钮状态 
			String upSql = " update  "+bean.getTablesName()+" set  "+setBut+"   where id = '"+bean.getObjectId()+"' ";
			countNum = atvFilesInfoMapper.updateTrunkTime(upSql);
			/**
			 * 是否对接环保部  -lhl  
			 * 1：修改基本信息 
			 * 2：修改业务表为需要对接
			 */
			String columnName = strArr[6]; //  [5]:对接状态    [6]: 是否已经对接过 "docking_state","dock_sus_state",
			if(!ChangnengUtil.isNull(columnName)){
 				// 1：把基本信息维护为需要对接
				CaseBaseInfo baseInfo =   caseBaseInfoMapper.selectByPrimaryKey(bean.getBaseId());
				// 基本信息 不是需要对接的，不是等待中的，不是已经对接过的
				if(baseInfo.getDockingState()!=2 && baseInfo.getDockingState()!=1 && baseInfo.getDockSusState()!=1 ){
					String updateSql = "update  case_base_info  set docking_state  =  1 where id= '"+bean.getBaseId()+"'";
					atvFilesInfoMapper.updateTrunkTime(updateSql);
				}
				// 2 只要提交  都修改为需要对接,同时去检察下对接管理表中是否有  docking_result= 0 等待中的对接记录，如果有，修改为 docking_result  =3
				String sql = " select  docking_state  from  "+bean.getTablesName()+" where  id ='" +bean.getObjectId() + "' ";
				Integer columuValue = caseStateMapper.getDockSusStateForTable(sql);
				if(columuValue ==2){ // （2：等待中） 条件满足则证明客户端提交已经对接到周景中间库
					// 一般行政处罚和建设项目的caseId和InfoId都是一样的，所以在管理表会查出两条，要分别置状态为3 --》补充2018-06-27 建设显目不再对接
					// 注意：上面注释只作为之前业务提示，现在为容错做法，严禁说只能 一般行政处罚（并且在有建设项目提交对接的话）才会出现集合，其他均应能对应一个
					List<caseManageTable> list = caseManageTableMapper.selectByCaseIdAndInfoIdForDockingResultList(bean.getBaseId(), bean.getObjectId());
					for (caseManageTable caseManageTable : list) {
						if(caseManageTable!=null){
							caseManageTable.setDockingResult(3);
							caseManageTableMapper.updateByPrimaryKeySelective(caseManageTable);
						}
					}
				}
				// 3 全部修改为需要对接状态
				String updateSql = "update "+bean.getTablesName()+"  set docking_state  =  1  where  id ='" +bean.getObjectId() + "' ";
				atvFilesInfoMapper.updateTrunkTime(updateSql);
			}
		}
		
		//修改案件状态表信息 最后更新时间
		CaseState caseState = new CaseState();
		caseState.setCaseId(bean.getBaseId());
		caseState.setUpdateDate(new Date());
		caseStateMapper.updateCaseStateByCaseIdForCloumn(caseState);
		if(countNum>0){
			jsonResult.setResult(Const.RESULT_SUCCESS);
			jsonResult.setMessage(getDetailMessage(bean.getTablesName(), bean.getUpOrDowm(),bean.getParentSelectType(),bean.getObjectId()));
		}else{
			jsonResult.setResult(Const.RESULT_ERROR);
		}
		return jsonResult;
	}

	/**
	 * 获得提交成功后不同的提示语
	 * @param tableName 表名
	 * @param upOrDowm 上部分，或者下部分
	 * @param parentSelectType 选中的模块序号  1基本信息 2简易 3一般 4命令 .....
	 * @param objectId 小案件id  主要用于查询 决定书发文日期
	 * @return
	 * <AUTHOR>
	 * @date 2017年12月20日-下午2:34:56
	 */
	private String getDetailMessage(String tableName,String upOrDowm,String parentSelectType,String objectId){
		String detailMag = "";
		// 简易程序，一般行政处罚
	    if("atv_sanction_case".equals(tableName)){
	    	if("3".equals(parentSelectType)){
	    		if("1".equals(upOrDowm)){
	    			if(!ChangnengUtil.isNull(objectId)) {
	    				//获取决定书发文日期。
	    				AtvSanctionCaseWithBLOBs atvBean = atvSanctionCaseMapper.selectByPrimaryKey(objectId);
	    				if(!ChangnengUtil.isNull(atvBean) && !ChangnengUtil.isNull(atvBean.getDecisionDate())) {
	    				Calendar calender = Calendar.getInstance();
	    				calender.setTime(atvBean.getDecisionDate());
	    					//提示信息案例 ：本案于YYYY年MM月DD日签发决定书，请于6个月内完成结案。下一步请点击右侧导航栏的“结案”
	    					detailMag = "<p style=\"display: block;\"><p style=\"color:red;text-align:left;padding:20px 50px;\">本案于"+calender.get(Calendar.YEAR)+"年"+(calender.get(Calendar.MONTH)+1)+"月"+calender.get(Calendar.DATE)+"日签发决定书，请于6个月内完成结案。下一步请点击右侧导航栏的“结案”填写结案内容。</p></p>";
	    				}
	    			}
	    		}
	    	}
	    } 
		return detailMag;
	}

	@Override
	public WenHaoSearchResult selectIsApplicationByAreaCode(String belongAreaCode,String type) {
		WenHaoSearchResult isApplication = atvSanctionCaseMapper.selectIsApplicationByAreaCode(belongAreaCode,type);
		return isApplication;
	}
	@Override
	public String selectDecisionNumberPrefix(String belongAreaCode,String type,String year) {
		CaseManageMaxNumber caseManageMaxNumber = caseManageMaxNumberMapper.selectDecisionNumberPrefix(belongAreaCode,type,year);
		String prefix = "";
		if(caseManageMaxNumber!=null && caseManageMaxNumber.getPrefixCode()!=null && !caseManageMaxNumber.getPrefixCode().equals("")) {
			prefix = caseManageMaxNumber.getPrefixCode();
		}
		return prefix;
	}
    
	public void putCodeToRedis(String id){
		try {
			AtvSanctionCaseWithBLOBs record = atvSanctionCaseMapper.selectByPrimaryKey(id);
			Calendar calendar = Calendar.getInstance();
			int year = calendar.get(calendar.YEAR);
			Calendar calendarData = Calendar.getInstance();
			String code = record.getOnlineDecisionNumber();
			if(code != null){
				String yearData = code.substring(code.indexOf("〔")+1, code.indexOf("〕"));
				if(yearData.equals(String.valueOf(year)) && record.getOnlineDecisionCode()!=null){
					CaseBaseInfo caseBaseInfo = baseInfoCaseService.getCaseBaseInfo(record.getCaseId());
					if(caseBaseInfo.getUserBelongAreaCode()!=null){
						operatorWenHao.addCode(caseBaseInfo.getUserBelongAreaCode().substring(0, 4)+"0000", "1", record.getOnlineDecisionCode());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	public static void main(String[] args) {
		Integer ttInteger = 2;
		if("2".equals(ttInteger)){
			System.out.println("ok");
		}else {
			System.out.println("No way");
		}
	}

	@Override
	public boolean selectRedisOnlyNumber(String userTareaCode, String type, String code, String year, String columnName,String userTareaCodeName,
			String tableName, String onlineDecisionNumber) {
		//如果在线申请文号为空则不进行校验。
		if(ChangnengUtil.isNull(onlineDecisionNumber)) {
			return false;
		}
		//组合查询sql
		String sql = "select count(*) from [tableName] where [userTareaCode] and online_decision_number= [onlineDecisionNumber]";
		return false;
	}

	@Override
	public ResponseJson blockCheckAtv(String infoId,String tableName,String methodType,String upOrDown) {
		/**
		 * methodType 1：基本信息，2：简易行政处罚，3：一般行政处罚，4：行政命令，5：查封扣押，6：限产停产，7：行政拘留，
						 8：环境污染犯罪，9：申请法院强制执行，10：其他移送，11：按日计罚
		 */
		if(ChangnengUtil.isNull(infoId)){return new ResponseJson().error("200", "200", "业务信息不存在，请先保存！", null, null);}
		if(ChangnengUtil.isNull(methodType)){return new ResponseJson().error("200", "200", "保存业务模块标志为空", null, null);}
		if(ChangnengUtil.isNull(tableName)){return new ResponseJson().error("200", "200", "表名称不能为空", null, null);}
		// select IMPLEMENTATION_CODE, CASE_LAW_ENFORC, CASE_LAW_NAME  from atv_sanction_case   where id= #{infoId}
		StringBuffer assembleSql = new StringBuffer();
		HashMap<String, String>  retMap = null; 
		if("2".equals(methodType) || "3".equals(methodType)){
			 
			assembleSql.append(" select ID, IMPLEMENTATION_CODE, CASE_LAW_ENFORC  from "+tableName+"   where id='"+infoId+"' ");
			retMap = atvSanctionCaseMapper.selectBlockCheck(assembleSql.toString());
			
			if(ChangnengUtil.isNull(upOrDown)){return new ResponseJson().error("200", "200", "保存上下部分模糊", null, null);}
			if("2".equals(upOrDown)){
				if(retMap!=null && !ChangnengUtil.isNull(retMap.get("IMPLEMENTATION_CODE"))){
					if(retMap.get("IMPLEMENTATION_CODE").equals("4")){
						return new ResponseJson().error("200", "200", "请先保存信息，检测到执行情况为尚未执行，无法提交！", null, null);
					}
				}
			}
			if(retMap!=null){
				if(ChangnengUtil.isNull(retMap.get("CASE_LAW_ENFORC"))){
					return new ResponseJson().error("200", "200", "请先添加保存主要执法人员信息，检测到主要执法人员为空，无法提交！或联系管理员开放权限！", null, null);
				}
			}else{
				return new ResponseJson().error("200", "200", "请先保存信息，没有找到业务信息", null, null);
			}
		}/*else if("5".equals(methodType)){
			
		}else if("6".equals(methodType)){
			return new ResponseJson().error("200", "200", "保存业务模块不清晰", null, null);
		}else if("7".equals(methodType)){
			return new ResponseJson().error("200", "200", "保存业务模块不清晰", null, null);
		}*/else if("8".equals(methodType)){
		
			assembleSql.append(" select ID,ACCEPT_TRANSFER, CASE_LAW_ENFORC  from "+tableName+"   where id='"+infoId+"' ");
			retMap = atvSanctionCaseMapper.selectBlockCheck(assembleSql.toString());
			if(retMap!=null){
				if(ChangnengUtil.isNull(retMap.get("ACCEPT_TRANSFER"))){
					return new ResponseJson().error("200", "200", "请先添加保存接受移送司法机关，接受移送司法机关为空，无法提交！", null, null);
				}
				if(ChangnengUtil.isNull(retMap.get("CASE_LAW_ENFORC"))){
					return new ResponseJson().error("200", "200", "请先添加保存主要执法人员信息，检测到主要执法人员为空，无法提交！", null, null);
				}
			}else{
				return new ResponseJson().error("200", "200", "请先保存信息，没有找到业务信息", null, null);
			}
			 
		}/*else if("9".equals(methodType)){
			return new ResponseJson().error("200", "200", "保存业务模块不清晰", null, null);
		}*/else if("11".equals(methodType)){
			assembleSql.append(" select ID, EXCUTE_SITUATION_CODE, CASE_LAW_ENFORC  from "+tableName+"   where id='"+infoId+"' ");
			retMap = atvSanctionCaseMapper.selectBlockCheck(assembleSql.toString());
			
			if(ChangnengUtil.isNull(upOrDown)){return new ResponseJson().error("200", "200", "保存上下部分模糊", null, null);}
			if("2".equals(upOrDown)){
				if(retMap!=null && !ChangnengUtil.isNull(retMap.get("EXCUTE_SITUATION_CODE"))){
					if(retMap.get("EXCUTE_SITUATION_CODE").equals("4")){
						return new ResponseJson().error("200", "200", "请先保存信息，检测到执行情况为尚未执行，无法提交！", null, null);
					}
				}
			}
			if(retMap!=null){
				if(ChangnengUtil.isNull(retMap.get("CASE_LAW_ENFORC"))){
					return new ResponseJson().error("200", "200", "请先添加保存主要执法人员信息，检测到主要执法人员为空，无法提交！或联系管理员开放权限！", null, null);
				}
			}else{
				return new ResponseJson().error("200", "200", "请先保存信息，没有找到业务信息", null, null);
			}
		}else{
			assembleSql.append(" select ID, CASE_LAW_ENFORC  from "+tableName+"   where id='"+infoId+"' ");
			retMap = atvSanctionCaseMapper.selectBlockCheck(assembleSql.toString());
			if(retMap!=null){
				if(ChangnengUtil.isNull(retMap.get("CASE_LAW_ENFORC"))){
					return new ResponseJson().error("200", "200", "请先添加保存主要执法人员信息，检测到主要执法人员为空，无法提交！", null, null);
				}
			}else{
				return new ResponseJson().error("200", "200", "请先保存信息，没有找到业务信息", null, null);
			}
		}
		return new ResponseJson().success("200", "200", "成功", null, null);
	}
	
}
 