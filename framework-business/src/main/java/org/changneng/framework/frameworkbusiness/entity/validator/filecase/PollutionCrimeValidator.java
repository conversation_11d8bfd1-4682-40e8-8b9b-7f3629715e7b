package org.changneng.framework.frameworkbusiness.entity.validator.filecase;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.changneng.framework.frameworkbusiness.entity.filecase.CaseLawEnforcement;
import org.changneng.framework.frameworkbusiness.entity.filecase.PollutionCrimeWithBLOBs;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.RegexUtil;

/**
 * 环境污染犯罪实体类后台校验
 * 
 * @ClassName: PollutionCrimeValidator
 * @Description:
 * <AUTHOR>
 * @date 2017年8月8日 上午11:23:00
 *
 */
public class PollutionCrimeValidator implements ConstraintValidator<PollutionCrimeValid, PollutionCrimeWithBLOBs> {

	@Override
	public void initialize(PollutionCrimeValid constraintAnnotation) {
	}

	@Override
	public boolean isValid(PollutionCrimeWithBLOBs value, ConstraintValidatorContext context) {
		if (value == null) {
			return true;
		}
		boolean isValid = true;
		if (ChangnengUtil.isNull(value.getSaveType())) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("参数异常，校验失败！").addPropertyNode("saveType").addConstraintViolation();
			return false;
		}
		if ("1".equals(value.getSaveType())) {
			if("1".equals(value.getIsTmp())){ // 暂存
				// 上半部分校验
				if (ChangnengUtil.isNull(value.getTransferDepartment())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送单位不能为空").addPropertyNode("transferDepartment").addConstraintViolation();
					return false;
				}
				if (value.getTransferDepartment().length() > 60) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送单位长度最大为60个字符").addPropertyNode("transferDepartment").addConstraintViolation();
					return false;
				}
				if (ChangnengUtil.isNull(value.getTransferDate())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送时间不能为空").addPropertyNode("transferDate").addConstraintViolation();
					return false;
				}
	
				if (value.getBriefCase()!=null && value.getBriefCase().length() > 2000) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("简要案情长度最大为2000个字符").addPropertyNode("briefCase").addConstraintViolation();
					return false;
				}
				if (value.getTransferReason()!=null&&value.getTransferReason().length() > 2000) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送理由长度最大为2000个字符").addPropertyNode("transferReason").addConstraintViolation();
					 
					return false;
				}
				if (value.getTransferCaseNumber()!=null&&value.getTransferCaseNumber().length() > 30) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送案卷编号长度最大为30个字符").addPropertyNode("transferCaseNumber").addConstraintViolation();
					return false;
				}
			}else{
				// 上半部分校验
				if (ChangnengUtil.isNull(value.getTransferDepartment())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送单位不能为空").addPropertyNode("transferDepartment").addConstraintViolation();
					return false;
				}
				if (value.getTransferDepartment().length() > 60) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送单位长度最大为60个字符").addPropertyNode("transferDepartment")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getTransferDate())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送时间不能为空").addPropertyNode("transferDate")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getIllegalTypeName())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("违法案件类型不能为空").addPropertyNode("illegalTypeName")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getTransferCaseNumber())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送案卷编号不能为空").addPropertyNode("transferCaseNumber")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getBriefCase())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("简要案情不能为空").addPropertyNode("briefCase")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (value.getBriefCase().length() > 4000) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("简要案情长度最大为4000个字符").addPropertyNode("briefCase")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getTransferReason())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送理由不能为空").addPropertyNode("transferReason")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (value.getTransferReason().length() > 2000) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送理由长度最大为2000个字符").addPropertyNode("transferReason")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (value.getTransferCaseNumber().length() > 30) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("移送案卷编号长度最大为30个字符").addPropertyNode("transferCaseNumber")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if(value.getLawUserList()==null || value.getLawUserList().size()==0){
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("主要执法人员信息不能为空").addPropertyNode("lawUserList").addConstraintViolation();
					return false;
				}else{
					if(value.getLawUserList().size()<2 || value.getLawUserList().size()>6){
						context.disableDefaultConstraintViolation();
						context.buildConstraintViolationWithTemplate("案件主要执法人员，执法人员人数不小于2人，不大于6人！").addPropertyNode("lawUserList").addConstraintViolation();
						return false;
					}
					Set<String> cardIdList = new HashSet<String>(); // 主要执法人员 身份证 信息
					for (int i = 0; i < value.getLawUserList().size(); i++) {
						CaseLawEnforcement  tmp= value.getLawUserList().get(i);
						if(ChangnengUtil.isNull(tmp.getLawUserName())){
							context.disableDefaultConstraintViolation();
							context.buildConstraintViolationWithTemplate("主要执法人员信息,执法人员信息不能有为空").addPropertyNode("lawUserList").addConstraintViolation();
							return false;
						} 
						if(ChangnengUtil.isNull(tmp.getUserCardId())){
							context.disableDefaultConstraintViolation();
							context.buildConstraintViolationWithTemplate("主要执法人员信息,身份证号信息不能有为空").addPropertyNode("lawUserList").addConstraintViolation();
							return false;
						}
						if(ChangnengUtil.isNull(tmp.getLawEnforcId())){
							context.disableDefaultConstraintViolation();
							context.buildConstraintViolationWithTemplate("主要执法人员信息,执法证号信息不能有为空").addPropertyNode("lawUserList").addConstraintViolation();
							return false;
						}
						if(ChangnengUtil.isNull(tmp.getLawDate())){
							context.disableDefaultConstraintViolation();
							context.buildConstraintViolationWithTemplate("主要执法人员信息,执法日期信息不能有为空").addPropertyNode("lawUserList").addConstraintViolation();
							return false;
						}
						if(tmp.getUserCardId()!=null){
							if(!cardIdList.contains(tmp.getUserCardId().trim())){ // 如果不在set集合中，则添加进去
								cardIdList.add(tmp.getUserCardId().trim());
							}else{
								context.disableDefaultConstraintViolation();
								context.buildConstraintViolationWithTemplate("主要执法人员信息,身份证号有重复。").addPropertyNode("lawUserList").addConstraintViolation();
								return false;
							}
						}
					}
				
				}
				// 下半部分校验
				if (ChangnengUtil.isNull(value.getAcceptTransfer())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("接受移送司法机关不能为空").addPropertyNode("acceptTransfer")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (value.getAcceptTransfer().length() > 60) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("接受移送司法机关名称长度最大为60个字符").addPropertyNode("acceptTransfer")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getIsAccept())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("是否受理选项不能为空").addPropertyNode("isAccept")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (ChangnengUtil.isNull(value.getFillContact())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("填报人联系方式不能为空").addPropertyNode("fillContact")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (!RegexUtil.isMobile(value.getFillContact())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("填报人联系方式格式不正确").addPropertyNode("fillContact").addConstraintViolation();
					return false;
				}
				if (value.getFillContact().length() > 13) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("填报人联系方式长度最大为13个字符").addPropertyNode("fillContact")
							.addConstraintViolation();
					// isValid=false;
					return false;
				}
				if (value.getRemark().length() > 2000) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate("备注最大长度2000个字符").addPropertyNode("remark").addConstraintViolation();
					return false;
				}
			}
		}else {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("参数异常，校验失败！").addPropertyNode("saveType").addConstraintViolation();
			return false;
		}

		return isValid;
	}
}
