<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.TXchjzfMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    <id column="ZFID" jdbcType="VARCHAR" property="zfid" />
    <result column="WRYBH" jdbcType="VARCHAR" property="wrybh" />
    <result column="WRYMC" jdbcType="VARCHAR" property="wrymc" />
    <result column="KSSJ" jdbcType="TIMESTAMP" property="kssj" />
    <result column="JSSJ" jdbcType="TIMESTAMP" property="jssj" />
    <result column="JCR" jdbcType="VARCHAR" property="jcr" />
    <result column="ZFZH" jdbcType="VARCHAR" property="zfzh" />
    <result column="SSHBBM" jdbcType="VARCHAR" property="sshbbm" />
    <result column="RWLX" jdbcType="VARCHAR" property="rwlx" />
    <result column="JD" jdbcType="VARCHAR" property="jd" />
    <result column="WD" jdbcType="VARCHAR" property="wd" />
    <result column="WRYDZ" jdbcType="VARCHAR" property="wrydz" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
    <result column="RKSJ" jdbcType="TIMESTAMP" property="rksj" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LHBJCXJ" jdbcType="VARCHAR" property="lhbjcxj" />
    <result column="KYBLXCQK" jdbcType="VARCHAR" property="kyblxcqk" />
    <result column="XWBLWD" jdbcType="VARCHAR" property="xwblwd" />
    
    <result column="JCRBH" jdbcType="VARCHAR" property="jcrbh" />
    <result column="SFFXSXHJWFXW" jdbcType="VARCHAR" property="sffxsxhjwfxw" />
    <result column="SXHJWFXWLX" jdbcType="VARCHAR" property="sxhjwfxwlx" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId"/>
    <result column="TASK_FROM_CODE" jdbcType="VARCHAR" property="taskFromCode"/>
    <result column="TASK_FROM_NAME" jdbcType="VARCHAR" property="taskFromName"/>
  </resultMap>
  <sql id="Base_Column_List">
    ZFID, WRYBH, WRYMC, KSSJ, JSSJ, JCR, ZFZH, SSHBBM, RWLX, JD, WD, WRYDZ, BZ, RKSJ, 
    CREATE_DATE,JCRBH,SFFXSXHJWFXW,SXHJWFXWLX
  </sql>
  <sql id="Blob_Column_List">
    LHBJCXJ, KYBLXCQK, XWBLWD
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_XCHJZF
    where ZFID = #{zfid,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByPrimaryKeyList" parameterType="java.lang.String" resultMap="BaseResultMap">
  	select tskLoj.*,
  			 (( case  when  (tskLoj.JCRBHS is NULL or replace(tskLoj.JCRBHS,' ','') is NULL)  THEN 'NULL' else tskLoj.JCRBHS end )||','|| ( case  when  (loc.CHECK_USER_IDS is NULL or replace(loc.CHECK_USER_IDS,' ','') is NULL)  THEN 'NULL' else loc.CHECK_USER_IDS end ) ) as JCRBH ,
  			 (( case  when  (tskLoj.JCRS is NULL or replace(tskLoj.JCRS,' ','') is NULL)  THEN 'NULL' else tskLoj.JCRS end )||','|| ( case  when  (loc.CHECK_USER_NAMES is NULL or replace(loc.CHECK_USER_NAMES,' ','') is NULL)  THEN 'NULL' else loc.CHECK_USER_NAMES end ) ) as JCR ,
  			 (( case  when  (tskLoj.ZFZHS is NULL or replace(tskLoj.ZFZHS,' ','') is NULL )  THEN 'NULL' else tskLoj.ZFZHS end ) ||','||( case  when   (loc.LAW_ENFORC_IDS is NULL or replace(loc.LAW_ENFORC_IDS,' ','') is NULL)  THEN 'NULL' else  loc.LAW_ENFORC_IDS end )) as ZFZH
			from  (
							SELECT tsk.id ZFID,
							tsk.task_id,
							tsk.task_from_code,
							tsk.task_from_name,
							tsk.CHECK_SUMMARY  as LHBJCXJ,
									 (CASE
											WHEN tsk.TASK_LAWOBJECT_STATUS = '1' or tsk.TASK_LAWOBJECT_STATUS = '4' or  tsk.TASK_LAWOBJECT_STATUS is null  THEN '4'
											WHEN tsk.TASK_LAWOBJECT_STATUS = '2'  THEN '1'
											WHEN tsk.TASK_LAWOBJECT_STATUS = '3'  THEN '2'
											WHEN tsk.TASK_LAWOBJECT_STATUS = '5'  THEN '3'
											ELSE '' END   ) as RWLX ,
									 ( CASE
											WHEN tsk.is_illegalact_name = '1' THEN '1'
											WHEN tsk.is_illegalact_name = '2' THEN '2'
											WHEN tsk.is_illegalact_name is null THEN ' '
											ELSE '0' END  )  as  SFFXSXHJWFXW ,
									 tsk.ENVIRONMENTAL_VIOLATIONS_CODE as SXHJWFXWLX ,
									 ( case  when  leoj.object_number  is null  THEN ' ' else leoj.object_number end )  as  WRYBH,
									 tsk.law_object_name as WRYMC ,
									 leoj.address as WRYDZ,
									 substr(tsk.end_user_code, 0, 6)  as  SSHBBM ,
									 tsk.PROCESSING_TIME as KSSJ ,
									 tsk.END_DATE as JSSJ ,

									 leoj.gis_coordinate_x as JD,
									 leoj.gis_coordinate_y as WD,
									 tsk.end_user_id as JCRBHS,
									 tsk.end_user_name  as JCRS ,
									 ( case  when  sur.law_enforc_id is null  THEN ' ' else sur.law_enforc_id  end )  as  ZFZHS ,

									 sysdate as RKSJ
							FROM  TASK  tsk
							LEFT JOIN history_law_enforce_object   leoj  ON  tsk.id =  leoj.task_id
							LEFT JOIN  SYS_USERS sur  ON  tsk.end_user_id =  sur.id
							where  tsk.task_state_code = 2
							and  (tsk.task_from_code != '9' or tsk.task_from_code is null)
							and  leoj.belong_area_id  not in  ('35019000','35099800','35069300','35052700','35089800')
							and  tsk.END_DATE &gt;=to_date(#{begTime},'yyyy-mm-dd hh24:mi:ss') and tsk.END_DATE &lt;=to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
						) tskLoj
		LEFT JOIN  local_check loc on loc.task_id = tskLoj.ZFID
  </select>
  <select id="selectSurveyRecord" parameterType="java.lang.String" resultMap="BaseResultMap">
	 select a1.JCR, a1.KYBLXCQK, a1.JCRBH,
	 		 (( case  when  (a1.ZFZHs  is NULL or replace(a1.ZFZHs,' ','') is NULL)  THEN 'NULL' else a1.ZFZHs  end )||','|| ( case  when  (a2.law_enforc_id  is NULL or replace(a2.law_enforc_id,' ','') is NULL)  THEN 'NULL' else a2.law_enforc_id  end )) as ZFZH  from
		(
			select
					( case  when  (chec_user_names  is NULL or replace(chec_user_names,' ','') is NULL)  THEN 'NULL' else chec_user_names  end ) ||',' || ( case  when  (record_user_name  is NULL or replace(record_user_name,' ','') is NULL)  THEN 'NULL' else record_user_name  end ) as JCR ,
					law_enforc_ids as ZFZHs ,
					chec_user_ids||','||record_user_id as JCRBH,
					record_user_id ,local_desc as KYBLXCQK
			from SURVEY_RECORD where  TASK_ID= #{taskID}  and rownum = 1
			ORDER BY create_date asc
		) a1
	LEFT JOIN  SYS_USERS a2  ON  a1.record_user_id =  a2.id
  </select>
  <select id="selectAsking" parameterType="java.lang.String" resultMap="BaseResultMap">
 		select a1.id as zfid, a1.jcr, 
 			(( case  when  (a1.jcrbhs  is NULL or replace(a1.jcrbhs,' ','') is NULL)  THEN 'NULL' else a1.jcrbhs  end )||','|| ( case  when  (a2.id  is NULL or replace(a2.id,' ','') is NULL)  THEN 'NULL' else a2.id  end )) as jcrbh,
 			(( case  when  (a1.ZFZHs  is NULL or replace(a1.ZFZHs,' ','') is NULL)  THEN 'NULL' else a1.ZFZHs  end )||','|| ( case  when  (a2.law_enforc_id  is NULL or replace(a2.law_enforc_id,' ','') is NULL)  THEN 'NULL' else a2.law_enforc_id  end )) as ZFZH   from
		(
				select  
					 id,
					(( case  when  (asking_user_names  is NULL or replace(asking_user_names,' ','') is NULL)  THEN 'NULL' else asking_user_names  end ) ||','|| ( case  when  (record_user_name  is NULL or replace(record_user_name,' ','') is NULL)  THEN 'NULL' else record_user_name  end ) ) jcr ,
					 law_enforc_ids as ZFZHs ,
					 (( case  when  (asking_user_ids  is NULL or replace(asking_user_ids,' ','') is NULL)  THEN 'NULL' else asking_user_ids  end )) jcrbhs ,
					 record_user
				from asking_record where  TASK_ID=#{taskID}  and rownum = 1
				ORDER BY asking_start_date asc
		) a1
		LEFT JOIN  SYS_USERS a2  ON  a1.record_user =  a2.id
  </select>
  
  <select id="selectAskingContent" parameterType="java.lang.String" resultMap="BaseResultMap">
		select   xmlagg(xmlparse(content a3.tt||' 结束。' wellformed) order by a3.tt).getclobval() as XWBLWD
		from  ( 
				select ('问：'||A2.asking_content ||'答：'|| A2.answer_content) as tt   
									from (
												select id
												from asking_record where  TASK_ID=  #{taskID}  and rownum = 1
												ORDER BY asking_start_date asc 
											) a1
				LEFT JOIN  asking_content a2 on a1.id = a2.asking_id
		) a3 
  </select>

  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from T_XCHJZF
    where ZFID = #{zfid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    insert into T_XCHJZF (ZFID, WRYBH, WRYMC, 
      KSSJ, JSSJ, JCR, 
      ZFZH, SSHBBM, RWLX, 
      JD, WD, WRYDZ, BZ, 
      RKSJ, CREATE_DATE, LHBJCXJ, 
      KYBLXCQK, XWBLWD)
    values (#{zfid,jdbcType=VARCHAR}, #{wrybh,jdbcType=VARCHAR}, #{wrymc,jdbcType=VARCHAR}, 
      #{kssj,jdbcType=TIMESTAMP}, #{jssj,jdbcType=TIMESTAMP}, #{jcr,jdbcType=VARCHAR}, 
      #{zfzh,jdbcType=VARCHAR}, #{sshbbm,jdbcType=VARCHAR}, #{rwlx,jdbcType=VARCHAR}, 
      #{jd,jdbcType=VARCHAR}, #{wd,jdbcType=VARCHAR}, #{wrydz,jdbcType=VARCHAR}, #{bz,jdbcType=VARCHAR}, 
      #{rksj,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP}, #{lhbjcxj,jdbcType=VARCHAR}, 
      #{kyblxcqk,jdbcType=VARCHAR}, #{xwblwd,jdbcType=VARCHAR})
  </insert>
  
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    insert into T_XCHJZF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="zfid != null">
        ZFID,
      </if>
      <if test="wrybh != null">
        WRYBH,
      </if>
      <if test="wrymc != null">
        WRYMC,
      </if>
      <if test="kssj != null">
        KSSJ,
      </if>
      <if test="jssj != null">
        JSSJ,
      </if>
      <if test="jcr != null">
        JCR,
      </if>
      <if test="zfzh != null">
        ZFZH,
      </if>
      <if test="sshbbm != null">
        SSHBBM,
      </if>
      <if test="rwlx != null">
        RWLX,
      </if>
      <if test="jd != null">
        JD,
      </if>
      <if test="wd != null">
        WD,
      </if>
      <if test="wrydz != null">
        WRYDZ,
      </if>
      <if test="bz != null">
        BZ,
      </if>
      <if test="rksj != null">
        RKSJ,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="lhbjcxj != null">
        LHBJCXJ,
      </if>
      <if test="kyblxcqk != null">
        KYBLXCQK,
      </if>
      <if test="xwblwd != null">
        XWBLWD,
      </if>
      <if test="jcrbh != null">
        JCRBH,
      </if>
      <if test="sffxsxhjwfxw != null">
        SFFXSXHJWFXW,
      </if>
      <if test="sxhjwfxwlx != null">
        SXHJWFXWLX,
      </if>
      <if test="taskId !=null">
        TASK_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="zfid != null">
        #{zfid,jdbcType=VARCHAR},
      </if>
      <if test="wrybh != null">
        #{wrybh,jdbcType=VARCHAR},
      </if>
      <if test="wrymc != null">
        #{wrymc,jdbcType=VARCHAR},
      </if>
      <if test="kssj != null">
        #{kssj,jdbcType=TIMESTAMP},
      </if>
      <if test="jssj != null">
        #{jssj,jdbcType=TIMESTAMP},
      </if>
      <if test="jcr != null">
        #{jcr,jdbcType=VARCHAR},
      </if>
      <if test="zfzh != null">
        #{zfzh,jdbcType=VARCHAR},
      </if>
      <if test="sshbbm != null">
        #{sshbbm,jdbcType=VARCHAR},
      </if>
      <if test="rwlx != null">
        #{rwlx,jdbcType=VARCHAR},
      </if>
      <if test="jd != null">
        #{jd,jdbcType=VARCHAR},
      </if>
      <if test="wd != null">
        #{wd,jdbcType=VARCHAR},
      </if>
      <if test="wrydz != null">
        #{wrydz,jdbcType=VARCHAR},
      </if>
      <if test="bz != null">
        #{bz,jdbcType=VARCHAR},
      </if>
      <if test="rksj != null">
        #{rksj,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lhbjcxj != null">
        #{lhbjcxj,jdbcType=VARCHAR},
      </if>
      <if test="kyblxcqk != null">
        #{kyblxcqk,jdbcType=VARCHAR},
      </if>
      <if test="xwblwd != null">
        #{xwblwd,jdbcType=VARCHAR},
      </if>
       <if test="jcrbh != null">
        #{jcrbh,jdbcType=VARCHAR},
      </if>
      <if test="sffxsxhjwfxw != null">
        #{sffxsxhjwfxw,jdbcType=VARCHAR},
      </if>
      <if test="sxhjwfxwlx != null">
       #{sxhjwfxwlx,jdbcType=VARCHAR},
      </if>
      <if test="taskId !=null">
        #{taskId,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    update T_XCHJZF
    <set>
      <if test="wrybh != null">
        WRYBH = #{wrybh,jdbcType=VARCHAR},
      </if>
      <if test="wrymc != null">
        WRYMC = #{wrymc,jdbcType=VARCHAR},
      </if>
      <if test="kssj != null">
        KSSJ = #{kssj,jdbcType=TIMESTAMP},
      </if>
      <if test="jssj != null">
        JSSJ = #{jssj,jdbcType=TIMESTAMP},
      </if>
      <if test="jcr != null">
        JCR = #{jcr,jdbcType=VARCHAR},
      </if>
      <if test="zfzh != null">
        ZFZH = #{zfzh,jdbcType=VARCHAR},
      </if>
      <if test="sshbbm != null">
        SSHBBM = #{sshbbm,jdbcType=VARCHAR},
      </if>
      <if test="rwlx != null">
        RWLX = #{rwlx,jdbcType=VARCHAR},
      </if>
      <if test="jd != null">
        JD = #{jd,jdbcType=VARCHAR},
      </if>
      <if test="wd != null">
        WD = #{wd,jdbcType=VARCHAR},
      </if>
      <if test="wrydz != null">
        WRYDZ = #{wrydz,jdbcType=VARCHAR},
      </if>
      <if test="bz != null">
        BZ = #{bz,jdbcType=VARCHAR},
      </if>
      <if test="rksj != null">
        RKSJ = #{rksj,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lhbjcxj != null">
        LHBJCXJ = #{lhbjcxj,jdbcType=VARCHAR},
      </if>
      <if test="kyblxcqk != null">
        KYBLXCQK = #{kyblxcqk,jdbcType=VARCHAR},
      </if>
      <if test="xwblwd != null">
        XWBLWD = #{xwblwd,jdbcType=VARCHAR},
      </if>
       <if test="jcrbh != null">
        JCRBH = #{jcrbh,jdbcType=VARCHAR},
      </if>
      <if test="sffxsxhjwfxw != null">
        SFFXSXHJWFXW = #{sffxsxhjwfxw,jdbcType=VARCHAR},
      </if>
      <if test="sxhjwfxwlx != null">
       SXHJWFXWLX = #{sxhjwfxwlx,jdbcType=VARCHAR},
      </if>
    </set>
    where ZFID = #{zfid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    update T_XCHJZF
    set WRYBH = #{wrybh,jdbcType=VARCHAR},
      WRYMC = #{wrymc,jdbcType=VARCHAR},
      KSSJ = #{kssj,jdbcType=TIMESTAMP},
      JSSJ = #{jssj,jdbcType=TIMESTAMP},
      JCR = #{jcr,jdbcType=VARCHAR},
      ZFZH = #{zfzh,jdbcType=VARCHAR},
      SSHBBM = #{sshbbm,jdbcType=VARCHAR},
      RWLX = #{rwlx,jdbcType=VARCHAR},
      JD = #{jd,jdbcType=VARCHAR},
      WD = #{wd,jdbcType=VARCHAR},
      WRYDZ = #{wrydz,jdbcType=VARCHAR},
      BZ = #{bz,jdbcType=VARCHAR},
      RKSJ = #{rksj,jdbcType=TIMESTAMP},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      LHBJCXJ = #{lhbjcxj,jdbcType=VARCHAR},
      KYBLXCQK = #{kyblxcqk,jdbcType=VARCHAR},
      XWBLWD = #{xwblwd,jdbcType=VARCHAR},
      JCRBH = #{jcrbh,jdbcType=VARCHAR},
      SFFXSXHJWFXW = #{sffxsxhjwfxw,jdbcType=VARCHAR},
      SXHJWFXWLX = #{sxhjwfxwlx,jdbcType=VARCHAR}
    where ZFID = #{zfid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.TXchjzf">
    update T_XCHJZF
    set WRYBH = #{wrybh,jdbcType=VARCHAR},
      WRYMC = #{wrymc,jdbcType=VARCHAR},
      KSSJ = #{kssj,jdbcType=TIMESTAMP},
      JSSJ = #{jssj,jdbcType=TIMESTAMP},
      JCR = #{jcr,jdbcType=VARCHAR},
      ZFZH = #{zfzh,jdbcType=VARCHAR},
      SSHBBM = #{sshbbm,jdbcType=VARCHAR},
      RWLX = #{rwlx,jdbcType=VARCHAR},
      JD = #{jd,jdbcType=VARCHAR},
      WD = #{wd,jdbcType=VARCHAR},
      WRYDZ = #{wrydz,jdbcType=VARCHAR},
      BZ = #{bz,jdbcType=VARCHAR},
      RKSJ = #{rksj,jdbcType=TIMESTAMP},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      JCRBH = #{jcrbh,jdbcType=VARCHAR},
      SFFXSXHJWFXW = #{sffxsxhjwfxw,jdbcType=VARCHAR},
      SXHJWFXWLX = #{sxhjwfxwlx,jdbcType=VARCHAR}
    where ZFID = #{zfid,jdbcType=VARCHAR}
  </update>
</mapper>