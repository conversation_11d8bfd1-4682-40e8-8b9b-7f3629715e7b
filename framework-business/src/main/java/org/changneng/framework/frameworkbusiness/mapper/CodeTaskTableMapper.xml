<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.CodeTaskTableMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.CodeTaskTable">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BELONG_AREA_ID" jdbcType="VARCHAR" property="belongAreaId" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BELONG_AREA_ID, TASK_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CODE_TASK_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CODE_TASK_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.CodeTaskTable">
    insert into CODE_TASK_TABLE (ID, BELONG_AREA_ID, TASK_ID
      )
    values (#{id,jdbcType=VARCHAR}, #{belongAreaId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CodeTaskTable">
   <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into CODE_TASK_TABLE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CodeTaskTable">
    update CODE_TASK_TABLE
    <set>
      <if test="belongAreaId != null">
        BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.CodeTaskTable">
    update CODE_TASK_TABLE
    set BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      TASK_ID = #{taskId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
   <select id="selectCodeTaskTableByCodeAndTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CODE_TASK_TABLE
    where 1=1
    <if test="code != null and code != ''">
       and  BELONG_AREA_ID = #{code,jdbcType=VARCHAR}
      </if>
      <if test="taskId != null and taskId !=''">
       and TASK_ID = #{taskId,jdbcType=VARCHAR}
      </if>
  </select>
  
</mapper>