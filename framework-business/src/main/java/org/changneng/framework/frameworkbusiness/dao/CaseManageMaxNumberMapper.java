package org.changneng.framework.frameworkbusiness.dao;

import java.util.HashMap;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.CaseManageMaxNumber;

public interface CaseManageMaxNumberMapper {
    int deleteByPrimaryKey(String id);

    int insert(CaseManageMaxNumber record);

    int insertSelective(CaseManageMaxNumber record);

    CaseManageMaxNumber selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CaseManageMaxNumber record);

    int updateByPrimaryKey(CaseManageMaxNumber record);
    
    CaseManageMaxNumber selectMaxWenHao(HashMap<String, String> map);
    
    int insertIntiMaxCode(HashMap<String, String> map);
    
    int updateMaxCode(HashMap<String, String> map);
    
    CaseManageMaxNumber selectDecisionNumberPrefix(@Param("belongAreaCode")String belongAreaCode,@Param("manageNumberType")String manageNumberType,@Param("year")String year);
}