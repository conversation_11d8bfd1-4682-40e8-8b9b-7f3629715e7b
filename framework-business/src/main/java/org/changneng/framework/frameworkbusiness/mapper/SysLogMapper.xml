<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SysLogMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SysLog">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LOGINID" jdbcType="VARCHAR" property="loginid" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="IP" jdbcType="VARCHAR" property="ip" />
    <result column="DBTYPE" jdbcType="VARCHAR" property="dbtype" />
    <result column="BUSINESSTYPE" jdbcType="VARCHAR" property="businesstype" />
    <result column="RESULT" jdbcType="VARCHAR" property="result" />
    <result column="OPTDATE" jdbcType="TIMESTAMP" property="optdate" />
    <result column="OPTSQL" jdbcType="VARCHAR" property="optsql" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="METHODNAME" jdbcType="VARCHAR" property="methodname" />
    <!-- 以下的是思路对接需要的字段 -->
    <result column="APP_CODE" jdbcType="VARCHAR" property="appCode" />
    <result column="OP_CODE" jdbcType="VARCHAR" property="opCode" />
    <result column="OP_LEVEL" jdbcType="VARCHAR" property="opLevel" />
    <result column="LOG_CONTENT" jdbcType="VARCHAR" property="logContent" />
    <result column="MODEL_CODE" jdbcType="TIMESTAMP" property="modelCodel" />
    <result column="MODEL_NAME" jdbcType="VARCHAR" property="modelName" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="USER_AGENT" jdbcType="VARCHAR" property="userAgent" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, LOGINID, USERNAME, IP, DBTYPE, BUSINESSTYPE, RESULT, OPTDATE, OPTSQL, REMARK, 
    METHODNAME,APP_CODE,OP_CODE,OP_LEVEL,LOG_CONTENT,MODEL_CODE,MODEL_NAME,URL,USER_AGENT
  </sql>
  <select id="queryLogList" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List"/>
  	from SYSLOG
  	<where>
  		<if test="search.ip != null and search.ip != ''">
  			IP like CONCAT('%',CONCAT(#{search.ip},'%'))
  		</if>
  		<if test="search.beginDate != null and search.beginDate != '' and search.endDate != null and search.endDate != ''">
  			and OPTDATE between to_date(#{search.beginDate},'yyyy-mm-dd hh24:mi:ss') and to_date(#{search.endDate},'yyyy-mm-dd hh24:mi:ss')
  		</if>
  	</where>
  	order by OPTDATE desc,ID asc
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SYSLOG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SYSLOG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SysLog">
    insert into SYSLOG (ID, LOGINID, USERNAME, 
      IP, DBTYPE, BUSINESSTYPE, 
      RESULT, OPTDATE, OPTSQL, 
      REMARK, METHODNAME,APP_CODE,OP_CODE,OP_LEVEL,LOG_CONTENT,MODEL_CODE,MODEL_NAME,URL,USER_AGENT)
    values (#{id,jdbcType=VARCHAR}, #{loginid,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{dbtype,jdbcType=VARCHAR}, #{businesstype,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{optdate,jdbcType=TIMESTAMP}, #{optsql,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{methodname,jdbcType=VARCHAR}
      , #{appCode,jdbcType=VARCHAR}
      , #{opCode,jdbcType=VARCHAR}, #{opLevel,jdbcType=VARCHAR}, #{logContent,jdbcType=VARCHAR}, #{modelCode,jdbcType=VARCHAR}
      , #{modelName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{userAgent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysLog">
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  </selectKey>
    insert into SYSLOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="loginid != null">
        LOGINID,
      </if>
      <if test="username != null">
        USERNAME,
      </if>
      <if test="ip != null">
        IP,
      </if>
      <if test="dbtype != null">
        DBTYPE,
      </if>
      <if test="businesstype != null">
        BUSINESSTYPE,
      </if>
      <if test="result != null">
        RESULT,
      </if>
      <if test="optdate != null">
        OPTDATE,
      </if>
      <if test="optsql != null">
        OPTSQL,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="methodname != null">
        METHODNAME,
      </if>
      <if test="appCode != null">
        APP_CODE,
      </if>
      <if test="opCode != null">
        OP_CODE,
      </if>
      <if test="opLevel != null">
        OP_LEVEL,
      </if>
      <if test="logContent != null">
        LOG_CONTENT,
      </if>
      <if test="modelCode != null">
        MODEL_CODE,
      </if>
      <if test="modelName != null">
        MODEL_NAME,
      </if>
      <if test="url != null">
        URL,
      </if>
      <if test="userAgent != null">
        USER_AGENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="loginid != null">
        #{loginid,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="dbtype != null">
        #{dbtype,jdbcType=VARCHAR},
      </if>
      <if test="businesstype != null">
        #{businesstype,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="optdate != null">
        #{optdate,jdbcType=TIMESTAMP},
      </if>
      <if test="optsql != null">
        #{optsql,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="methodname != null">
        #{methodname,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null">
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="opLevel != null">
        #{opLevel,jdbcType=VARCHAR}, 
      </if>
      <if test="logContent != null">
        #{logContent,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        #{userAgent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysLog">
    update SYSLOG
    <set>
      <if test="loginid != null">
        LOGINID = #{loginid,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        IP = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="dbtype != null">
        DBTYPE = #{dbtype,jdbcType=VARCHAR},
      </if>
      <if test="businesstype != null">
        BUSINESSTYPE = #{businesstype,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        RESULT = #{result,jdbcType=VARCHAR},
      </if>
      <if test="optdate != null">
        OPTDATE = #{optdate,jdbcType=TIMESTAMP},
      </if>
      <if test="optsql != null">
        OPTSQL = #{optsql,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="methodname != null">
        METHODNAME = #{methodname,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null">
        APP_CODE = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        OP_CODE = #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="opLevel != null">
        OP_LEVEL = #{opLevel,jdbcType=VARCHAR}, 
      </if>
      <if test="logContent != null">
        LOG_CONTENT = #{logContent,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null">
        MODEL_CODE = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        MODEL_NAME = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        USER_AGENT = #{userAgent,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.SysLog">
    update SYSLOG
    set LOGINID = #{loginid,jdbcType=VARCHAR},
      USERNAME = #{username,jdbcType=VARCHAR},
      IP = #{ip,jdbcType=VARCHAR},
      DBTYPE = #{dbtype,jdbcType=VARCHAR},
      BUSINESSTYPE = #{businesstype,jdbcType=VARCHAR},
      RESULT = #{result,jdbcType=VARCHAR},
      OPTDATE = #{optdate,jdbcType=TIMESTAMP},
      OPTSQL = #{optsql,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      METHODNAME = #{methodname,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
   
    <select id="getByDaySysLog" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
       ID, LOGINID, USERNAME, IP, DBTYPE, BUSINESSTYPE, RESULT, OPTDATE, OPTSQL, REMARK, METHODNAME,APP_CODE,OP_CODE,OP_LEVEL,LOG_CONTENT,MODEL_CODE,MODEL_NAME,URL,USER_AGENT
    from SYSLOG
    where OPTDATE &lt;= to_date(#{endDate},'yyyy-MM-dd')  and OPTDATE  &gt; to_date(#{startDate},'yyyy-MM-dd') 
  </select>
  
</mapper>