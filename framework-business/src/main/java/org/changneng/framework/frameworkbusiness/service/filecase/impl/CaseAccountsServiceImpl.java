package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import com.github.pagehelper.PageHelper;
import org.changneng.framework.frameworkbusiness.dao.filecase.*;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResult;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResultBean;
import org.changneng.framework.frameworkbusiness.service.filecase.CaseAccountsService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 案件台账业务处理
 * @ClassName: CaseAccountsServiceImpl 
 * @Description: 
 * <AUTHOR>
 * @date 2017年8月15日 下午2:06:33 
 *
 */
@Service
public class CaseAccountsServiceImpl implements CaseAccountsService {
	
	@Autowired
	private AtvSanctionCaseMapper atvSanctionCaseMapper;
	
	@Autowired
	private LimitStopProductMapper limitStopProductMapper;
	
	@Autowired
	private SequestrationInfoMapper sequestrationInfoMapper;
	
	@Autowired
	private PollutionCrimeMapper pollutionCrimeMapper;

	@Autowired
	private ApplyForceMapper applyForceMapper;
	
	@Autowired
	private PenaltyDayMapper penaltyDayMapper;
	
	@Autowired
	private CaseBaseInfoMapper caseBaseInfoMapper;
	
	@Override
	public PageBean<AjtzSearchResultBean> getArjfList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<>(penaltyDayMapper.selectCaseAccountsInfo(searchBean, sysUsers.getBelongAreaId()));
	}

	@Override
	public List<AjtzSearchResultBean> getArjfDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		searchBean = transferSearchBean(searchBean);
		return penaltyDayMapper.selectCaseAccountsInfo(searchBean, sysUsers.getBelongAreaId());
	}
	
	@Override
	public List<AjtzSearchResultBean> selectDownList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		return transferAttr(atvSanctionCaseMapper.selectCaseAccountsInfo(searchBean, sysUsers.getBelongAreaId()));
	}
	
	@Override
	public PageBean<AjtzSearchResultBean> getAtvCaseList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<>(transferAttr(atvSanctionCaseMapper.selectCaseAccountsInfo(searchBean, sysUsers.getBelongAreaId())));
	}

	@Override
	public PageBean<AjtzSearchResultBean> getXctcList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		String belongAreaCode = sysUsers.getBelongAreaId();
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		List<AjtzSearchResultBean> xctcList = limitStopProductMapper.getXctcList(searchBean, belongAreaCode);
		return new PageBean<AjtzSearchResultBean>(xctcList);
	}

	@Override
	public List<AjtzSearchResultBean> selectCfkyDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		searchBean = transferSearchBean(searchBean);
		return sequestrationInfoMapper.getCfkyList(searchBean, sysUsers.getBelongAreaId());
	}

	@Override
	public List<AjtzSearchResultBean> selectXctcDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		searchBean = transferSearchBean(searchBean);
		return limitStopProductMapper.getXctcList(searchBean, sysUsers.getBelongAreaId());
	}
    /**
     * backlog1.5.4需求变更，此方法废弃不再用
     * @param searchBean
     * @return
     * @throws Exception
     * <AUTHOR>
     */
	private AjtzSearchBean transferSearchBean(AjtzSearchBean searchBean) throws Exception{
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateMonth()) {
			case "01":
			case "03":
			case "05":
			case "07":
			case "08":
			case "10":
			case "12":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-31");
				break;
			case "02":
				boolean tf = DateUtil.getSpecialParticularYear(Integer.parseInt(searchBean.getCaseCreateYear()));
				if(tf){
					searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
					searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-29");
					break;
				}else{
					searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
					searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-28");
					break;
				}
			case "04":
			case "06":
			case "09":
			case "11":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-30");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-03-31");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-06-30");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-09-30");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
				break;
			default:
				break;
			}
		}/*else if(!ChangnengUtil.isNull(searchBean.getCaseCreateYear()) && ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && ChangnengUtil.isNull(searchBean.getCaseCreateMonth())){
			//月度和季度都为空，年度不为空
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		}*//*else if (ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
		   
			//throw new Exception("参数异常，下载失败");
		}*/
		else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		return searchBean;
	}

	@Override
	public PageBean<AjtzSearchResultBean> getHjwrfzList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		List<AjtzSearchResultBean> xctcList = pollutionCrimeMapper.getHjwrfzList(searchBean, sysUsers.getBelongAreaId());
		return new PageBean<AjtzSearchResultBean>(xctcList);
	}

	@Override
	public List<AjtzSearchResultBean> selectHjwrfzDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		searchBean = transferSearchBean(searchBean);
		List<AjtzSearchResultBean> xctcList = pollutionCrimeMapper.getHjwrfzList(searchBean, sysUsers.getBelongAreaId());
		return xctcList;
	}
	
	@Override
	public PageBean<AjtzSearchResultBean> getQzzxList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		List<AjtzSearchResultBean> xctcList = applyForceMapper.getQzzxList(searchBean, sysUsers.getBelongAreaId());
		return new PageBean<AjtzSearchResultBean>(xctcList);
	}

	@Override
	public List<AjtzSearchResultBean> getQzzxDownList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		List<AjtzSearchResultBean> xctcList = applyForceMapper.getQzzxList(searchBean, sysUsers.getBelongAreaId());
		return xctcList;
	}

	/**
	 * 导出属性转换
	 * 
	 * @param resultList
	 * @return
	 */
	private List<AjtzSearchResultBean> transferAttr(List<AjtzSearchResultBean> resultList){
		for (int i = 0; i < resultList.size(); i++) {
			AjtzSearchResultBean ajtzSearchResultBean = resultList.get(i);
			
			if (!ChangnengUtil.isNull(ajtzSearchResultBean.getPunishBasis()) && ajtzSearchResultBean.getPunishBasis().length()>0) {
				StringBuffer sb = new StringBuffer();

				String str = ajtzSearchResultBean.getPunishBasis();
				if (!"".equals(str) && str != null) {
					String[] split = str.split("&&");
					for (String string : split) {
						String[] split2 = string.split("[$]");
						try {
							sb.append(split2[1]);
						} catch (Exception e) {
							System.out.println(e);
							continue;
						}
					}
					ajtzSearchResultBean.setPunishBasis(sb.toString());
				}
			}
		}
		
		return resultList;
	}

	@Override
	public PageBean<AjtzSearchResultBean> getCaseBaseList(AjtzSearchBean searchBean) throws Exception {
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<AjtzSearchResultBean>(atvSanctionCaseMapper.selectCaseBaseInfoByObjectId(searchBean));
	}

	@Override
	public PageBean<AjtzSearchResultBean> getCaseList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		// TODO Auto-generated method stub
		Integer pageSize = searchBean.getPageSize();
		Integer pageNum = searchBean.getPageNum();
		if(ChangnengUtil.isNull(pageSize)){
		     pageSize = 10;
		}
		if(ChangnengUtil.isNull(pageNum)){
			pageNum=1;
		}
		PageHelper.startPage(pageNum, pageSize);
		//这段代码主要用于获取选择的专项行动id并组装成一个sql片段。
		if(!ChangnengUtil.isNull(searchBean.getSpecialAction())) {
			//and #{searchBean.specialAction} in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)
			StringBuffer sb = new StringBuffer();
			String[] ids = searchBean.getSpecialAction().split("-");
			for(int i = 0;i<ids.length;i++) {
				sb.append("'");
				sb.append(ids[i]);
				sb.append("' in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)");
				sb.append(" OR");
			}
			sb= sb.deleteCharAt(sb.length()-1);
			sb.deleteCharAt(sb.length()-1);
			//System.out.println(sb.toString());
			searchBean.setSpecialAction(sb.toString());
		}
		List<AjtzSearchResultBean> list = caseBaseInfoMapper.getAjtzResultList(searchBean, sysUsers.getBelongAreaId());
		return new PageBean<>(list);
	}



	@Override
	public List<AjtzSearchResultBean> getMainCaseList(AjtzSearchBean searchBean) throws Exception {
		// TODO Auto-generated method stub
		//这段代码主要用于获取选择的专项行动id并组装成一个sql片段。
		if(!ChangnengUtil.isNull(searchBean.getSpecialAction())) {
			//and #{searchBean.specialAction} in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)
			StringBuffer sb = new StringBuffer();
			String[] ids = searchBean.getSpecialAction().split("-");
			for(int i = 0;i<ids.length;i++) {
				sb.append("'");
				sb.append(ids[i]);
				sb.append("' in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)");
				sb.append(" OR");
			}
			sb= sb.deleteCharAt(sb.length()-1);
			sb.deleteCharAt(sb.length()-1);
			//System.out.println(sb.toString());
			searchBean.setSpecialAction(sb.toString());
		}
		List<AjtzSearchResultBean> list = caseBaseInfoMapper.getMainCaseList(searchBean);
		//
		if(list!=null && list.size() >0 ){
			for (AjtzSearchResultBean bean : list) {
				Double easyAmount = 0.0;
				Double generalAmount = 0.0;
				Double penaltyAmount = 0.0;
				if(bean.getEasyAmount() != null){
					easyAmount = Double.parseDouble(bean.getEasyAmount());
				}
				if(bean.getGeneralAmount() != null){
					generalAmount = Double.parseDouble(bean.getGeneralAmount());
				}
				if(bean.getPenaltyAmount() != null){
					penaltyAmount = Double.parseDouble(bean.getPenaltyAmount());
				}
				bean.setAllMoneyAmount(easyAmount+generalAmount+penaltyAmount);
			}
			//按AllMoneyAmount排序 取前5个
			Collections.sort(list, new Comparator<AjtzSearchResultBean>() {
				public int compare(AjtzSearchResultBean o1, AjtzSearchResultBean o2) {
					if(o2.getAllMoneyAmount() < o1.getAllMoneyAmount()){
						return -1;
					}else if(o2.getAllMoneyAmount() > o1.getAllMoneyAmount()){
						return 1;
					}
						return 0;
				}
			});
			list = list.subList(0,5);
			System.out.println("aaa");
		}
		return list;
	}

	@Override
	public List<AjtzSearchResultBean> getCaseDownloadList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		//这段代码主要用于获取选择的专项行动id并组装成一个sql片段。
		if(!ChangnengUtil.isNull(searchBean.getSpecialAction())) {
			//and #{searchBean.specialAction} in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)
			StringBuffer sb = new StringBuffer();
			String[] ids = searchBean.getSpecialAction().split("-");
			for(int i = 0;i<ids.length;i++) {
				sb.append("'");
				sb.append(ids[i]);
				sb.append("' in (select cs.special_action_id from case_special cs where cs.case_id = cb.id)");
				sb.append(" OR");
			}
			sb= sb.deleteCharAt(sb.length()-1);
			sb.deleteCharAt(sb.length()-1);
			//System.out.println(sb.toString());
			searchBean.setSpecialAction(sb.toString());
		}
		List<AjtzSearchResultBean> list = caseBaseInfoMapper.getAjtzResultList(searchBean, sysUsers.getBelongAreaId());
		return list;
	}

	@Override
	public PageBean<AjtzSearchResultBean> getfgwData(AjtzSearchBean searchBean,SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<>(transferAttr(atvSanctionCaseMapper.getfgwData(searchBean,sysUsers)));
	}

	@Override
	public List<AjtzSearchResultBean> selectFgwDownList(AjtzSearchBean searchBean,SysUsers sysUsers) throws Exception {
		searchBean = transferSearchBean(searchBean);
		return transferAttr(atvSanctionCaseMapper.getfgwData(searchBean,sysUsers));
	}

	@Override
	public PageBean<AjtzSearchResult> selectSupervisionCases(AjtzSearchBean searchBean) {
		PageHelper.startPage(searchBean.getPageNum(), searchBean.getPageSize());
		/*String lawID = caseBaseInfoMapper.getSupervise(searchBean.getDbdbh());
		if (org.apache.commons.lang.StringUtils.isNotEmpty(lawID)) {
			searchBean.setLawID(lawID);
			
		}*/
		return new PageBean<>(caseBaseInfoMapper.selectSupervisionCases(searchBean));
	}

	@Override
	public PageBean<AjtzSearchResultBean> getXFCaseBaseList(AjtzSearchBean searchBean) {
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<AjtzSearchResultBean>(atvSanctionCaseMapper.selectCaseBaseInfoByObjectName(searchBean));
	}

	@Override
	public PageBean<AjtzSearchResultBean> queryPwaseBaseList(AjtzSearchBean searchBean) {
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(10);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<AjtzSearchResultBean>(atvSanctionCaseMapper.queryPwaseBaseList(searchBean));
	}
}
