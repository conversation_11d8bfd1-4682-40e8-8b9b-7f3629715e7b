package org.changneng.framework.frameworkbusiness.utils;

public class ExeclBeanUtil {

	
	/**
	 * 综合台账导出表头
	 * ,"所在地市","所在区县","详细地址","权属行政区","对象类型","监管级别","任务来源","监察类型","限办时间","检查时间","关联专项行动"
	 */
	public static String [] taskGeneralColumn = new String[] {"执法编号","发起部门","执法对象名称","适用排污许可行业技术规范","是否属于“小散乱污”企业","当前环节","现场执法开始时间","现场执法结束时间","执法部门","检查人","经办人","任务类型",/*"特定专项监察",*/
 
			/*"是否存在违法行为",*/"任务状态","执法状态","所在行政区","详细地址","权属行政区","对象类型","监管级别","任务来源","执法发起年度","执法发起季度","双随机属性","监察类型","限办时间","开始办理时间","办结时间","关联专项行动","是否发现涉嫌环境违法行为","违法行为涉及环境要素","涉嫌环境违法行为类型","拟处罚措施","固定污染源排污许可分类","管理类型","是否发证","许可证编号","发证部门","拟处罚金额","是否排污许可证专项执法","处置类型"};
	/**
	 * 综合台账导出数据库列名
	 */
	public static String [] taskGeneralDB  = new String[] {"taskNumber","creatDepartmentName","lawObjectName","sypwxkhyjsgfName","xslw","nodeName","lawEnforcementStartTime","lawEnforcementEndTime","handlingUnitName","checUserNames","handleUserName","taskLawobjectStatus",/*"taskFromTypeName",*/
			/*"isIllegalact",*/"nodeTimeoutName","taskStateName","belongAreaName","address","powerAreaName","typeName","levelName","taskFromName","belongYear","quarter","attrName","monitorTypeName","limitTime","processDate","begEndTime","specialActionNames","isIllegalact","taskLlegalName","environmentalViolationsName","proposedPenaltyMeasuresName","sewageClassifyName","managementType","isCertification","licenseNumber","certifyingAuthority","penaltyAmount","isSpecials","disposeType"};
	
	/**
	 * 问题反馈导出excel表头
	 */
	public static String [] feedbackColumn = new String[] {
			"问题编号","创建-问题类型","创建-所属系统、模块","主题","描述","环境_操作系统名称","环境_浏览器名称","联系人","联系电话","创建时间","问题创建用户","创建用户所属单位",
			"当前状态","当前状态创建时间","优先级","受理-所属系统、模块确认","受理-问题归属名称","受理-问题类型确认","受理时间","计划解决时间","实际解决时间","实际工作量","问题解决备注信息"
	};
	
	/**
	 * 问题反馈导出excel数据库对应列名
	 */
	public static String [] feedbackDb = new String[] {
			"quesNumber","quesTypeName","belongModule","theme","quesDesc","systemName","browserName","contact","contactPhone","creatDate","creatUserName","belongDepartName",
			"state","stateDate","quesLevel","confirmBelongModule","quesOwnerName","confirmQuesTypeName","acceptDate","planSolveDate","actualSolveDate","workload","solveRemark"
	};
	
	/**
	 * 已下发任务导出列明
	 */
	public static String [] randomTaskColumn = new String []{"任务所属年份","季度","执法对象名称","任务状态","对象来源双随机库","现场检查时间","双随机执法人员","执法对象地址","联系人","联系方式","统一社会信用代码","组织机构代码",
			"污染源编码","经度","纬度","行业类型","监管级别","生产状态","当事人性质","所属级别名称","股票代码","所属集团公司名称","是否所属集团公司","所属集团公司组织机构代码","所属集团公司股票代码","排污口是否规范化","所属流域代码","所属流域名称"
																	};
	
	/**
	 * 已下发任务导出数据
	 */
	public static String [] randomTaskDB = new String []{"belongYear","quarter","objectName","taskStateName","attrName","taskCheckTime","extractedPersonNames","address","linkman","legalPhone","socialCreditCode","orgCode",
			"wasteID","gisCoordinateX","gisCoordinateY","industryTypeName","levelName","productStateName","personNatureName","belonglevelName","stockCode","groupCompanyName","isGroupCompany","groupCompanyCode","groupCompanyStockCode","isOutFallStandard","WSCD","WSNM"
															};
	
	
	/**
	 * 行政处罚：导出的表列名称
	 */
	public static String [] caseXzcfColumn = new  String []{""};
	
	/**
	 * 行政处罚：导出的表列数据库名称
	 */
	public static String [] caseXzcfDB = new  String []{""};
	
	
	
	/**
	 * 大案件
	 */
	public static String [] dnjColumn = new String[] {
			"大案件编号","案件名称","当事人名称","处罚主体","调查机构","案由","案件状态",
			"关联专项行动","违法类型","案件创建时间","有无简易行政处罚","有无一般行政处罚",
			"有无行政命令","有无查封扣押","有无限产停产","有无移送行政拘留","有无移送涉嫌犯罪","按日连续处罚次数"
	};
 
	public static String [] dnjDBname = new String[] {
			"caseNumber","caseName","lawObjectName","punishSubject","researchOrgName",
			"caseReason","caseStatus","specialActions","illegalTypes","createTime",
			"smrProgramState","cmlPunishState","astnDecState","attachmentState",
			"lmtProductionState","detentionState","evmtPollutionState","pdcount"
	};
	
	/**
	 * 行政处罚
	 */
	public static String [] xzcfColumn = new String[] {
			"实施单位","实施对象","处罚案件类型","案件创建时间","立案日期","立案号","销案时间",
			"是否处罚","不处罚原因","决定书文号","本系统对应违法行为","环保部对应违法行为",
			"处罚依据","处罚种类","罚款数额（万元）","是否举行听证","决定下达日期","是否建设项目相关案件",
			"审批单位","项目名称","项目建设时间","执行情况","执行完毕日期","复议情况","诉讼情况","移送情况",
			"具体情形","是否纳入银行征信系统","结案日期","案卷号","是否公开","信息公开情况","污染源","行业类别","大案件编号",
			"行政处罚案件编号","案件状态","行政处罚案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"
	};
 
	public static String [] xzcfDBname = new String[] {
			"punishSubject","lawObjectName","caseType","createTime","filingDate",
			"filingNumber","removeCaseDate","isPunish","noPunishReasons","decisionNumber",
			"majorViolationsName","entProtectionName","punishBasis","punishType","penaltyAmount",
			"atvIsHearing","decisionDate","isProject","approvalCompany","projectName","projectCreatorDate",
			"implementationName","impleOverDate","reviewSituation","litigationSituation","transferName","specificSituation",
			"isBank","closeCaseDate","closeCaseNumber","isOpen","otherPublicDesc","littleCaseTypeName","industryTypeName",
			"mainCaseNumber","sanctionNumber","caseStatus","isComplete","upState","informantName","submitTime"
	};
	
	
	/**
	 * 行政命令
	 */
	public static String [] xzmlColumn = new String[] {
			"实施单位","实施对象","案件创建时间","行政命令种类","决定书文号","本系统对应违法行为","违反法律条款",
			"污染源","行业类别","大案件编号","行政命令案件编号","案件状态","行政命令案件完整性","填报人","完成案卷时间"
	};
 
	public static String [] xzmlDBname = new String[] {
			"punishSubject","lawObjectName","createTime","orderTypeName","decisionNumber","majorViolationsName",
			"punishBasis","illegalCaseType","industryTypeName","mainCaseNumber","sanctionNumber","caseStatus","isComplete","informantName","generateTime"
	};
	
	/**
	 * 查封扣押
	 */
	public static String [] cfkyColumn = new String[] {
			"实施单位","实施对象","案件创建时间","主要违法行为","适用情形","决定情况","实施期限","查封、扣押的设施及设备",
			"相关的行政处罚决定","是否公开","信息公开情况","备注","污染源","行业类别","大案件编号","查封扣押案件编号",
			"案件状态","案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"
	};
 
	public static String [] cfkyDBname = new String[] {
			"punishSubject","lawObjectName","createTime","majorViolationsNames","adaptSituationName",
			"sequestrationNumber","delayReason","detentionDevice","atvDecisionNumber","isOpen","openTime",
			"remark","littleCaseTypeName","industryTypeName","mainCaseNumber",
			"caseNumber","attachmentState","isComplete","upState","informantName","submitTime"

	};
	
	/**
	 * 限产停产
	 */
	public static String [] xctcColumn = new String[] {
			"实施单位","实施对象","实施措施类型","案件创建时间","主要违法行为","适用情形","决定情况","实施期限","执行情况",
			"解除情况","跟踪检查情况","执行情况","是否公开","信息公开情况","备注","污染源","行业类别","大案件编号","限产停产案件编号",
			"案件状态","限产停产案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"
	};
 
	public static String [] xctcDBname = new String[] {
			"punishSubject","lawObjectName","actionType","createTime","majorViolations","adaptSituationName",
			"decisionNumber","isDelay","implementationName","releaseSituation","checkSituation","punishTypeName",
			"isOpen","openTime","remark","littleCaseTypeName","industryTypeName","mainCaseNumber","caseNumber",
			"caseStatus","isComplete","upState","informantName","submitTime"
	};
	
	/**
	 * 行政拘留
	 */
	public static String [] xzjlColumn = new String[] {
			"移送单位","移送时间","涉案当事人","案件创建时间","简要案情","适用情形","环保部门实施行政处罚情况",
			"公安机关受案和行政拘留情况","移送案卷编号","备注","污染源","行业类别","大案件编号","行政拘留案件编号",
			"案件状态","行政拘留案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"

	};
 
	public static String [] xzjlDBname = new String[] {
			"punishSubject","transferTime","lawObjectName","createTime","briefCase","adaptSituationName",
			"implementationName","detentionNumber","transferNumber","remark","littleCaseTypeName",
			"industryTypeName","mainCaseNumber","caseNumber","caseStatus","isComplete","synchroStatus","informantName","submitTime"
	};
	
	/**
	 * 环境污染犯罪
	 */
	public static String [] hjwrfzColumn = new String[] {
			"移送单位","移送时间","涉案当事人","案件创建时间","简要案情","环保部门实施行政处罚情况","移送理由","接收移送司法机关",
			"是否受理","移送案卷编号","备注","污染源","行业类别","大案件编号","环境污染犯罪案件编号","案件状态",
			"环境污染犯罪案件完整性","是否提交环保部","填报人","完成提交日期","入库日期"
	};
 
	public static String [] hjwrfzDBname = new String[] {
			"transferDepartment","transferDate","lawObjectName","createDate","briefCase","implementationName",
			"transferReason","acceptTransfer","isAccept","transferCaseNumber","remark","illegalTypeName",
			"industryTypeName","mainCaseNumber","caseNumber","attachmentState","isComplete","upState","informantName","submitTime"
	};
	
	/**
	 * 其他移送
	 */
	public static String [] qtysColumn = new String[] {
			"移送单位","移送时间","涉案当事人","案件创建时间","移送移交机关","移送原因","移送案卷编号",
			"污染源","行业类别","大案件编号","其他移送案件编号","案件状态","其他移送案件完整性","填报人","完成案卷时间"
	};
 
	public static String [] qtysDBname = new String[] {
			"punishSubject","transfTime","lawObjectName","createTime","transfOfficeName",
			"transfReason","transfDossierId","punishType","industryTypeName","mainCaseNumber",
			"sanctionNumber","caseStatus","isComplete","informantName","generateTime"

	};
	
	/**
	 * 按日计罚
	 */
	public static String [] arjfColumn = new String[] {
			"处罚单位","处罚对象","案件创建时间","主要违法行为","适用情形","原处罚决定","按日连续处罚决定","计罚日数",
			"按日计罚罚款数额（万元","是否公开","信息公开情况","执行情况","执行完毕日期","污染源","行业类别","大案件编号",
			"按日计罚案件编号","案件状态","按日计罚案案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"
	};
 
	public static String [] arjfDBname = new String[] {
			"punishSubject","lawObjectName","createTime","illegalAction","applyName","penaltyAmount","penaltyDecisionDate",
			"penaltyDate","dayPenaltyAmount","isOpen","otherPublicDesc","implementationName","impleOverDate",
			"littleCaseTypeName","industryTypeName","mainCaseNumber","penaltyCaseNumber","caseStatus","isComplete","upState","informantName","submitTime"
	};
	
	
	/**
	 * 申请法院强制执行
	 */
	public static String [] sqfyColumn = new String[] {
			"申请单位","涉案当事人","案件创建时间","移送案件编号","申请时间","申请执行理由","申请执行事项","法院受理情况",
			"行业类别","大案件编号","申请法院强制执行案件编号","案件状态","案件完整性","是否提交环保部","填报人","完成提交时间","入库日期"

	};
 
	public static String [] sqfyDBname = new String[] {
			"applyDepartment","lawObjectName","createDate","transferCaseNumber","applyDate","applyReason",
			"applyMatter","isAccept","industryTypeName","mainCaseNumber","caseNumber","caseStatus","isComplete","upState","informantName","submitTime"
	};
	
}
