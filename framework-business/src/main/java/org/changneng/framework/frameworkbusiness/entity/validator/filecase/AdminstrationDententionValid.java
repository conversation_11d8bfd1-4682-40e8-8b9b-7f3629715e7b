package org.changneng.framework.frameworkbusiness.entity.validator.filecase;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

/**
 * 申请法院强制执行实体类后台校验注解
 * @ClassName: ApplyForceValid 
 * @Description: 
 * <AUTHOR>
 * @date 2017年8月8日 下午2:42:59 
 *
 */
@Target({ FIELD, METHOD, PARAMETER,TYPE,ANNOTATION_TYPE })
@Retention(RUNTIME)
@Constraint(validatedBy =AdminstrationDententionValidator.class)
@Documented
public @interface AdminstrationDententionValid {
	String message() default "信息校验失败"; 
	 
	Class<?>[] groups() default { };
	 
	Class<? extends Payload>[] payload() default { };
	 
	@Target({ FIELD, METHOD, PARAMETER, ANNOTATION_TYPE })  
	@Retention(RUNTIME)  
	@Documented  
	@interface List {  
		AdminstrationDententionValid[] value();  
	}  
}
