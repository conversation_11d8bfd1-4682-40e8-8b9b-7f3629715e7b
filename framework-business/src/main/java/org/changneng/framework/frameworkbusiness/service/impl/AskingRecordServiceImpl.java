package org.changneng.framework.frameworkbusiness.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.changneng.framework.frameworkbusiness.dao.AskingContentMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingCustomItemMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingCustomModelMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingDefaultModelMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingItemDatabaseMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingRecordMapper;
import org.changneng.framework.frameworkbusiness.dao.AskingUsuallyModelMapper;
import org.changneng.framework.frameworkbusiness.dao.ScanningAttachmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SurveyRecordMapper;
import org.changneng.framework.frameworkbusiness.dao.SysDepartmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.dao.TcDictionaryMapper;
import org.changneng.framework.frameworkbusiness.entity.AskingContent;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomBean;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomItem;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomModel;
import org.changneng.framework.frameworkbusiness.entity.AskingDefaultModel;
import org.changneng.framework.frameworkbusiness.entity.AskingItemDatabase;
import org.changneng.framework.frameworkbusiness.entity.AskingRecord;
import org.changneng.framework.frameworkbusiness.entity.AskingRecordPdf;
import org.changneng.framework.frameworkbusiness.entity.AskingUsuallyModel;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.LawObjectTypeBean;
import org.changneng.framework.frameworkbusiness.entity.ModelerLocationBean;
import org.changneng.framework.frameworkbusiness.entity.ScanningAttachment;
import org.changneng.framework.frameworkbusiness.entity.SysDepartment;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.Task;
import org.changneng.framework.frameworkbusiness.entity.historyLawEnforceObject;
import org.changneng.framework.frameworkbusiness.pdf.GeneratePdfService;
import org.changneng.framework.frameworkbusiness.service.AskingRecordService;
import org.changneng.framework.frameworkbusiness.service.SystemCodingService;
import org.changneng.framework.frameworkbusiness.service.ZfdxManagerService;
import org.changneng.framework.frameworkbusiness.utils.ImageUtil;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.CheckFileFormatTools;
import org.changneng.framework.frameworkcore.utils.Const;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.ExcelReader;
import org.changneng.framework.frameworkcore.utils.FastDFSClient;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.ResourceLoader;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;

@Service("askingRecordService")
public class AskingRecordServiceImpl implements AskingRecordService {

	@Autowired
	private AskingRecordMapper askingMapper;

	@Autowired
	private AskingContentMapper contentMapper;

	@Autowired
	private TaskMapper taskMapper;

	@Autowired
	private GeneratePdfService generatePdfService;

	@Autowired
	private ScanningAttachmentMapper attachmentMapper;
	
	@Autowired
	private SysFilesMapper sysfilesMapper;
	
	@Autowired 
	private TcDictionaryMapper tcDictionaryMapper;
	
	@Autowired
	private SurveyRecordMapper surveyRecordMapper;
	
	@Autowired
	private SysDepartmentMapper sysDepartmentMapper;
	
	@Autowired
	private AskingCustomModelMapper askCustomMapper;

	@Autowired
	private AskingUsuallyModelMapper usuallyModelMapper; 
	
	@Autowired
	private AskingDefaultModelMapper defaultModelMapper;
	
	@Autowired
	private AskingCustomItemMapper customItemMapper; 
	
	@Autowired
	private AskingItemDatabaseMapper itemDatabaseMapper; 
	
	@Autowired
	private SystemCodingService systemCodingService;
	
	@Autowired
	private ZfdxManagerService zfdxManagerService;
	
	@Override
	public AskingRecord getAskingRecordByUpdateDataAndTaskId(String taskId,String parentUrl ) throws Exception {
		AskingRecord askingRecord = null;
		try {
			askingRecord = askingMapper.selectAskingRecordByUpdateDataAndTaskId(taskId);
			if (askingRecord == null) {
				// 若没有查到，则说明是第一次新增
				return askingRecord;
			}
			if ("0".equals(parentUrl)) { // 表示历史途径进入
				List<AskingContent> contentList = contentMapper.selectAskingContentByAskId(askingRecord.getId());
				askingRecord.setContentList(contentList);
			}
		} catch (Exception e) {
			throw e;
		}
		return askingRecord;
	}

	@Override
	public AskingRecord getAskingRecordById(String id) {
		AskingRecord askingRecord = askingMapper.selectByPrimaryKey(id);
		List<AskingContent> contentList = contentMapper.selectAskingContentByAskId(askingRecord.getId());
		askingRecord.setContentList(contentList);
		return askingRecord;
	}

	@Override
	public int deleteAskingContent(String contentId) {
		return contentMapper.deleteByPrimaryKey(contentId);
	}

	@Override
	public int insertSelective(AskingContent content) {
		return contentMapper.insertSelective(content);
	}

	@Override
	public int updateByPrimaryKeySelective(AskingContent content) {
		return contentMapper.updateByPrimaryKeySelective(content);
	}

	@Override
	public int deleteAskingRecordById(String id) {
		return askingMapper.deleteByPrimaryKey(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public int instrtAskingRecord(AskingRecord record) throws Exception {
		return askingMapper.insertSelective(record);
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int updateAskingRecord(AskingRecord record) throws Exception {
		int countNum = 0;
		String docUrl = record.getDocUrl();
		if(record.getId()!=null && !"".equals(record.getId())){
			AskingRecord askingRecord = askingMapper.selectByPrimaryKey(record.getId());
			if(askingRecord!=null){
				docUrl = askingRecord.getDocUrl();	
			}
			
		}
		// 文件管理器
		FastDFSClient fDfs = new FastDFSClient("classpath:fdfs_client.conf");
		try {
			// 修改 保存新的模版信息 覆盖并且删除之前模版信息
			AskingRecordPdf pdfBean = new AskingRecordPdf();
			if(record.getId()!=null && !"".equals(record.getId())){
				// 
				if(!ChangnengUtil.isNull(record.getJsonContent())){
					List<AskingContent> contentList = JacksonUtils.toCollection(record.getJsonContent(), new TypeReference<List<AskingContent>>() {});
					if(contentList!=null && contentList.size()>0){
						for (int i = 0; i < contentList.size(); i++) {
							AskingContent item = contentList.get(i);
							item.setAskingId(record.getId());
							item.setLoction((i+1));
							contentMapper.updateByPrimaryKeySelective(item);
						}
					}
				}
				
				List<AskingContent> contentList = contentMapper.selectAskingContentByAskId(record.getId());
				record.setContentList(contentList);
			}
			BeanUtils.copyProperties(record, pdfBean);

			String startDateStr = "";
			String endDateStr = "";
			if(record.getAskingStartDate()!=null){
				startDateStr = DateUtil.getSimpleFormate(record.getAskingStartDate());
			}
			if(record.getAskingEndDate()!=null){
				endDateStr = DateUtil.getSimpleFormate(record.getAskingEndDate());
			}
			// 格式化输出时间
			if (startDateStr != null && !"".equals(startDateStr)) {
				pdfBean.setStartYear(startDateStr.substring(0, 4));
				pdfBean.setStartMouth(startDateStr.substring(5, 7));
				pdfBean.setStartDay(startDateStr.substring(8, 11));
				pdfBean.setStartHour(startDateStr.substring(11, 13));
				pdfBean.setStartMinute(startDateStr.substring(14));
			}
			if (!"".equals(endDateStr) && endDateStr != null) {
				pdfBean.setEndYear(endDateStr.substring(0, 4));
				pdfBean.setEndMouth(endDateStr.substring(5, 7));
				pdfBean.setEndDay(endDateStr.substring(8, 11));
				pdfBean.setEndHour(endDateStr.substring(11, 13));
				pdfBean.setEndMinute(endDateStr.substring(14));

			}
			
			if(pdfBean.getAskingUserNames() !=null && pdfBean.getLawEnforcIds() !=null ){
				String userAndLaw="";
				String userList[] = pdfBean.getAskingUserNames().split(",");
				String lawLit [] = pdfBean.getLawEnforcIds().split(",");
				for (int i = 0; i < 100; i++) {
					if(i>=userList.length || i>=lawLit.length){
						break;
					}
					if(userList[i]== null || lawLit[i]==null){
						break;
					}
					userAndLaw += userList[i]+":"+lawLit[i]+",";
				}
				pdfBean.setUserAndLawInfo(userAndLaw.substring(0,userAndLaw.length()-1));
	 
			} 
	 
			pdfBean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg(""));
			
			//由于html中纯数字和英文版的逗号 不会自动换行 所以将英文逗号转换为中文逗号 处理自动换行问题。
	     	//执法证号（告知事项部分）
	     	if(pdfBean.getLawEnforcNumber()!=null && !pdfBean.getLawEnforcNumber().equals("")) {
	     		String temp = pdfBean.getLawEnforcNumber();
	     		pdfBean.setLawEnforcNumber(temp.replaceAll(",", "，"));
	     	}
			
			//判断是新增还是修改
			if(record.getId()==null || "".equals(record.getId())){
				countNum = askingMapper.insertSelective(record);
				// 初始化询问笔录问答信息
				if(record.getAskType()!=null && 0==record.getAskType()){
					if(!ChangnengUtil.isNull(record.getJsonContent())){
						List<AskingContent> contentList = JacksonUtils.toCollection(record.getJsonContent(), new TypeReference<List<AskingContent>>() {});
						Date data =  new Date();
						if(contentList!=null && contentList.size()>0){
							for (int i = 0; i < contentList.size(); i++) {
								AskingContent item = contentList.get(i);
								item.setAskingId(record.getId());
								item.setCreateDate(data);
								item.setTaskId(record.getTaskId());
								item.setLoction((i+1));
								data = addOneSecond(data);
								contentMapper.insertSelective(item);
							}
							pdfBean.setContentList(contentList);
						}
					}
				}
				//行距倍数
				if(!ChangnengUtil.isNull(record.getMultiple())) {
					pdfBean.setMultiple(record.getMultiple());
				}else {
					pdfBean.setMultiple("30");
					record.setMultiple("30");
				}
				String newUrl = generatePdfService.generateAskingRecordPdf(pdfBean,
						File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localAskingModel.html");
				String url = fDfs.uploadFile(newUrl);
				record.setDocUrl(url);
				countNum = askingMapper.updateByPrimaryKeySelective(record);
			}else{
				//行距倍数
				if(!ChangnengUtil.isNull(record.getMultiple())) {
					pdfBean.setMultiple(record.getMultiple());
				}else {
					pdfBean.setMultiple("30");
					record.setMultiple("30");
				}
				String newUrl = generatePdfService.generateAskingRecordPdf(pdfBean,
						File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localAskingModel.html");
				String url = fDfs.uploadFile(newUrl);
				record.setDocUrl(url);
				countNum = askingMapper.updateByPrimaryKeySelective(record);
			}
			if (!"".equals(docUrl) && docUrl != null) {
				//fDfs.delete_file(Const.GROUP, docUrl);
				//fDfs.delete_file(docUrl.substring(0, docUrl.indexOf("/")), docUrl.substring(7));
			}
		} catch (Exception e) {
			throw e;
		}
		return countNum;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int generateAskingRecord(AskingRecord record) throws Exception {
		int countNum = 0;
		String docUrl = record.getDocUrl();
		if(record.getId()!=null && !"".equals(record.getId())){
			AskingRecord askingRecord = askingMapper.selectByPrimaryKey(record.getId());
			if(askingRecord!=null){
				docUrl = askingRecord.getDocUrl();	
			}
			
		}
		// 文件管理器
		FastDFSClient fDfs = new FastDFSClient("classpath:fdfs_client.conf");
		try {
			// 修改 保存新的模版信息 覆盖并且删除之前模版信息
			AskingRecordPdf pdfBean = new AskingRecordPdf();
			if(record.getId()!=null && !"".equals(record.getId())){
				// 
				if(!ChangnengUtil.isNull(record.getJsonContent())){
					List<AskingContent> contentList = JacksonUtils.toCollection(record.getJsonContent(), new TypeReference<List<AskingContent>>() {});
					if(contentList!=null && contentList.size()>0){
						for (int i = 0; i < contentList.size(); i++) {
							AskingContent item = contentList.get(i);
							item.setAskingId(record.getId());
							item.setLoction((i+1));
							contentMapper.updateByPrimaryKeySelective(item);
						}
					}
				}
				
				List<AskingContent> contentList = contentMapper.selectAskingContentByAskId(record.getId());
				record.setContentList(contentList);
			}
			BeanUtils.copyProperties(record, pdfBean);

			String startDateStr = "";
			String endDateStr = "";
			if(record.getAskingStartDate()!=null){
				startDateStr = DateUtil.getSimpleFormate(record.getAskingStartDate());
			}
			if(record.getAskingEndDate()!=null){
				endDateStr = DateUtil.getSimpleFormate(record.getAskingEndDate());
			}
			// 格式化输出时间
			if (startDateStr != null && !"".equals(startDateStr)) {
				pdfBean.setStartYear(startDateStr.substring(0, 4));
				pdfBean.setStartMouth(startDateStr.substring(5, 7));
				pdfBean.setStartDay(startDateStr.substring(8, 11));
				pdfBean.setStartHour(startDateStr.substring(11, 13));
				pdfBean.setStartMinute(startDateStr.substring(14));
			}
			if (!"".equals(endDateStr) && endDateStr != null) {
				pdfBean.setEndYear(endDateStr.substring(0, 4));
				pdfBean.setEndMouth(endDateStr.substring(5, 7));
				pdfBean.setEndDay(endDateStr.substring(8, 11));
				pdfBean.setEndHour(endDateStr.substring(11, 13));
				pdfBean.setEndMinute(endDateStr.substring(14));

			}
			
			if(pdfBean.getAskingUserNames() !=null && pdfBean.getLawEnforcIds() !=null ){
				String userAndLaw="";
				String userList[] = pdfBean.getAskingUserNames().split(",");
				String lawLit [] = pdfBean.getLawEnforcIds().split(",");
				for (int i = 0; i < 100; i++) {
					if(i>=userList.length || i>=lawLit.length){
						break;
					}
					if(userList[i]== null || lawLit[i]==null){
						break;
					}
					userAndLaw += userList[i]+":"+lawLit[i]+",";
				}
				pdfBean.setUserAndLawInfo(userAndLaw.substring(0,userAndLaw.length()-1));
	 
			} 
	 
			pdfBean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg(""));
			
			//由于html中纯数字和英文版的逗号 不会自动换行 所以将英文逗号转换为中文逗号 处理自动换行问题。
	     	//执法证号（告知事项部分）
	     	if(pdfBean.getLawEnforcNumber()!=null && !pdfBean.getLawEnforcNumber().equals("")) {
	     		String temp = pdfBean.getLawEnforcNumber();
	     		pdfBean.setLawEnforcNumber(temp.replaceAll(",", "，"));
	     	}
			
				countNum = askingMapper.insertSelective(record);
				// 初始化询问笔录问答信息
				if(record.getAskType()!=null && 0==record.getAskType()){
					if(!ChangnengUtil.isNull(record.getJsonContent())){
						List<AskingContent> contentList = JacksonUtils.toCollection(record.getJsonContent(), new TypeReference<List<AskingContent>>() {});
						Date data =  new Date();
						if(contentList!=null && contentList.size()>0){
							for (int i = 0; i < contentList.size(); i++) {
								AskingContent item = contentList.get(i);
								item.setAskingId(record.getId());
								item.setCreateDate(data);
								item.setTaskId(record.getTaskId());
								item.setLoction((i+1));
								data = addOneSecond(data);
								contentMapper.insertSelective(item);
							}
							pdfBean.setContentList(contentList);
						}
					}
				}
				//行距倍数
				if(!ChangnengUtil.isNull(record.getMultiple())) {
					pdfBean.setMultiple(record.getMultiple());
				}else {
					pdfBean.setMultiple("0");
					record.setMultiple("0");
				}
				String newUrl = generatePdfService.generateAskingRecordPdf(pdfBean,
						File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localAskingModel.html");
				String url = fDfs.uploadFile(newUrl);
				record.setDocUrl(url);
				countNum = askingMapper.updateByPrimaryKeySelective(record);
			if (!"".equals(docUrl) && docUrl != null) {
				//fDfs.delete_file(Const.GROUP, docUrl);
				//fDfs.delete_file(docUrl.substring(0, docUrl.indexOf("/")), docUrl.substring(7));
			}
		} catch (Exception e) {
			throw e;
		}
		return countNum;
	}

	@Override
	public AskingRecord getAskingRecordByRecordNameAndTaskId(String recordName, String taskId) throws Exception {
		if (recordName == null || "".equals(recordName)) {
			throw new BusinessException("询问笔录名称为空");
		}
		if (taskId == null || "".equals(taskId)) {
			throw new BusinessException("主干任务id为空");
		}
		return askingMapper.getAskingRecordByRecordNameAndTaskId(recordName.trim(), taskId.trim());
	}

	@Override
	public AskingRecord addNewAskingRecord(LawObjectTypeBean bean, SysUsers sysUsers) throws Exception {
		// TODO Auto-generated method stub
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		Task task = taskMapper.selectByPrimaryKey(bean.getTaskId());
	//	LawEnforceObjectWithBLOBs lawEnforceObject = zfdxManagerService.selectByPrimaryKey(task.getLawObjectId());
		historyLawEnforceObject lawEnforceObject = zfdxManagerService.selectByTaskId(bean.getTaskId());
		if (task == null) {
			throw new BusinessException("主干任务不存在");
		}
		AskingRecord askingRecord = new AskingRecord();
		askingRecord.setAskType(0); // 默认是系统录入
		askingRecord.setAddress(lawEnforceObject.getAddress());
		askingRecord.setPlace(lawEnforceObject.getAddress());
		askingRecord.setUnitName(lawEnforceObject.getObjectName());
		if("1".equals(lawEnforceObject.getTypeCode())){
			//企业，环保人和电话
			askingRecord.setAskedUserName(lawEnforceObject.getChargePerson());
			askingRecord.setPhone(lawEnforceObject.getChargePersonPhone());
		}else{
			askingRecord.setAskedUserName(lawEnforceObject.getLegalPerson());
			askingRecord.setPhone(lawEnforceObject.getLegalPhone());
		}
		if(!ChangnengUtil.isNull(lawEnforceObject.getCardNumber())&&(!ChangnengUtil.isNull(lawEnforceObject.getPersoncardTypeCode())||(!ChangnengUtil.isNull(lawEnforceObject.getCardTypeCode()))&&lawEnforceObject.getCardTypeCode().equals("4"))) {
			askingRecord.setCardId(lawEnforceObject.getCardNumber()); 
		}else if ("1".equals(lawEnforceObject.getTypeCode()) && !ChangnengUtil.isNull(lawEnforceObject.getChargeManIdCard())){
			askingRecord.setCardId(lawEnforceObject.getChargeManIdCard()); 
		}
		askingRecord.setRecordUser(sysUsers.getId());
		askingRecord.setRecordUserName(sysUsers.getLoginname());
		askingRecord.setAskingUserIds(task.getChecUserIds());
		askingRecord.setAskingUserNames(task.getChecUserNames());
		askingRecord.setLawEnforcIds(task.getLawEnforcIds());
		askingRecord.setLawEnforcNumber(task.getLawEnforcIds());
		askingRecord.setAskingUnitName(sysDepartmentMapper.selectByPrimaryKey(sysUsers.getBelongDepartmentId()).getDepartmentName());
		askingRecord.setAskingUnitId(sysUsers.getBelongDepartmentId()); 
		String hbjName = surveyRecordMapper.queryHBJNameByAreacode(sysUsers.getBelongAreaId());
		askingRecord.setAskDepartmentName(hbjName);
		SysDepartment sysDepartment = sysDepartmentMapper.selectFristDepartmentByNumber("D00"+sysUser.getBelongAreaId().substring(0,6),sysUser.getBelongAreaId());
		askingRecord.setMakeUnitName(sysDepartment.getDepartmentName());
		return askingRecord;
	}

	@Override
	public JsonResult readContentFroExcel(InputStream is,AskingRecord askRecord) throws Exception{
		JsonResult jsonResult=  new JsonResult();
		HSSFRow row = null;
		try {
			POIFSFileSystem fs = new POIFSFileSystem(is);
			
			HSSFSheet sheet;
			try {
				sheet = new HSSFWorkbook(fs).getSheetAt(1);
			} catch (Exception e) {
				e.printStackTrace();
				jsonResult.setResult(Const.RESULT_ERROR);
				jsonResult.setMessage("填写信息有误，请重新修改保存！");
				return jsonResult ;
			}
			
			String makeUnitName = "";
			String startTime = "";
			String endTime = "";
			String address = "";
			String place = "";
			String askedUserName = "";
			String sex = "";
			String age = "";
			String cardId = "";
			String unitName = "";
			String job = "";
			String phone = "";
			String postcode = "";
			String relationCaseName = "";
			String askingUserNames = "";
			String lawEnforcIds = "";
			String recordUserName = "";
			String askingUnitName = "";
			String askDepartmentName = "";
			String lawEnforcNumber = "";
			String participant="";
			// 文书制作单位
			row = sheet.getRow(0);
			makeUnitName = ExcelReader.getCellFormatValue(row.getCell(1)).trim();
			if(makeUnitName!=null && !"".equals(makeUnitName) ){
				askRecord.setMakeUnitName(makeUnitName);
			}else{
				throw new BusinessException("表头制作单位不能为空");
			}
			// 开始结束时间
			row = sheet.getRow(3);
			startTime = ExcelReader.getCellFormatValue(row.getCell(2)).trim() + "-"
					+ ExcelReader.getCellFormatValue(row.getCell(4)).trim() + "-"
					+ ExcelReader.getCellFormatValue(row.getCell(6)).trim() + " "
					+ ExcelReader.getCellFormatValue(row.getCell(8)).trim() + ":"
					+ ExcelReader.getCellFormatValue(row.getCell(10)).trim() + ":" + "00";
			endTime = ExcelReader.getCellFormatValue(row.getCell(12)).trim() + "-"
					+ ExcelReader.getCellFormatValue(row.getCell(14)).trim() + "-"
					+ ExcelReader.getCellFormatValue(row.getCell(16)).trim() + " "
					+ ExcelReader.getCellFormatValue(row.getCell(18)).trim() + ":"
					+ ExcelReader.getCellFormatValue(row.getCell(20)).trim() + ":" + "00";
			if(!isValidDate(startTime)){
				throw new BusinessException("开始询问时间不正确");
			}
			if(!isValidDate(endTime)){
				throw new BusinessException("结束询问时间不正确");
			}
	        SimpleDateFormat sdf =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        // 若开始时间，大于结束时间
	        if(DateUtil.compareDate(sdf.parse(startTime), sdf.parse(endTime))>0){
	        	throw new BusinessException("询问开始时间晚于询问结束时间");
	        }
			askRecord.setAskingStartDate(sdf.parse(startTime));
			askRecord.setAskingEndDate(sdf.parse(endTime));
			// 地点
			row = sheet.getRow(4);
			place = ExcelReader.getCellFormatValue(row.getCell(2)).trim();
	        if(place!=null){
	        	askRecord.setPlace(place);
	        }
	        // 被调查询问人 ，性别：年龄：身份证号码：
	        row = sheet.getRow(5);
	        askedUserName = ExcelReader.getCellFormatValue(row.getCell(4)).trim();
	        if(askedUserName!=null){
	        	askRecord.setAskedUserName(askedUserName);
	        }else{
	        	throw new BusinessException("被调查询问人不能为空");
	        }
			sex =  ExcelReader.getCellFormatValue(row.getCell(10)).trim();
			if("女".equals(sex) || "男".equals(sex)){
				if("女".equals(sex)){
					askRecord.setSex(0+"");
				}
				if("男".equals(sex)){
					askRecord.setSex(1+"");
				}
			} 
			age = ExcelReader.getCellFormatValue(row.getCell(12)).trim();
			if(age!=null){
				// 有可能在类型转换的时候发生异常
				if(0 < Integer.parseInt(age) && Integer.parseInt(age)<150 ){
					askRecord.setAge(Integer.parseInt(age));
				}else{
					throw new BusinessException("请输入正确年龄");
				}
			}
			cardId = ExcelReader.getCellFormatValue(row.getCell(16)).trim();
			if(cardId!=null){
				askRecord.setCardId(cardId);
			}
			//工作单位：职务：电话：
			row = sheet.getRow(6);
			unitName =  ExcelReader.getCellFormatValue(row.getCell(3)).trim();
			if(unitName!=null){
				askRecord.setUnitName(unitName);
			}
			job =  ExcelReader.getCellFormatValue(row.getCell(13)).trim();
			if(job!=null){
				askRecord.setJob(job);
			}
			phone =  ExcelReader.getCellFormatValue(row.getCell(18)).trim();
			if(phone!=null){
				askRecord.setPhone(phone);
			}
			// 地址：邮编：
			row = sheet.getRow(7);
			address = ExcelReader.getCellFormatValue(row.getCell(2)).trim();
			postcode = ExcelReader.getCellFormatValue(row.getCell(18)).trim();
			if(address!=null){
				askRecord.setAddress(address);
			}
			if(postcode!=null){
				askRecord.setPostcode(postcode);
			}
			//与本案关系：
			row = sheet.getRow(8);
			relationCaseName = ExcelReader.getCellFormatValue(row.getCell(3)).trim();
			if(relationCaseName!=null){
				if("违法嫌疑单位的工作人员".equals(relationCaseName)){
					askRecord.setRelationCaseCode(0+"");
				}else if("污染受害人".equals(relationCaseName)){
					askRecord.setRelationCaseCode(1+"");
				}else if("证人".equals(relationCaseName)){
					askRecord.setRelationCaseCode(2+"");
				}else if("违法嫌疑人".equals(relationCaseName)){
					askRecord.setRelationCaseCode(3+"");
				}else{
					askRecord.setRelationCaseCode(null);
				}
				askRecord.setRelationCaseName(relationCaseName);
			}
			// 调查询问人及执法证编号：
			row = sheet.getRow(9);
			String userList = ExcelReader.getCellFormatValue(row.getCell(6)).trim();
		    String infoStr[] = userList.split(",");
		    if(infoStr.length>1){
	        	for(String stra:infoStr){
	        		String[] sub = stra.split(":");
	        		if(sub.length==2){
	        			askingUserNames += sub[0]+",";
	        			lawEnforcIds += sub[1]+",";
	        		}else{
	        			
	        		}
	        	}
	        } 
		    if(askingUserNames!=null && !"".equals(askingUserNames)){
		    	askRecord.setAskingUserNames(askingUserNames.substring(0,askingUserNames.length()-1));
		    }else{
		    	throw new BusinessException("调查询问人不能为空");
		    }
		    if(lawEnforcIds!=null && !"".equals(lawEnforcIds)){
		    	askRecord.setLawEnforcIds(lawEnforcIds.substring(0,lawEnforcIds.length()-1));
		    }else{
		    	throw new BusinessException("执法编号不能为空");
		    }
		    // 记录人：工作单位：
		    row = sheet.getRow(10);
		    recordUserName = ExcelReader.getCellFormatValue(row.getCell(3)).trim();
			if(recordUserName!=null && !"".equals(recordUserName)){
				askRecord.setRecordUserName(recordUserName); 
			}else{
				throw new BusinessException("记录人不能为空");
			}
			askingUnitName = ExcelReader.getCellFormatValue(row.getCell(10)).trim();
			if(askingUnitName!=null && !"".equals(askingUnitName)){
				askRecord.setAskingUnitName(askingUnitName);
			}else{
				throw new BusinessException("记录人工作单位人不能为空");
			}
			// 参与人员 
			row = sheet.getRow(11);
			participant = ExcelReader.getCellFormatValue(row.getCell(6)).trim();
			if(participant!=null){
				askRecord.setParticipant(participant);
			}
			
			//执法人员表明身份、出示证件及被调查询问人确认的记录：我们是
			row = sheet.getRow(12);
			askDepartmentName = ExcelReader.getCellFormatValue(row.getCell(14)).trim();
			if(askDepartmentName!=null){
				askRecord.setAskDepartmentName(askDepartmentName);
			}
			// 的行政执法人员，这是我们的执法证件（执法证编号：
			row = sheet.getRow(13);
			lawEnforcNumber = ExcelReader.getCellFormatValue(row.getCell(12)).trim();
			if(lawEnforcNumber!=null){
				askRecord.setLawEnforcNumber(lawEnforcNumber);
			}
			// 从21行开始，进入调查问答，判断问答结束的条件是，读到下一行没有内容时候，表示结束，我这里默认循环到100行，实际需求应该达不到100次
			List<AskingContent> contentList  = new ArrayList<AskingContent>();
			Date data =  new Date();
			for (int i = 20; i < 120; i = i+2) {
				AskingContent content = new AskingContent();
				row = sheet.getRow(i);
				if(row == null){
					break;
				}
				String strOne =  ExcelReader.getCellFormatValue(row.getCell(2)).trim();
				row = sheet.getRow(i+1);
				if(row == null){
					break;
				}
				String strTwo =  ExcelReader.getCellFormatValue(row.getCell(2)).trim();
				if(strOne==null || "".equals(strOne) || strTwo==null || "".equals(strTwo) ){
					// 没有内容表示结束
					break;
				}
				content.setCreateDate(data);
				content.setAskingContent(strOne);
				content.setAnswerContent(strTwo);
				contentList.add(content);
				data = addOneSecond(data);
			}
			if(contentList.size()>0){
				askRecord.setContentList(contentList);
			}
			jsonResult.setResult(Const.RESULT_SUCCESS);
			jsonResult.setMessage("成功");
			
		} catch (Exception e) {
			jsonResult.setResult(Const.RESULT_SUCCESS);
			jsonResult.setMessage("失败");
			is.close();
			throw e;
		}
		is.close();
		return jsonResult;
	}

	public static boolean isValidDate(String str) {
		boolean convertSuccess=true;
		// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			// 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
	        format.setLenient(false);
	        format.parse(str);
	    } catch (Exception e) {
	        convertSuccess=false;
		} 
		return convertSuccess;
	}

	@Override
	public int batchInsertAskingContent(List<AskingContent> list) {
		return contentMapper.batchInsert(list);
	}

	@Override
	public AskingRecord getAskRecordDocurl(String askId, Integer type) throws Exception {
		AskingRecord askingRecord  = askingMapper.selectByPrimaryKey(askId);
		if(type==1){
			// 这里我想用fastdfs读到nginx的代理地址，但这没有想好解决办法，这里先写在静态类里把
			if(askingRecord.getDocUrl()!=null && !"".equals(askingRecord.getDocUrl())){
				askingRecord.setDocUrl(PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs")+askingRecord.getDocUrl());	
			}
		}
		return askingRecord;
	}
	/**
	 * 时间加一秒
	 * @param date
	 * @return
	 */
	public static  Date addOneSecond(Date date) {    
	    Calendar calendar = Calendar.getInstance();    
	    calendar.setTime(date);    
	    calendar.add(Calendar.SECOND, 1);    
	    return calendar.getTime();    
	} 
	@Override
	public void saveSmj(String taskId, String recordId, String itemType, List<SysFiles> list) throws BusinessException {
		try {
			for (SysFiles sf:list) {
				
				ScanningAttachment attachment = new ScanningAttachment();
				attachment.setTaskId(taskId);
				attachment.setCheckItemId(recordId);
				attachment.setFileId(sf.getId());
				attachment.setFileUrl(sf.getFileUrl());
				attachment.setFileType(Integer.valueOf(sf.getFileType()));
				if (itemType != null && "askingRecord".equals(itemType)) {
					attachment.setItemType(1);
				} else if (itemType != null && "localCheck".equals(itemType)){
					attachment.setItemType(0);
				} else if(itemType !=null && "zpzjsmj".equals(itemType)){
					attachment.setItemType(3);
				}
				attachment.setFileName(sf.getFileName());
				attachment.setCreateTime(new Date());
				attachmentMapper.insertSelective(attachment);
				sysfilesMapper.updateSysFile(sf.getId());
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new BusinessException();
		}
	}
	
	@Override
	public List<ScanningAttachment> getAppendFile(String askingId) {
		return attachmentMapper.getAttByItemTypeAndCheckItemId("1", askingId);
	}
	
	@Override
	public ScanningAttachment getAttachmentById(String attachmentId) {
		
		return attachmentMapper.selectByPrimaryKey(attachmentId);
	}
	
	@Override
	public void fileinfoDelete(String attachmentId) throws BusinessException {
		try {
			ScanningAttachment attachment = attachmentMapper.selectByPrimaryKey(attachmentId);
			sysfilesMapper.updateSysFileDidable(attachment.getFileId());
			attachmentMapper.deleteByPrimaryKey(attachmentId);
		} catch (Exception e) {
			e.printStackTrace();
			throw new BusinessException();
		}
	}

	@Override
	public PageBean<AskingCustomModel> getAskingCustomModelByUser(AskingCustomBean customBean) {
		String areaSql = "";
		if(customBean.getBelongCountry() == null || "".equals(customBean.getBelongCountry())){
			if(customBean.getBelongCity()!= null && !"".equals(customBean.getBelongCity())){
				areaSql = customBean.getBelongCity();
			}
		}else{
			areaSql = customBean.getBelongCountry();
		}
		if(areaSql!=null && !"".equals(areaSql)){
			if("000000".equals(areaSql.substring(2))){// 省
				customBean.setAreaSql(null);
			}else if("0000".equals(areaSql.substring(4))){// 市
				customBean.setAreaSql(" and acm.TEMPLATE_AREA like '"+areaSql.substring(0,4)+"%'  ");
			}else if ("00".equals(areaSql.substring(6))){// 县 
				customBean.setAreaSql(" and acm.TEMPLATE_AREA = '"+areaSql+"' ");
			}else{
				customBean.setAreaSql(null);
			}
		}else{
			customBean.setAreaSql(null);
		}
		if(customBean.getPageNum()==null || customBean.getPageNum()==0 ){
			customBean.setPageNum(1);
		}
		if(customBean.getPageSize()==null || customBean.getPageSize()==0){
			customBean.setPageSize(10);
		}	
		PageHelper.startPage(customBean.getPageNum(),customBean.getPageSize());
		return  new PageBean<AskingCustomModel>(askCustomMapper.getAskingCustomModelListByUser(customBean)); 
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean) throws Exception{
		JsonResult jsonResult = new JsonResult();
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		//usuallyOrDefault： 0 常用/ 1 默认 / 2删除   code： 1->当前已经是常用 0->当前不是常用 
		try {
			if(customBean.getUsuallyOrDefaultOrDelete()!=null && "0".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 常用设置项
				if(customBean.getCode()!=null && "1".equals(customBean.getCode())){// 已经是常用设置为非常用
					usuallyModelMapper.deleteByPrimaryKeyByUser(sysUsers.getId(), customBean.getModelerId());
				}else if(customBean.getCode()!=null && "0".equals(customBean.getCode())){
					AskingUsuallyModel usuallyModel = new AskingUsuallyModel(); 
					usuallyModel.setUserid(sysUsers.getId());
					usuallyModel.setTemplateObjectType(customBean.getType());
					usuallyModel.setCustomDatabaseId(customBean.getModelerId());
					usuallyModelMapper.insertSelective(usuallyModel);
				}else{
					jsonResult.setResult(Const.RESULT_ERROR);
					jsonResult.setMessage("页面传递系是否为常用项信息错误");
					return jsonResult;
				}
			}else if(customBean.getUsuallyOrDefaultOrDelete()!=null && "1".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 默认设置项
				if(customBean.getCode()!=null && "1".equals(customBean.getCode())){// 已经是默认设置为非默认
					//defaultModelMapper.deleteByPrimaryKeyByUser(sysUsers.getId(), customBean.getModelerId(),"1");
					defaultModelMapper.deleteByPrimaryKeyIdByUserId(sysUsers.getId(),"1");
				}else if(customBean.getCode()!=null && "0".equals(customBean.getCode())){
					AskingDefaultModel defaultModel = new AskingDefaultModel();
					defaultModel.setUserid(sysUsers.getId());
					defaultModel.setTemplateType("1");
					defaultModel.setTemplateObjectType(customBean.getType());
					defaultModel.setCustomDatabaseId(customBean.getModelerId());
					// 一个用户同类下只能有一个默认的模版项，先删除之前的模版
					//defaultModelMapper.deleteByPrimaryKeyByUserId(sysUsers.getId(),customBean.getType());
					defaultModelMapper.deleteByPrimaryKeyIdByUserId(sysUsers.getId(),"1");
					// 新增
					defaultModelMapper.insertSelective(defaultModel);
				}else{
					jsonResult.setResult(Const.RESULT_ERROR);
					jsonResult.setMessage("页面传递系是否为默认项信息错误");
					return jsonResult;
				}
			}else if(customBean.getUsuallyOrDefaultOrDelete()!=null && "2".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 创建者删除模版
				 // 常用表，默认表，模版表，模版与项中间表
				usuallyModelMapper.deleteByPrimaryKeyByCustomDatabaseId(customBean.getModelerId());
				defaultModelMapper.deleteByPrimaryKeyByCustomModelerId(customBean.getModelerId(),"1");
				customItemMapper.deleteByPrimaryKeyByCustomModelerId(customBean.getModelerId());
				askCustomMapper.deleteByPrimaryKey(customBean.getModelerId());
			}else{
				jsonResult.setResult(Const.RESULT_ERROR);
				jsonResult.setMessage("页面传递系类型信息错误");
				return jsonResult;
			}
		} catch (Exception e) {
			throw e;
		}
		jsonResult.setResult(Const.RESULT_SUCCESS);
		jsonResult.setMessage("设置成功");
		return jsonResult;
	}

	@Override
	public PageBean<AskingCustomModel> getAskingExistModelByLawObject(AskingCustomBean customBean) {
		Task  task  = taskMapper.selectByPrimaryKey(customBean.getTemplateNumber());
		if(customBean.getPageNum()==null || customBean.getPageNum()==0 ){
			customBean.setPageNum(1);
		}
		if(customBean.getPageSize()==null || customBean.getPageSize()==0){
			customBean.setPageSize(10);
		}	
		PageHelper.startPage(customBean.getPageNum(),customBean.getPageSize());
		// customBean.getTemplateNumber() 代替执法对象id
		return new PageBean<AskingCustomModel>(askCustomMapper.getAskingExistModelListByLawObjId(task.getLawObjectId()));
	}

	@Override
	public AskingRecord getNewContentList(String lawObjectType,String code,String customModelerId,String contentId) {
		AskingRecord newAsk = new AskingRecord();
		List<AskingContent> newAskContent = new ArrayList<AskingContent>();
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userId = sysUsers.getId();
		List<AskingItemDatabase> askModelItemList = new ArrayList<AskingItemDatabase>();
		if("0".equals(code)){ // 新增
			// 1. 查询该类型下该用户是否有默认模版
		//	AskingDefaultModel defaultModel = defaultModelMapper.getAskDefaultModel(userId, lawObjectType);
			List<AskingDefaultModel> defaultModelList = defaultModelMapper.getAskDefaultModelList(userId);
			
			if(defaultModelList==null || defaultModelList.size()==0){ // 无：使用系统 系统只用一份，默认id 为 1 (固定，后期若需要修改)
				//调用系统默认模板 只有一份
				//askModelItemList =  itemDatabaseMapper.askSysModelList("1");
				askModelItemList =  itemDatabaseMapper.askSysDefaultModelList();
				newAsk.setModelerType("系统模版");
			}else{ // 有：使用默认
				for(AskingDefaultModel askingDefaultModel :defaultModelList){
					if("1".equals(askingDefaultModel.getTemplateType())){
						//自定义默认
						askModelItemList =  itemDatabaseMapper.askDefaultModelList(askingDefaultModel.getCustomDatabaseId());
						AskingCustomModel askingCustomModel   = askCustomMapper.selectByPrimaryKey(askingDefaultModel.getCustomDatabaseId());
						newAsk.setRecordName(askingCustomModel.getContributionName()!=null?askingCustomModel.getContributionName():""); // 得到贡献者名字
					}else if("0".equals(askingDefaultModel.getTemplateType())){
						//调用用户系统默认模板
						askModelItemList =  itemDatabaseMapper.isSysaskDefaultModelList(askingDefaultModel.getCustomDatabaseId(),"1");
					}
					
					if(askModelItemList ==null ||askModelItemList.size()==0){ 
						//调用用户系统默认模板
						askModelItemList =  itemDatabaseMapper.isSysaskDefaultModelList(askingDefaultModel.getCustomDatabaseId(),"1");
					}
					newAsk.setModelerType("自定义模版");
				}
			}
			newAskContent = packAskingContentList(askModelItemList);
		}else if("1".equals(code)){ // 自定义模版
			askModelItemList =  itemDatabaseMapper.askDefaultModelList(customModelerId);
			newAskContent = packAskingContentList(askModelItemList);
			AskingCustomModel askingCustomModel   = askCustomMapper.selectByPrimaryKey(customModelerId);
			newAsk.setRecordName(askingCustomModel.getContributionName()!=null?askingCustomModel.getContributionName():""); // 得到贡献者名字
			newAsk.setModelerType("自定义模版");
		}else if("2".equals(code)){ // 历史记录模版
			newAskContent = contentMapper.selectAskingContentByAskId(contentId);
			AskingRecord askingRecord  = askingMapper.selectByPrimaryKey(contentId);
			newAsk.setRecordName(askingRecord.getContributionName()!=null?askingRecord.getContributionName():""); // 得到贡献者名字
			newAsk.setModelerType("历史模版");
		}else if("3".equals(code)){ // 使用系统模版
			askModelItemList =  itemDatabaseMapper.askSysModelList("1");
			newAskContent = packAskingContentList(askModelItemList);
			newAsk.setModelerType("系统模版");
		}else{
			return null;
		}
		newAsk.setContentList(newAskContent);
		return newAsk;
	}
	
	/**
	 * 该方法基于上面一个方法抽取出的独立封装
	 */
	public static List<AskingContent> packAskingContentList(List<AskingItemDatabase> askSysModelList ){
		List<AskingContent> list = new ArrayList<AskingContent>();
		if(askSysModelList!=null && askSysModelList.size()>0){
			for(AskingItemDatabase sysItem : askSysModelList){
				if(sysItem==null){
					continue;
				}
				
				AskingContent content = new AskingContent();
				content.setAskingContent(sysItem.getAskingContent()!=null?sysItem.getAskingContent():"");
				content.setAnswerContent(sysItem.getAnswerContent()!=null?sysItem.getAnswerContent():"");
				content.setAskingItemDatabaseId(sysItem.getId()!=null?sysItem.getId():"");
				list.add(content);
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult saveTemplate(AskingCustomModel customModel) throws Exception {
		JsonResult json = new JsonResult();
		try {
			if(customModel.getTemplateObjectType()==null || customModel.getTemplateObjectType()==""){
				throw new BusinessException("类型不明确");
			}
			//查重 模版名称
			AskingCustomModel customModelTemp  = askCustomMapper.chickTemplateName(customModel.getTemplateName(),customModel.getTemplateObjectType());
			if(customModelTemp !=null ){
				json.setMessage("重复的模板名称,请更换模板名称");
				json.setResult(Const.RESULT_ERROR);
				return json;
			}
			//1类型为 现象执法 -ZFDX，2类型为勘察笔录-KCBL，3类型为询问笔录-XWBL
			String templateNumber =  systemCodingService.getSysTemplateCoding("XWBL");
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			customModel.setTemplateNumber(templateNumber);
			customModel.setCreateTime(new Date());
			customModel.setUpdateTime(new Date());
			customModel.setUserName(sysUser.getLoginname());
			customModel.setUserId(sysUser.getId());
			//模版贡献者 
			if(ChangnengUtil.isNull(customModel.getContributionName())){
				customModel.setContributionName(sysUser.getLoginname());
			}else{
				if(customModel.getContributionName().length()<4900){ // 字段过长
					String[] contributionIdArr = customModel.getContributionName().split(",");
					if(!sysUser.getLoginname().equals(contributionIdArr[contributionIdArr.length-1])){
						//追加贡献者
						customModel.setContributionName(customModel.getContributionName()+","+sysUser.getLoginname());
					}
				}
			}
			// 自定义模版入库
			askCustomMapper.insertSelective(customModel);
			// 保存检查项信息库
			String databaseItem = customModel.getVueJson();
			if(!ChangnengUtil.isNull(databaseItem)){
				List<AskingItemDatabase> askDataBaseItem = JacksonUtils.toCollection(databaseItem, new TypeReference<List<AskingItemDatabase>>() {});
				if(askDataBaseItem!=null && askDataBaseItem.size()>0){
					for (int i = 0; i < askDataBaseItem.size(); i++) {
						AskingItemDatabase item = askDataBaseItem.get(i);
						AskingCustomItem askCustomItem = new AskingCustomItem(); // 中间表
						AskingItemDatabase askingItemDatabase  = null;
						if(item.getId()!=null && !"".equals(item.getId())){
							askingItemDatabase = itemDatabaseMapper.selectByPrimaryKey(item.getId());
						}
						if(ChangnengUtil.isNull(item.getId())  || askingItemDatabase == null){ // 证明是要新增项
							item.setCreateDate((new Date()));
							item.setModelerType("1"); // 自定义类型
							item.setTemplateObjectType(customModel.getTemplateObjectType());
							itemDatabaseMapper.insertSelective(item);
							askCustomItem.setAskingItemId(item.getId());
							//维护业务库的模板库id
							AskingContent askContent = new AskingContent();
							askContent.setId(item.getAskContentId());
							askContent.setAskingItemDatabaseId(item.getId());
							contentMapper.updateByPrimaryKeySelective(askContent);
						}else{
							askCustomItem.setAskingItemId(askingItemDatabase.getId());
						}
						askCustomItem.setLoction((i+1)+"");
						askCustomItem.setCustomModelerId(customModel.getId());
						customItemMapper.insertSelective(askCustomItem);
					}
				}
			
			}
			//判断是否为常用模板 1是 0否
			String usuallyStatus = customModel.getUsuallyStatus();
			if("1".equals(usuallyStatus)){ 
				AskingUsuallyModel usuallyModel = new AskingUsuallyModel();
				usuallyModel.setTemplateObjectType(customModel.getTemplateObjectType());
				usuallyModel.setUserid(sysUser.getId());
				usuallyModel.setCustomDatabaseId(customModel.getId());
				usuallyModelMapper.insertSelective(usuallyModel);
			}
			//判断是否为默认模板
			String defaultStatus = customModel.getDefaultStatus();
			if("1".equals(defaultStatus)){
				// 一个用户同类下只能有一个默认的模版项，先删除之前的模版
				defaultModelMapper.deleteByPrimaryKeyByUserId(sysUser.getId(),customModel.getTemplateObjectType());
				AskingDefaultModel  defaultModel = new AskingDefaultModel();
				defaultModel.setTemplateObjectType(customModel.getTemplateObjectType());
				defaultModel.setUserid(sysUser.getId());
				defaultModel.setTemplateType("1");
				defaultModel.setCustomDatabaseId(customModel.getId());
				defaultModelMapper.insertSelective(defaultModel);
			}
			 json.setMessage("保存自定义模板成功");
			 json.setResult(Const.RESULT_SUCCESS);
		} catch (Exception e) {
			throw e;
		}
		return json;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult saveAskingContentLocation(ModelerLocationBean locationBean) throws Exception {
		JsonResult jsonResult = new JsonResult();
		if(ChangnengUtil.isNull(locationBean.getContentIdA()) || ChangnengUtil.isNull(locationBean.getContentIdB())){
			throw new BusinessException("移动数据无标志");
		}
		if(ChangnengUtil.isNull(locationBean.getIndex())){
			throw new BusinessException("移动下标不明确");
		}
		AskingContent recordA = new AskingContent();
		recordA.setId(locationBean.getContentIdA());
		AskingContent recordB = new AskingContent();
		recordB.setId(locationBean.getContentIdB());
		
		if("0".equals(locationBean.getType())){ // 上移
			recordA.setLoction(Integer.parseInt(locationBean.getIndex())-1);
			recordB.setLoction(Integer.parseInt(locationBean.getIndex())+1);
			contentMapper.updateByPrimaryKeySelective(recordA);
			contentMapper.updateByPrimaryKeySelective(recordB);
		}else if ("1".equals(locationBean.getType())){ // 下移
			recordA.setLoction(Integer.parseInt(locationBean.getIndex())+1);
			recordB.setLoction(Integer.parseInt(locationBean.getIndex())-1);
			contentMapper.updateByPrimaryKeySelective(recordA);
			contentMapper.updateByPrimaryKeySelective(recordB);
			
		}else{
			throw new BusinessException("类型不明确");
		}
		jsonResult.setResult(Const.RESULT_SUCCESS);
		return jsonResult;
	}

	@Override
	public void saveApiSmj(String taskId, String recordId, String itemType,
			HttpServletRequest request, HttpServletResponse response,SysUsers sysUsers) throws IOException, Exception {
		
		  MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			
			//获取图片项
			  MultipartFile multipartFile = multipartRequest.getFile("file");
			  boolean fileAllowed = CheckFileFormatTools.isFileAllowed(multipartFile);
				if(!fileAllowed){
					throw new Exception("文件格式有问题，请检查待上传文件是否能正常打开");
			  }
			  //获取图片的相关信息
			  String fileName = multipartFile.getOriginalFilename();//文件名
			  if(fileName.contains("?")){
			  fileName = fileName.substring(0, fileName.indexOf("?"));
			  }
				String prefix=fileName.substring(fileName.lastIndexOf(".")+1);
	            FastDFSClient fd=new FastDFSClient("classpath:fdfs_client.conf");
	            String url= fd.uploadFile(multipartFile.getBytes(),prefix);
	            SysFiles sf=new SysFiles();
	            sf.setFileName(fileName);
	            if(prefix.equalsIgnoreCase("pdf")){
	            	 sf.setFileType("0");
	            }else{
	            	sf.setFileType("1");
	            }
	            sf.setState("1");
	            sf.setFileSize(String.valueOf(multipartFile.getSize()));
	            sf.setFileUrl(url);
	            sf.setCreateDate(new Date());
	            sf.setCreatUserId(sysUsers.getId());
	            sf.setCreatUserName(sysUsers.getUsername());
	            sysfilesMapper.insertSelective(sf);
		
				ScanningAttachment attachment = new ScanningAttachment();
				attachment.setTaskId(taskId);
				attachment.setCheckItemId(recordId);
				attachment.setFileId(sf.getId());
				attachment.setFileUrl(sf.getFileUrl());
				attachment.setFileType(Integer.valueOf(sf.getFileType()));
				if (itemType != null && "askingRecord".equals(itemType)) {
					attachment.setItemType(1);
				} else if (itemType != null && "localCheck".equals(itemType)){
					attachment.setItemType(0);
				}
				attachment.setFileName(sf.getFileName());
				
				attachmentMapper.insertSelective(attachment);
	}
	public String selectUserParentTareaName(String code){
		String name = "";
		if(!ChangnengUtil.isNull(code)){
			// 区划的后4为若不是4个0则表示为3级（区、县）用户，则查出上级区划名称
			if(!code.substring(code.length()-4, code.length()).equals("0000")){
				name = taskMapper.getTareaNameByCode(code.substring(0, 4)+"0000");
			}
		}
		return name;
	}
	public static void main(String[] args) {
		String code = "35030400";
		String str = code.substring(code.length()-4, code.length());
		System.out.println(str);
		if(!code.substring(code.length()-4, code.length()).equals("0000")){
			str  = code.substring(0, 4)+"0000";
			System.out.println(str);
		}
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void fileinfoDeleteGroup(String ids) throws BusinessException {
		try {
			String idArr []=ids.split(",");
			for (int i = 0; i < idArr.length; i++) {
				ScanningAttachment attachment = attachmentMapper.selectByPrimaryKey(idArr[i]);
				sysfilesMapper.updateSysFileDidable(attachment.getFileId());
				attachmentMapper.deleteByPrimaryKey(attachment.getId());
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new BusinessException("删除扫描件失败");
		}
	}

	@Override
	public ResponseJson rotateImage(String id, String url, String tableName,String column,String fileId,Integer degree) throws Exception{
		String endUrl = "";
		String fastdfs_ip = PropertiesHandlerUtil.getValue("fastdfs.nginx.iptwo","fastdfs");
		if(!ChangnengUtil.isNull(degree) && degree!=0){
			String newUrl = ImageUtil.RotationImage(fastdfs_ip+url, degree);
			sysfilesMapper.updateUrl(id, newUrl, tableName, column);
			//sysfilesMapper.updateSysFileDidable(fileId);
			SysFiles record = new SysFiles();
			record.setId(fileId);
			record.setFileUrl(newUrl);
			sysfilesMapper.updateByPrimaryKeySelective(record);
			endUrl = newUrl;
		}
		return new ResponseJson().success("200", "200", "保存成功！", null, endUrl);
	}

}
