package org.changneng.framework.frameworkbusiness.utils;

import java.util.HashMap;
import java.util.Map;

import org.changneng.framework.frameworkcore.utils.ChangnengUtil;

/**
  * 该类提供：排除你传入的表，查询你其他的表中是否还有需要对接非删除的数据存在
  * <p>Title:DockingDeletionUtil </p>
  * <p>Description: </p>
  * <p>Company: </p> 
  * <AUTHOR>
  * @date 2017年10月28日-上午11:33:26
 */
public class DockingDeletionUtil {
 
	public static Map<String ,String> removeCurrentTables(String key){
		 Map<String ,String> deleteType = new HashMap<>();
		 if(!ChangnengUtil.isNull(key)){
			// 1简易行政处罚、2一般行政处罚、如果基本信息发送失败，就不用再发送该条信息了，下边都一样；建设项目包含在其中
			 deleteType.put("atv_sanction_case", " select count(*) from atv_sanction_case where is_delete !=1 and docking_state !=0 and case_id = ");
			// 4查封扣押
			 deleteType.put("sequestration_info", " select count(*) from sequestration_info where  is_del !=1 and docking_state !=0 and  case_id = ");
			 // 5限产停产、
			 deleteType.put("limit_stop_product", " select count(*) from limit_stop_product where is_del!=1 and  docking_state !=0 and case_id = ");
			 // 6行政拘留、
			 deleteType.put("administrative_detention", " select count(*) from administrative_detention where  is_del!=1 and  docking_state !=0 and case_id = ");
			 // 7环境污染犯罪、
			 deleteType.put("pollution_crime", " select count(*) from pollution_crime where IS_DEL!=1 and docking_state !=0 and case_id = ");
			 // 8申请法院强制执行、
			 deleteType.put("apply_force", " select count(*) from apply_force where IS_DEL !=1 and docking_state !=0 and case_id = ");
			 // 9按日计罚  按日计罚是一对多这个特殊处理  排除他的id以为是否还有其他的
			 deleteType.put("penalty_day", " select count(*) from penalty_day where IS_DEL !=1 and docking_state !=0 and case_id =  ");
			
			 deleteType.remove(key);
			
		 }
		 return deleteType;
	}
}
