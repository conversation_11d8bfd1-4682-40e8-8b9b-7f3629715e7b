<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.TaskRequireFilesMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.TaskRequireFiles">
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl" />
    <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
  </resultMap>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskRequireFiles">
    insert into TASK_REQUIRE_FILES (ID, TASK_ID, FILE_ID, 
      FILE_URL, FILE_TYPE, FILE_NAME)
    values (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR}, 
      #{fileUrl,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR},#{fileName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskRequireFiles">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into TASK_REQUIRE_FILES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
      <if test="fileId != null">
        FILE_ID,
      </if>
      <if test="fileUrl != null">
        FILE_URL,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- getTaskRequireFilesByTaskId -->
    <!-- getTaskRequireFilesByTaskId -->
  	<select id="getTaskRequireFilesByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
		select ID,TASK_ID,FILE_ID,FILE_URL,FILE_TYPE, FILE_NAME
		from  TASK_REQUIRE_FILES  where TASK_ID = #{taskId}
	</select>
	 <!-- selectByPrimaryKey -->
   <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    *
    from TASK_REQUIRE_FILES
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <!-- deletefileInfo -->
  <delete id="deletefileInfo" parameterType="java.lang.String">
    delete from TASK_REQUIRE_FILES
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
</mapper>