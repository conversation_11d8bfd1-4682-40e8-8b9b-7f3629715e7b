package org.changneng.framework.frameworkbusiness.service.swingtag.impl;

import java.text.SimpleDateFormat;
import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagAdjunctMapper;
import org.changneng.framework.frameworkbusiness.dao.swingtag.SwingTagSubjectMapper;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTag;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagAdjunct;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagQuery;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagQueryBean;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagQuerySeach;
import org.changneng.framework.frameworkbusiness.service.swingtag.SwingTagQueryService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;

@Service
public class SwingTagQueryServiceImp implements SwingTagQueryService{
	
	@Autowired
	private SwingTagSubjectMapper swingTagSubjectMapper;
	@Autowired
	private SwingTagAdjunctMapper  swingTagAdjunctMapper;
	@Override
	public PageBean<SwingTagQuery> swingtagQueryList(
			SwingTagQuerySeach swingTagQuerySeach) {
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		if(ChangnengUtil.isNull(swingTagQuerySeach.getPageNumber()) ){
			swingTagQuerySeach.setPageNumber(1);
		}
		if(ChangnengUtil.isNull(swingTagQuerySeach.getPageSize()) ){
			swingTagQuerySeach.setPageSize(15);
		}
		
		if(!ChangnengUtil.isNull(swingTagQuerySeach.getAreaStatus())){
			swingTagQuerySeach.setAreaCode(sysUser.getBelongAreaId());
		}else{
			// 条件查询
			if ((swingTagQuerySeach.getBelongCity() != null && !"".equals(swingTagQuerySeach.getBelongCity()))
					&& (swingTagQuerySeach.getBelongCounty() != null && !"".equals(swingTagQuerySeach.getBelongCounty()))) {
				// 县级查询
				swingTagQuerySeach.setAreaLeavel("3");
			} else if ((swingTagQuerySeach.getBelongCity() != null && !"".equals(swingTagQuerySeach.getBelongCity()))
					&& ("".equals(swingTagQuerySeach.getBelongCounty()))) {
				// 市级查询
				swingTagQuerySeach.setAreaLeavel("2");
				swingTagQuerySeach.setAreaCode(swingTagQuerySeach.getBelongCity().substring(0, 4));
			} else {
				swingTagQuerySeach.setAreaLeavel("1");
			}
		}
		
		PageHelper.startPage(swingTagQuerySeach.getPageNumber(), swingTagQuerySeach.getPageSize());
		return new PageBean<SwingTagQuery>(swingTagSubjectMapper.swingtagQueryList(swingTagQuerySeach));
	}
	
	@Override
	public List<SwingTagQuery> swingtagDownList(SwingTagQuerySeach swingTagQuerySeach) {
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		if(ChangnengUtil.isNull(swingTagQuerySeach.getPageNumber()) ){
			swingTagQuerySeach.setPageNumber(1);
		}
		if(ChangnengUtil.isNull(swingTagQuerySeach.getPageSize()) ){
			swingTagQuerySeach.setPageSize(15);
		}
		
		if(!ChangnengUtil.isNull(swingTagQuerySeach.getAreaStatus())){
			swingTagQuerySeach.setAreaCode(sysUser.getBelongAreaId());
		}else{
			// 条件查询
			if ((swingTagQuerySeach.getBelongCity() != null && !"".equals(swingTagQuerySeach.getBelongCity()))
					&& (swingTagQuerySeach.getBelongCounty() != null && !"".equals(swingTagQuerySeach.getBelongCounty()))) {
				// 县级查询
				swingTagQuerySeach.setAreaLeavel("3");
			} else if ((swingTagQuerySeach.getBelongCity() != null && !"".equals(swingTagQuerySeach.getBelongCity()))
					&& ("".equals(swingTagQuerySeach.getBelongCounty()))) {
				// 市级查询
				swingTagQuerySeach.setAreaLeavel("2");
				swingTagQuerySeach.setAreaCode(swingTagQuerySeach.getBelongCity().substring(0, 4));
			} else {
				swingTagQuerySeach.setAreaLeavel("1");
			}
		}
		List<SwingTagQuery> swingtagQueryList = swingTagSubjectMapper.swingtagQueryList(swingTagQuerySeach);
		transferAttr(swingtagQueryList);
		return swingtagQueryList;
	}
	
	/**
	 * 导出属性转换
	 * 
	 * @param swingTagList
	 * @return
	 */
	private List<SwingTagQuery> transferAttr(List<SwingTagQuery> swingtagQueryList) {
		for (int i = 0; i < swingtagQueryList.size(); i++) {
			SwingTagQuery swingTagQuery = swingtagQueryList.get(i);
			if (swingTagQuery.getIsSwingTag() != null) {
				if(swingTagQuery.getIsSwingTag()==1){
					swingTagQuery.setIsSwingTagTr("挂牌中");
				}else if(swingTagQuery.getIsSwingTag()==0){
					swingTagQuery.setIsSwingTagTr("已解牌");
				}
			}
			if (swingTagQuery.getNodeTimeoutState() != null) {
				if(swingTagQuery.getNodeTimeoutState().equals("0")){
					swingTagQuery.setNodeTimeoutStateTr("正常");
				}else if(swingTagQuery.getNodeTimeoutState().equals("1")){
					swingTagQuery.setNodeTimeoutStateTr("预警");
				}else if(swingTagQuery.getNodeTimeoutState().equals("2")){
					swingTagQuery.setNodeTimeoutStateTr("超时");
				}
			}
			if (swingTagQuery.getReleaseDate() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				swingTagQuery.setReleaseDateTr(sdf.format(swingTagQuery.getReleaseDate()));
			}
			if (swingTagQuery.getLimitTime() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				swingTagQuery.setLimitTimeTr(sdf.format(swingTagQuery.getLimitTime()));
			}
			if (swingTagQuery.getRelieveTime() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				swingTagQuery.setRelieveTimeTr(sdf.format(swingTagQuery.getRelieveTime()));
			}
		}
		
		return swingtagQueryList;
	}
	
	@Override
	public SwingTagQueryBean swingtagQuery(String swingTagSubjectId) {
		SwingTagQueryBean swingTagQueryBean = new SwingTagQueryBean();
		if(!ChangnengUtil.isNull(swingTagSubjectId)){
			try {
				SwingTagQuery swingTagQuery=swingTagSubjectMapper.swingtagQuery(swingTagSubjectId);
				//查询挂牌文件
				List<SwingTagAdjunct> swingTagUpAdjunct = swingTagAdjunctMapper.querySwingTagUpAdjunct(swingTagSubjectId);
				//查询解牌文件
				List<SwingTagAdjunct> swingTagDownAdjunct = swingTagAdjunctMapper.querySwingTagDownAdjunct(swingTagSubjectId);
				swingTagQueryBean.setSwingTagDownAdjunctList(swingTagDownAdjunct);
				swingTagQueryBean.setSwingTagQuery(swingTagQuery);
				swingTagQueryBean.setSwingTagUpAdjunctList(swingTagUpAdjunct);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return swingTagQueryBean;
	}


}
