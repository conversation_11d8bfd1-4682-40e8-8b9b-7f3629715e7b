package org.changneng.framework.frameworkbusiness.entity.filecase;

public class DecisionNumberCheckout {
	private String decisionNumberType;//决定书文号隐藏显示状态
	private String isHaveDecisionNumberType;//是否已有决定书文号隐藏显示状态
	private String userBelongAreaCode;//基本信息创建人去区划
	private Integer isHaveDecisionNumber;//是否已有决定书文号
	
	//用于限产停产的状态
	private String isHaveDecisionNumberType1;//是否已有决定书文号隐藏显示状态
	private Integer isHaveDecisionNumber1;//是否已有决定书文号
	private String decisionNumberType1;//决定书文号隐藏显示状态
	
	
	public String getDecisionNumberType1() {
		return decisionNumberType1;
	}
	public void setDecisionNumberType1(String decisionNumberType1) {
		this.decisionNumberType1 = decisionNumberType1;
	}
	public String getIsHaveDecisionNumberType1() {
		return isHaveDecisionNumberType1;
	}
	public void setIsHaveDecisionNumberType1(String isHaveDecisionNumberType1) {
		this.isHaveDecisionNumberType1 = isHaveDecisionNumberType1;
	}
	public Integer getIsHaveDecisionNumber1() {
		return isHaveDecisionNumber1;
	}
	public void setIsHaveDecisionNumber1(Integer isHaveDecisionNumber1) {
		this.isHaveDecisionNumber1 = isHaveDecisionNumber1;
	}
	public Integer getIsHaveDecisionNumber() {
		return isHaveDecisionNumber;
	}
	public void setIsHaveDecisionNumber(Integer isHaveDecisionNumber) {
		this.isHaveDecisionNumber = isHaveDecisionNumber;
	}
	public String getDecisionNumberType() {
		return decisionNumberType;
	}
	public void setDecisionNumberType(String decisionNumberType) {
		this.decisionNumberType = decisionNumberType;
	}
	public String getIsHaveDecisionNumberType() {
		return isHaveDecisionNumberType;
	}
	public void setIsHaveDecisionNumberType(String isHaveDecisionNumberType) {
		this.isHaveDecisionNumberType = isHaveDecisionNumberType;
	}
	public String getUserBelongAreaCode() {
		return userBelongAreaCode;
	}
	public void setUserBelongAreaCode(String userBelongAreaCode) {
		this.userBelongAreaCode = userBelongAreaCode;
	}
	
	
}
