<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.ScanningAttachmentMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.ScanningAttachment">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="CHECK_ITEM_ID" jdbcType="VARCHAR" property="checkItemId" />
    <result column="ITEM_TYPE" jdbcType="DECIMAL" property="itemType" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl" />
    <result column="FILE_TYPE" jdbcType="DECIMAL" property="fileType" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize" />
    <result column="LOCATION" jdbcType="DECIMAL" property="location" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, TASK_ID, CHECK_ITEM_ID, ITEM_TYPE, FILE_ID, FILE_URL, FILE_TYPE, FILE_NAME,LOCATION,CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SCANNING_ATTACHMENT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SCANNING_ATTACHMENT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.ScanningAttachment">
    insert into SCANNING_ATTACHMENT (ID, TASK_ID, CHECK_ITEM_ID, 
      ITEM_TYPE, FILE_ID, FILE_URL, 
      FILE_TYPE, FILE_NAME,LOCATION,CREATE_TIME)
    values (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{checkItemId,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=DECIMAL}, #{fileId,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, 
      #{fileType,jdbcType=DECIMAL}, #{fileName,jdbcType=VARCHAR},#{location,jdbcType=DECIMAL},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ScanningAttachment">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into SCANNING_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
      <if test="checkItemId != null">
        CHECK_ITEM_ID,
      </if>
      <if test="itemType != null">
        ITEM_TYPE,
      </if>
      <if test="fileId != null">
        FILE_ID,
      </if>
      <if test="fileUrl != null">
        FILE_URL,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="location != null">
        LOCATION,
      </if>
      <if test="createTime != null">
		CREATE_TIME,
	  </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="checkItemId != null">
        #{checkItemId,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=DECIMAL},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=DECIMAL},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
		#{createTime,jdbcType=TIMESTAMP},
	  </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ScanningAttachment">
    update SCANNING_ATTACHMENT
    <set>
      <if test="taskId != null">
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="checkItemId != null">
        CHECK_ITEM_ID = #{checkItemId,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        ITEM_TYPE = #{itemType,jdbcType=DECIMAL},
      </if>
      <if test="fileId != null">
        FILE_ID = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        FILE_TYPE = #{fileType,jdbcType=DECIMAL},
      </if>
      <if test="fileName != null">
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
		LOCATION = #{location,jdbcType=DECIMAL},
	  </if>
	  <if test="createTime != null">
		#{createTime,jdbcType=TIMESTAMP},
	  </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.ScanningAttachment">
    update SCANNING_ATTACHMENT
    set TASK_ID = #{taskId,jdbcType=VARCHAR},
      CHECK_ITEM_ID = #{checkItemId,jdbcType=VARCHAR},
      ITEM_TYPE = #{itemType,jdbcType=DECIMAL},
      FILE_ID = #{fileId,jdbcType=VARCHAR},
      FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      FILE_TYPE = #{fileType,jdbcType=DECIMAL},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      LOCATION = #{location,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <select id="getAttByItemTypeAndCheckItemId" parameterType="java.lang.String" resultMap="BaseResultMap">
  
  	select 
	<include refid="Base_Column_List" />
	from  SCANNING_ATTACHMENT  
	where  CHECK_ITEM_ID = #{checkItemId} and ITEM_TYPE = #{itemType}
  </select>
  
  <select id="getSmjListBytaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
  
  	select 
	<include refid="Base_Column_List" />
	from  SCANNING_ATTACHMENT  
	where  TASK_ID = #{taskId} and ITEM_TYPE = #{itemType} order by location
  </select>
  
  
   <select id="getAttByTaskIdAndGeneratedTime"  resultMap="BaseResultMap">
   	 SELECT  sa.ITEM_TYPE,sa.FILE_URL, sf.file_type as  FILE_TYPE, sf.file_size, sf.file_name,sa.CREATE_TIME
   	 from  scanning_attachment  sa
	 LEFT JOIN sys_files sf ON sa.file_id = sf.id  
   	 where  sa.TASK_ID  in( ${taskId} )   and sa.ITEM_TYPE !='2' and 
   	 CREATE_TIME &gt;= #{createTime,jdbcType=TIMESTAMP}  ORDER BY  sa.ITEM_TYPE 
  </select>
  
   <select id="getScanningAttachmentListByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
   	 SELECT  sa.ITEM_TYPE,sa.FILE_URL, sf.file_type as  FILE_TYPE, sf.file_size, sf.file_name,sa.CREATE_TIME
   	 from  scanning_attachment  sa
	 LEFT JOIN sys_files sf ON sa.file_id = sf.id  
   	 where  sa.TASK_ID  in( ${taskId} )   and sa.ITEM_TYPE !='2'  ORDER BY  sa.ITEM_TYPE 
  </select>
  
    <!--  getChickItemAdjunctList -->
   <select id="getChickItemAdjunctList" parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT   sa.ID, sa.TASK_ID, sa.CHECK_ITEM_ID, sa.ITEM_TYPE, sa.FILE_ID, sa.FILE_URL, sa.FILE_TYPE, sa.FILE_NAME
		from LOCAL_CHECK lc RIGHT JOIN SCANNING_ATTACHMENT sa 
		on lc.id = sa.check_item_id 
		where LC.task_id =#{taskId} 
		and SA.ITEM_TYPE =#{itemType} order by location
  </select>
</mapper>