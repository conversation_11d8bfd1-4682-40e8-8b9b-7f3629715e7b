package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.FilesMonitor;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.entity.historyLawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RLawEnforce;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RLawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RMattersDatabase;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

/** 
* <AUTHOR> 
* @version 2018年7月30日 下午8:30:39 
* “建立随机抽查的service”
*/
public interface BuildRandomCheckService {
	//页面列表及查询分页
	public PageBean<RMattersDatabase> getMattersList(SysUsers sysUsers, RMattersDatabase rMattersDatabase,int pNum,int pageSize)throws Exception;
	//某个事项详情
	public RMattersDatabase getMattersDatabase(String id)throws Exception;
	//根据事项id取到附件id查找所有所关联的附件信息（FilesMonitor  文书制作表）
	public  List<FilesMonitor> getRMattersFiles(String id)throws Exception;
	//根据事项id取到管理部门id查找所有执法人员
	public PageBean<SysUsers> geSysUsersList(String matterId,int pNum,int pageSize)throws Exception;
	//根据事项id查询执法对象(id为1，2   加载建设项目对象)
	public PageBean<RLawEnforceObject> getRLawEnforceObject(RLawEnforceObject rLawEnforceObject,int pNum,int pageSize)throws Exception;
	//根据事项id查询执法对象（不为1,2   加载本系统对象）
	public PageBean<LawEnforceObject> getHistoryLawEnforceObject(LawEnforceObject lawEnforceObject,int pNum,int pageSize)throws Exception;
	//将选择的执法对象添加到相应的数据表中
	public void saveRLawEnforce (String id,String matterId );
	//根据事项id查找已添加的执法对象进行随机抽取
	public List<RLawEnforce> getObject(String matterId);
	//将随机抽取的执法对象添加到相应的数据表中
	public void saveRandomObject(Integer selectNumber,String matterId);
	//勾选的执法对象回显使用
	public List<RLawEnforce> selectRLawEnforceByMatterId(String matterId);
	//根据事项id查询已抽取的执法对象(id为1，2   加载建设项目对象)
	public PageBean<RLawEnforceObject> getRLawEnforceObjectNum(String matterId,int pNum,int pageSize)throws Exception;
	//根据事项id查询已抽取的执法对象（不为1,2   加载本系统对象）
	public PageBean<LawEnforceObject> getHistoryLawEnforceObjectNum(String matterId,int pNum,int pageSize)throws Exception;
	
	
}
