package org.changneng.framework.frameworkbusiness.entity;

import java.math.BigDecimal;
/**
 * 
 * 查询污染源库信息的实体类
 * <AUTHOR>
 *
 */
public class RandomPollutionVo {
	
	private String id;

    private String belongAreaId;
    
    private String attrName;
    
    private String belongAreaName;

    private BigDecimal objectTotalNumber;

    private BigDecimal extractedObjNumber;

    private BigDecimal extractedObjTimes;

    private BigDecimal unextractedObjNumber;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBelongAreaId() {
		return belongAreaId;
	}

	public void setBelongAreaId(String belongAreaId) {
		this.belongAreaId = belongAreaId;
	}

	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public String getBelongAreaName() {
		return belongAreaName;
	}

	public void setBelongAreaName(String belongAreaName) {
		this.belongAreaName = belongAreaName;
	}

	public BigDecimal getObjectTotalNumber() {
		return objectTotalNumber;
	}

	public void setObjectTotalNumber(BigDecimal objectTotalNumber) {
		this.objectTotalNumber = objectTotalNumber;
	}

	public BigDecimal getExtractedObjNumber() {
		return extractedObjNumber;
	}

	public void setExtractedObjNumber(BigDecimal extractedObjNumber) {
		this.extractedObjNumber = extractedObjNumber;
	}

	public BigDecimal getExtractedObjTimes() {
		return extractedObjTimes;
	}

	public void setExtractedObjTimes(BigDecimal extractedObjTimes) {
		this.extractedObjTimes = extractedObjTimes;
	}

	public BigDecimal getUnextractedObjNumber() {
		return unextractedObjNumber;
	}

	public void setUnextractedObjNumber(BigDecimal unextractedObjNumber) {
		this.unextractedObjNumber = unextractedObjNumber;
	}
    
    

}
