package org.changneng.framework.frameworkbusiness.entity.dto;

//--↓↓↓↓↓↓↓↓↓↓---
/**
 * 环境监管一件事检查项DTO
 *
 * <AUTHOR> Generated
 * @date 2025-01-31
 */
public class EnvSupervisionItemDTO {

    /**
     * 本地检查ID
     */
    private String localCheckId;

    /**
     * 配置项ID（CHECK_ITEM_CONFIG表的真实主键ID）
     */
    private String configItemId;

    /**
     * 检查项层级标识码（如：0_0、0_1、2_1等，对应前端的problemId）
     */
    private String itemLevelCode;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 父级标题
     */
    private String parentTitle;

    /**
     * 检查项名称
     */
    private String itemName;

    /**
     * 检查结果：1=是，0=否，2=不涉及
     */
    private String result;

    /**
     * 问题简述
     */
    private String problemDesc;

    /**
     * 是否父级不涉及
     */
    private Boolean isParentNotInvolved;

    // 构造函数
    public EnvSupervisionItemDTO() {
        super();
    }

    public EnvSupervisionItemDTO(String configItemId, String itemLevelCode, String parentId, String itemName,
                                String result, String problemDesc) {
        this.configItemId = configItemId;
        this.itemLevelCode = itemLevelCode;
        this.parentId = parentId;
        this.itemName = itemName;
        this.result = result;
        this.problemDesc = problemDesc;
    }

    // Getter和Setter方法
    public String getLocalCheckId() {
        return localCheckId;
    }

    public void setLocalCheckId(String localCheckId) {
        this.localCheckId = localCheckId;
    }

    public String getConfigItemId() {
        return configItemId;
    }

    public void setConfigItemId(String configItemId) {
        this.configItemId = configItemId;
    }

    public String getItemLevelCode() {
        return itemLevelCode;
    }

    public void setItemLevelCode(String itemLevelCode) {
        this.itemLevelCode = itemLevelCode;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentTitle() {
        return parentTitle;
    }

    public void setParentTitle(String parentTitle) {
        this.parentTitle = parentTitle;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getProblemDesc() {
        return problemDesc;
    }

    public void setProblemDesc(String problemDesc) {
        this.problemDesc = problemDesc;
    }

    public Boolean getIsParentNotInvolved() {
        return isParentNotInvolved;
    }

    public void setIsParentNotInvolved(Boolean isParentNotInvolved) {
        this.isParentNotInvolved = isParentNotInvolved;
    }

    @Override
    public String toString() {
        return "EnvSupervisionItemDTO{" +
                "localCheckId='" + localCheckId + '\'' +
                ", configItemId='" + configItemId + '\'' +
                ", itemLevelCode='" + itemLevelCode + '\'' +
                ", parentId='" + parentId + '\'' +
                ", itemName='" + itemName + '\'' +
                ", result='" + result + '\'' +
                ", problemDesc='" + problemDesc + '\'' +
                '}';
    }
}
//----------↑↑↑↑↑↑-----
