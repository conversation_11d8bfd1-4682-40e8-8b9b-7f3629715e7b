package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.OopenInfo;
import org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.OopenTask;
import org.changneng.framework.frameworkbusiness.entity.OopenTaskWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.RmattersTaskNode;
import org.changneng.framework.frameworkbusiness.entity.RmattersTaskSearch;
import org.changneng.framework.frameworkbusiness.entity.RmattersTaskWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface RandomOpenIssuedRandomService {
	/**
	 * 查询 已下发双随机抽查信息
	 * @param seachBean
	 * @return
	 */
	public PageBean<RmattersTaskWithBLOBs> selectIssuedRandomList(RmattersTaskSearch seachBean);
	/**
	 * 根据id查询 已下发双随机抽查信息
	 * @param id
	 * @return
	 */
	public RmattersTaskWithBLOBs selectIssuedRandomInfo(String id );
	/**
	 * 查询该流转信息下的时间导航状态
	 * @param rTaskId   流转信息id
	 * @return
	 */
	public List<RmattersTaskNode> selectInfoByTaskId(String rTaskId );
	/**
	 * 新增/修改 执法记录
	 * @param rMattersTaskWithBLOBs
	 * @return
	 */
	int  addOrUpdateRmattersTaskNodeInfo(RmattersTaskWithBLOBs rMattersTaskWithBLOBs );
	
	public String uploadActionlRequireFiless(HttpServletRequest request, HttpServletResponse response,
			SysUsers sysUsers)throws BusinessException;
	public String deleteFileInfoById(String id,String recordId)throws BusinessException;
	/**
	 * 查询公开信息表初始化数据
	 * @param ids
	 * @return
	 */
	public List<OopenTaskWithBLOBs> selectMattersTaskList(String ids);
	/**
	 * 公开信息保存/编辑
	 * @param openInfo
	 * @return
	 * @throws Exception
	 */
	public int addOrUpdateOpenInfo(OopenInfoWithBLOBs openInfo)throws Exception;
	/**
	 * 通过主键id查询公开信息
	 * @param id
	 * @return
	 */
	public OopenInfoWithBLOBs selectOpenInfo(String id);
	/**
	 * 通过公开信息id查询任务列表
	 * @param id
	 * @return
	 */
	public List<OopenTaskWithBLOBs> selectOpenTaskList(String id);
	/**
	 * 通过查询条件导出excel
	 * @param seachBean
	 * @return
	 */
	public List<RmattersTaskWithBLOBs> getIssuedRandomDownloadList(RmattersTaskSearch seachBean);
	
	public void siLuInit()throws Exception;
}
