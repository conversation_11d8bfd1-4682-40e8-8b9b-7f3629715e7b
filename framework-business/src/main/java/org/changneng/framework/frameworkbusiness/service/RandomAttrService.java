package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.RandomPollutionDatabase;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

import com.github.pagehelper.PageInfo;

public interface RandomAttrService {
	
	/**
	 * 插入一条双随机属性
	 * @param record
	 * @return
	 */
    ResponseJson insertNewAttr(RandomPollutionDatabase record);
    
    /**
     * 更新一条
     * @param record
     * @return
     */
    ResponseJson updateAttr(RandomPollutionDatabase record);
    
    /**
     * 根据id删除一条
     * @param id
     * @return
     */
    ResponseJson deleteAttrById(String id);
    
    /**
     * 根据用户区划查询列表
     * @param belongAreaCode
     * @return
     */
    PageInfo<RandomPollutionDatabase> queryListByCondition(String belongAreaCode,String pageSize,String pageNum);
    
    /**
     * 根据id查询单个双随机属性
     * @param id
     * @return
     */
    RandomPollutionDatabase selectById(String id);
    
    /**
     * 根据污染源库名和所属区划id校验是否有同名的双随机属性存在
     * @param attrName
     * @param belongAreaId
     * @return
     */
    List<RandomPollutionDatabase> selectByNameAndBelongAreaCode(String attrName,String areacode);

}
