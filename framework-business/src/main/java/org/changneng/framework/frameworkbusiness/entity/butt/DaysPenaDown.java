package org.changneng.framework.frameworkbusiness.entity.butt;

import java.util.Date;


public class DaysPenaDown {
	
	// 地区编码不能为空
	// 地区编码长度不能超过8个字符
    private String dLoginareacode;
	
	// 年度不能为空
	// 年度长度不能超过4个字符
    private String dNiandu;
	
	// 月份不能为空
	// 月份长度不能超过2个字符
    private String dYuefen;
	
	// 是否公开不能为空
	// 是否公开长度不能超过2个字符
	// 是否公开不符合数据字典
    private String dIsgongkai;
	
	// 公开日期不符合时间约束
    private String dGongkairiqi;
	
    // 公开网站地址不能超过500个字符
    private String dGongkaiurl;
    
    // 公开方式长度不能超过2个字符
    // 公开方式不符合数据字典
    private String dGongkaifangshi;
    
    // 执行情况不能为空
    // 执行情况长度不能超过2个字符
    // 执行情况不符合数据字典
    private String dZhixingqingkuang;
    
    // 说明理由不能超过2000个字符
    private String dZhixingqingkuangdemo;
    
    // 执行完毕日期不符合时间约束
    private String dZhixingwanbiriqi;
    
    // 复议情况不能为空
    // 复议情况长度不能超过2个字符
    // 复议情况不符合数据字典
    private String dFuyiqingkuang;
    
    // 复议结果长度不能超过2个字符
    // 复议结果不符合数据字典
    private String dFuyijieguo;
    
    // 诉讼情况不能为空
    // 诉讼情况长度不能超过2个字符
    // 诉讼情况不符合数据字典
    private String dSusongqingkuang;
    
    // 诉讼结果长度不能超过2个字符
    // 诉讼结果不符合数据字典
    private String dSusongjieguo;
    
    // 移送信息不能为空
    // 移送信息长度不能超过25个字符
    private String dYisongxinxi;
    
    // 具体情形长度不能超过2个字符
    // 具体情形不符合数据字典
    private String dJutiqingxing;
    
    // 是否纳入银行征信系统不能为空
    // 是否纳入银行征信系统长度不能超过2个字符
    // 是否纳入银行征信系统不符合数据字典
    private String dIsnaru;
    
    // 结案日期不符合时间约束
    private String dJieanriqi;
    
    // 案卷号不能为空
    // 案卷号长度不能超过100个字符
    private String dAnjuanhao;
    
    // 案件材料不能为空
    // 案件材料长度不能超过200个字符
    private String dAttachfilename;
    
    // 案件材料不能为空
    // 案件材料长度不能超过100个字符
    private String dAttachfilename2;
    
    // 案件材料长度不能超过1000个字符
    private String dBeizhu;
    
    // 填报人不能为空
    // 填报人长度不能超过100个字符
    private String dTianbaoren;
    
    // 填报人联系方式不能为空
    // 填报人联系方式长度不能超过50个字符
    private String dTbrLianxifangshi;
    
    // 填报单位
    // 填报单位长度不能超过100个字符
    private String dTianbaodanwei;

    private Date dShangbaotime;

    private String dZhuangtai;

    private Integer dShenheinfoId;
    
    // 创建时间不符合时间约束
    private String createtime;

    private String delflag;

    private String dStatus;
    
    // 其他公开方式描述长度不能超过2000个字符
    private String dQitafangshi;

    private String dUrlIseffective;

    private Date updatetime;

    private String istransGk;

    private Date lastuploadtm;

    private Date lastuploadtm2;
    // 业务数据唯一标识不能为空
    // 业务数据唯一标识长度不能超过32个字符
    private String businessid;
    // 是否更新字段不能为空
    // 是否更新长度不能超过2个字符
    // 是否更新字段不符合字典约束
    private String isupdatefile;
    
	public String getIsupdatefile() {
		return isupdatefile;
	}

	public void setIsupdatefile(String isupdatefile) {
		this.isupdatefile = isupdatefile;
	}

	public String getdLoginareacode() {
		return dLoginareacode;
	}

	public void setdLoginareacode(String dLoginareacode) {
		this.dLoginareacode = dLoginareacode;
	}

	public String getdNiandu() {
		return dNiandu;
	}

	public void setdNiandu(String dNiandu) {
		this.dNiandu = dNiandu;
	}

	public String getdYuefen() {
		return dYuefen;
	}

	public void setdYuefen(String dYuefen) {
		this.dYuefen = dYuefen;
	}

	public String getdIsgongkai() {
		return dIsgongkai;
	}

	public void setdIsgongkai(String dIsgongkai) {
		this.dIsgongkai = dIsgongkai;
	}

	public String getdGongkairiqi() {
		return dGongkairiqi;
	}

	public void setdGongkairiqi(String dGongkairiqi) {
		this.dGongkairiqi = dGongkairiqi;
	}

	public String getdGongkaiurl() {
		return dGongkaiurl;
	}

	public void setdGongkaiurl(String dGongkaiurl) {
		this.dGongkaiurl = dGongkaiurl;
	}

	public String getdGongkaifangshi() {
		return dGongkaifangshi;
	}

	public void setdGongkaifangshi(String dGongkaifangshi) {
		this.dGongkaifangshi = dGongkaifangshi;
	}

	public String getdZhixingqingkuang() {
		return dZhixingqingkuang;
	}

	public void setdZhixingqingkuang(String dZhixingqingkuang) {
		this.dZhixingqingkuang = dZhixingqingkuang;
	}

	public String getdZhixingqingkuangdemo() {
		return dZhixingqingkuangdemo;
	}

	public void setdZhixingqingkuangdemo(String dZhixingqingkuangdemo) {
		this.dZhixingqingkuangdemo = dZhixingqingkuangdemo;
	}

	public String getdZhixingwanbiriqi() {
		return dZhixingwanbiriqi;
	}

	public void setdZhixingwanbiriqi(String dZhixingwanbiriqi) {
		this.dZhixingwanbiriqi = dZhixingwanbiriqi;
	}

	public String getdFuyiqingkuang() {
		return dFuyiqingkuang;
	}

	public void setdFuyiqingkuang(String dFuyiqingkuang) {
		this.dFuyiqingkuang = dFuyiqingkuang;
	}

	public String getdFuyijieguo() {
		return dFuyijieguo;
	}

	public void setdFuyijieguo(String dFuyijieguo) {
		this.dFuyijieguo = dFuyijieguo;
	}

	public String getdSusongqingkuang() {
		return dSusongqingkuang;
	}

	public void setdSusongqingkuang(String dSusongqingkuang) {
		this.dSusongqingkuang = dSusongqingkuang;
	}

	public String getdSusongjieguo() {
		return dSusongjieguo;
	}

	public void setdSusongjieguo(String dSusongjieguo) {
		this.dSusongjieguo = dSusongjieguo;
	}

	public String getdYisongxinxi() {
		return dYisongxinxi;
	}

	public void setdYisongxinxi(String dYisongxinxi) {
		this.dYisongxinxi = dYisongxinxi;
	}

	public String getdJutiqingxing() {
		return dJutiqingxing;
	}

	public void setdJutiqingxing(String dJutiqingxing) {
		this.dJutiqingxing = dJutiqingxing;
	}

	public String getdIsnaru() {
		return dIsnaru;
	}

	public void setdIsnaru(String dIsnaru) {
		this.dIsnaru = dIsnaru;
	}

	public String getdJieanriqi() {
		return dJieanriqi;
	}

	public void setdJieanriqi(String dJieanriqi) {
		this.dJieanriqi = dJieanriqi;
	}

	public String getdAnjuanhao() {
		return dAnjuanhao;
	}

	public void setdAnjuanhao(String dAnjuanhao) {
		this.dAnjuanhao = dAnjuanhao;
	}

	public String getdAttachfilename() {
		return dAttachfilename;
	}

	public void setdAttachfilename(String dAttachfilename) {
		this.dAttachfilename = dAttachfilename;
	}

	public String getdAttachfilename2() {
		return dAttachfilename2;
	}

	public void setdAttachfilename2(String dAttachfilename2) {
		this.dAttachfilename2 = dAttachfilename2;
	}

	public String getdBeizhu() {
		return dBeizhu;
	}

	public void setdBeizhu(String dBeizhu) {
		this.dBeizhu = dBeizhu;
	}

	public String getdTianbaoren() {
		return dTianbaoren;
	}

	public void setdTianbaoren(String dTianbaoren) {
		this.dTianbaoren = dTianbaoren;
	}

	public String getdTbrLianxifangshi() {
		return dTbrLianxifangshi;
	}

	public void setdTbrLianxifangshi(String dTbrLianxifangshi) {
		this.dTbrLianxifangshi = dTbrLianxifangshi;
	}

	public String getdTianbaodanwei() {
		return dTianbaodanwei;
	}

	public void setdTianbaodanwei(String dTianbaodanwei) {
		this.dTianbaodanwei = dTianbaodanwei;
	}

	public Date getdShangbaotime() {
		return dShangbaotime;
	}

	public void setdShangbaotime(Date dShangbaotime) {
		this.dShangbaotime = dShangbaotime;
	}

	public String getdZhuangtai() {
		return dZhuangtai;
	}

	public void setdZhuangtai(String dZhuangtai) {
		this.dZhuangtai = dZhuangtai;
	}

	public Integer getdShenheinfoId() {
		return dShenheinfoId;
	}

	public void setdShenheinfoId(Integer dShenheinfoId) {
		this.dShenheinfoId = dShenheinfoId;
	}

	public String getCreatetime() {
		return createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	public String getDelflag() {
		return delflag;
	}

	public void setDelflag(String delflag) {
		this.delflag = delflag;
	}

	public String getdStatus() {
		return dStatus;
	}

	public void setdStatus(String dStatus) {
		this.dStatus = dStatus;
	}

	public String getdQitafangshi() {
		return dQitafangshi;
	}

	public void setdQitafangshi(String dQitafangshi) {
		this.dQitafangshi = dQitafangshi;
	}

	public String getdUrlIseffective() {
		return dUrlIseffective;
	}

	public void setdUrlIseffective(String dUrlIseffective) {
		this.dUrlIseffective = dUrlIseffective;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public String getIstransGk() {
		return istransGk;
	}

	public void setIstransGk(String istransGk) {
		this.istransGk = istransGk;
	}

	public Date getLastuploadtm() {
		return lastuploadtm;
	}

	public void setLastuploadtm(Date lastuploadtm) {
		this.lastuploadtm = lastuploadtm;
	}

	public Date getLastuploadtm2() {
		return lastuploadtm2;
	}

	public void setLastuploadtm2(Date lastuploadtm2) {
		this.lastuploadtm2 = lastuploadtm2;
	}

	public String getBusinessid() {
		return businessid;
	}

	public void setBusinessid(String businessid) {
		this.businessid = businessid;
	}

	@Override
	public String toString() {
		return "DaysPenaDown [dLoginareacode=" + dLoginareacode + ", dNiandu=" + dNiandu + ", dYuefen=" + dYuefen
				+ ", dIsgongkai=" + dIsgongkai + ", dGongkairiqi=" + dGongkairiqi + ", dGongkaiurl=" + dGongkaiurl
				+ ", dGongkaifangshi=" + dGongkaifangshi + ", dZhixingqingkuang=" + dZhixingqingkuang
				+ ", dZhixingqingkuangdemo=" + dZhixingqingkuangdemo + ", dZhixingwanbiriqi=" + dZhixingwanbiriqi
				+ ", dFuyiqingkuang=" + dFuyiqingkuang + ", dFuyijieguo=" + dFuyijieguo + ", dSusongqingkuang="
				+ dSusongqingkuang + ", dSusongjieguo=" + dSusongjieguo + ", dYisongxinxi=" + dYisongxinxi
				+ ", dJutiqingxing=" + dJutiqingxing + ", dIsnaru=" + dIsnaru + ", dJieanriqi=" + dJieanriqi
				+ ", dAnjuanhao=" + dAnjuanhao + ", dAttachfilename=" + dAttachfilename + ", dAttachfilename2="
				+ dAttachfilename2 + ", dBeizhu=" + dBeizhu + ", dTianbaoren=" + dTianbaoren + ", dTbrLianxifangshi="
				+ dTbrLianxifangshi + ", dTianbaodanwei=" + dTianbaodanwei + ", dShangbaotime=" + dShangbaotime
				+ ", dZhuangtai=" + dZhuangtai + ", dShenheinfoId=" + dShenheinfoId + ", createtime=" + createtime
				+ ", delflag=" + delflag + ", dStatus=" + dStatus + ", dQitafangshi=" + dQitafangshi
				+ ", dUrlIseffective=" + dUrlIseffective + ", updatetime=" + updatetime + ", istransGk=" + istransGk
				+ ", lastuploadtm=" + lastuploadtm + ", lastuploadtm2=" + lastuploadtm2 + ", businessid=" + businessid
				+ ", isupdatefile=" + isupdatefile + "]";
	}
	
}