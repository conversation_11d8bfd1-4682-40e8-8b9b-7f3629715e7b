package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

public class EnterpriseRandomDatabase {
	private String id;

	private String databaseId;

	private String enterpriseId;

	private String isExtracted;

	private Integer extractedTimes;

	private Date lastExtractedDate;

	private String state;

	private Date updateDate;

	private String operUserId;

	private String dangerCoefficient;

	private String belongAreaId;

	private String belongAreaName;

	private String attrName;

	private Integer attrType;
	
	private String attrTypeName;
	
	private String lawTypeCode;
	


	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	public String getDatabaseId() {
		return databaseId;
	}

	public void setDatabaseId(String databaseId) {
		this.databaseId = databaseId == null ? null : databaseId.trim();
	}

	public String getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(String enterpriseId) {
		this.enterpriseId = enterpriseId == null ? null : enterpriseId.trim();
	}

	public String getIsExtracted() {
		return isExtracted;
	}

	public void setIsExtracted(String isExtracted) {
		this.isExtracted = isExtracted == null ? null : isExtracted.trim();
	}

	public Integer getExtractedTimes() {
		return extractedTimes;
	}

	public void setExtractedTimes(Integer extractedTimes) {
		this.extractedTimes = extractedTimes;
	}

	public Date getLastExtractedDate() {
		return lastExtractedDate;
	}

	public void setLastExtractedDate(Date lastExtractedDate) {
		this.lastExtractedDate = lastExtractedDate;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state == null ? null : state.trim();
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperUserId() {
		return operUserId;
	}

	public void setOperUserId(String operUserId) {
		this.operUserId = operUserId == null ? null : operUserId.trim();
	}

	public String getDangerCoefficient() {
		return dangerCoefficient;
	}

	public void setDangerCoefficient(String dangerCoefficient) {
		this.dangerCoefficient = dangerCoefficient == null ? null : dangerCoefficient.trim();
	}

	public String getBelongAreaId() {
		return belongAreaId;
	}

	public void setBelongAreaId(String belongAreaId) {
		this.belongAreaId = belongAreaId == null ? null : belongAreaId.trim();
	}

	public String getBelongAreaName() {
		return belongAreaName;
	}

	public void setBelongAreaName(String belongAreaName) {
		this.belongAreaName = belongAreaName == null ? null : belongAreaName.trim();
	}

	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public Integer getAttrType() {
		return attrType;
	}

	public void setAttrType(Integer attrType) {
		this.attrType = attrType;
	}

	public String getAttrTypeName() {
		return attrTypeName;
	}

	public void setAttrTypeName(String attrTypeName) {
		this.attrTypeName = attrTypeName;
	}

	public String getLawTypeCode() {
		return lawTypeCode;
	}

	public void setLawTypeCode(String lawTypeCode) {
		this.lawTypeCode = lawTypeCode;
	}

}