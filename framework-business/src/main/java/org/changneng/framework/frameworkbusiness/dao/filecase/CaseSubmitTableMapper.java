package org.changneng.framework.frameworkbusiness.dao.filecase;

import org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable;

public interface CaseSubmitTableMapper {
    int deleteByPrimaryKey(String id);

    int insert(CaseSubmitTable record);

    int insertSelective(CaseSubmitTable record);

    CaseSubmitTable selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CaseSubmitTable record);

    int updateByPrimaryKey(CaseSubmitTable record);
}