package org.changneng.framework.frameworkbusiness.dao.filecase;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry;

public interface CFileEntryMapper {
    int deleteByPrimaryKey(String id);

    int insert(CFileEntry record);

    int insertSelective(CFileEntry record);

    CFileEntry selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CFileEntry record);

    int updateByPrimaryKey(CFileEntry record);
    
    List<CFileEntry> selectAll();
    
    CFileEntry selectByIdAndName(@Param("entryId") String entryId,@Param("entryName") String entryName);
    
    int selectMaxLocation();
    
    int deleteAll();
    List<CFileEntry> selectAvailableDataList();
}