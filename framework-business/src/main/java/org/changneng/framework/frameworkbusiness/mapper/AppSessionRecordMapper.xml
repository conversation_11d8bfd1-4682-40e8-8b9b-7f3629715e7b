<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.AppSessionRecordMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.AppSessionRecord">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="LOGIN_TOKEN" jdbcType="VARCHAR" property="loginToken" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID,USER_ID,USER_NAME, LOGIN_TOKEN, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APP_SESSION_RECORD
    where USER_NAME = #{username,jdbcType=VARCHAR}
  </select>
  
  <select id="queryUsernameList" parameterType="java.lang.String" resultMap="BaseResultMap">
  	  select 
    <include refid="Base_Column_List" />
     from APP_SESSION_RECORD
   where USER_ID = #{userid,jdbcType=VARCHAR}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from APP_SESSION_RECORD
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  
  <delete id="deleteByLoginToken" parameterType="java.lang.String">
    delete from APP_SESSION_RECORD
    where login_token = #{loginToken,jdbcType=VARCHAR}
  </delete>
  
  <delete id="deleteByUserid" parameterType="java.lang.String">
  	delete from APP_SESSION_RECORD
    where USER_ID = #{userid,jdbcType=VARCHAR}
  </delete>
  
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.AppSessionRecord">
    insert into APP_SESSION_RECORD (ID,USER_ID,USER_NAME, LOGIN_TOKEN, 
      CREATE_TIME)
    values (#{id,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{loginToken,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.AppSessionRecord">
  	 <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into APP_SESSION_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="loginToken != null">
        LOGIN_TOKEN,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
       <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="loginToken != null">
        #{loginToken,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.AppSessionRecord">
    update APP_SESSION_RECORD
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="loginToken != null">
        LOGIN_TOKEN = #{loginToken,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.AppSessionRecord">
    update APP_SESSION_RECORD
    set USER_ID = #{userId,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      LOGIN_TOKEN = #{loginToken,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>