package org.changneng.framework.frameworkbusiness.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.AppSysUsers;
import org.changneng.framework.frameworkbusiness.entity.CaseLawUserSearch;
import org.changneng.framework.frameworkbusiness.entity.CheckUserChooseBean;
import org.changneng.framework.frameworkbusiness.entity.SysUserSearchCondition;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;

public interface SysUsersMapper {
    int deleteByPrimaryKey(String id);

    int insert(SysUsers record);

    int insertSelective(SysUsers record);

    SysUsers selectByPrimaryKey(String id);

	SysUsers selectGmByPrimaryKey(String id);

    int updateByPrimaryKeySelective(SysUsers record);
    
    int updateByPrimaryKeySelectiveDateAndIp(SysUsers record);

    int updateByPrimaryKey(SysUsers record);
    
    int getUpdateUserByYesterdayTotal(@Param("yesterday")Date yesterday);
    
    SysUsers getByLoginname(String loginname);
    
    SysUsers getByUsername(String loginname);
    
    List<SysUsers> getSysUserAll();
    
    Date getMaxLastUpdateTimeForSilu();
    
    SysUsers checkUsernameForUpdate(@Param("loginname")String loginname,@Param("updateid")String updateid);
    
    void resetPassword(SysUsers record);
    
    List<SysUsers> querySystemUsers(@Param("search")SysUserSearchCondition search);
    
    List<SysUsers> getUpdateUserByYesterday(@Param("yesterday")Date yesterday,
    		 @Param("currPage") int currPage,
			 @Param("pageSize") int pageSize);
    
    /**
     * 导出用户信息列表
     * @param search
     * @return
     */
    List<SysUsers> queryExeclList(@Param("search")SysUserSearchCondition search);
    
    void enabledAndDisabledUser(@Param("id")String id,@Param("type")Integer type);
    
    void updateEnableById(@Param("id")String id);
    
    void updatePersonalInfo(SysUsers record);
    
    void updateSnsStateById(@Param("id")String id);
    
	//areaCode,userName,citySubCode,status
/*	List<CheckUserChooseBean> CheckUserChooseAllList(
			@Param("areaCode") String areaCode,
			@Param("userName") String userName,
			@Param("citySubCode")  String citySubCode,
			@Param("status") String status, @Param("sysUserId") String sysUserId);*/

	//List<CheckUserChooseBean> CheckUserChooseList(@Param("areaCode") String areaCode,
	//		@Param("status") 	String status, @Param("citySubCode") String citySubCode, @Param("sysUserId") String sysUserId);
    //areaCode,userName,citySubCode,levelstatus,
	List<CheckUserChooseBean> CheckUserChooseList(@Param("areaCode")String areaCode,
			@Param("levelstatus")String levelstatus,
			@Param("citySubCode")String citySubCode,
			@Param("sysUserId")String sysUserId, 
			@Param("currPage")int currPage,
			@Param("pageSize") int pageSize);

	int CheckUserTotal(@Param("areaCode")String areaCode,
			@Param("levelstatus") String levelstatus,
			@Param("citySubCode")String citySubCode,
			 @Param("sysUserId")String sysUserId);

	List<CheckUserChooseBean> CheckUserChooseAllList(@Param("areaCode") String areaCode,
			@Param("userName")String userName,
			@Param("citySubCode")String citySubCode, 
			@Param("levelstatus")String levelstatus,
			@Param("sysUserId") String sysUserId,
			 @Param("currPage")int currPage,
			 @Param("pageSize") int pageSize);
	
	
	/**
	 * 适用与 案件：重要执法人员
	 * @param searchBean
	 * @return
	 * <AUTHOR>
	 * @date 2018年6月5日-下午1:52:49
	 */
	List<CheckUserChooseBean> getCheckUserChooseAllList(@Param("searchBean")CaseLawUserSearch searchBean);

	int CheckUserAllTotal(	@Param("areaCode")String areaCode,	
			@Param("userName") String userName, 
			@Param("citySubCode")String citySubCode,
			@Param("levelstatus")String levelstatus, 
			@Param("sysUserId") String sysUserId);

	List<SysUsers> getAreaList(@Param("checUserIds")String checUserIds);

	/**
	 * 每日重置用户登录状态
	 */
	void resetLoginState();

	
	List<AppSysUsers> getUserByDepartmentNumber(@Param("departmentNumber")String departmentNumber,@Param("sysUserId") String sysUserId);
	
	/**
	 * 案件 检查人所有
	 * @param areaCode
	 * @param levelstatus
	 * @param citySubCode
	 * @param object
	 * @param i
	 * @param j
	 * @return
	 */
	List<CheckUserChooseBean> getCaseChickUserList(
			 		@Param("areaCode")String areaCode,
					@Param("levelstatus")String levelstatus,
					@Param("citySubCode")String citySubCode,
					@Param("sysUserId")String sysUserId, 
					@Param("currPage")int currPage,
					@Param("pageSize") int pageSize);
	
	/**
	 * 案件检查人 所有总数
	 * @param areaCode
	 * @param levelstatus
	 * @param citySubCode
	 * @param object
	 * @return
	 */
	int getCaseChickUserCount(@Param("areaCode")String areaCode,
			@Param("levelstatus") String levelstatus,
			@Param("citySubCode")String citySubCode,
			 @Param("sysUserId")String sysUserId);
	/**
	 * 案件 检查人默认
	 * @param areaCode
	 * @param userName
	 * @param citySubCode
	 * @param levelstatus
	 * @param object
	 * @param i
	 * @param j
	 * @return
	 */
	List<CheckUserChooseBean> getCaseChickUserAllList(
			@Param("areaCode") String areaCode,
			@Param("userName")String userName,
			@Param("citySubCode")String citySubCode, 
			@Param("levelstatus")String levelstatus,
			@Param("sysUserId") String sysUserId,
			@Param("currPage")int currPage,
			@Param("pageSize") int pageSize);
	
	/**
	 * 案件 检查人默然总数
	 * @param areaCode
	 * @param userName
	 * @param citySubCode
	 * @param levelstatus
	 * @param object
	 * @return
	 */
	int getCaseChickUserAllCount(
			@Param("areaCode")String areaCode,	
			@Param("userName") String userName, 
			@Param("citySubCode")String citySubCode,
			@Param("levelstatus")String levelstatus, 
			@Param("sysUserId") String sysUserId);
	/**
	 * 根据联系人id查询用户信息
	 * @param id
	 * @return
	 */
	public SysUsers getSysUserByInformantId(String id);

	/**
	 * 查出来用户所属部门（厅局级）
	 * @param id
	 * @return
	 * <AUTHOR>
	 */
	String queryUserDeptName(String id);

	/**
	 * 根据一个区划list查询出所属的用户ids
	 * @param areaCodeList
	 * @return
	 * 
	 * <AUTHOR>
	 */
	List<String> selectUserIdsByAreaCodes(@Param("areaCodeList")List<String> areaCodeList);
	/**
	 * 根据执法圈用户id查找所在编制部门
	 * @param areaCodeList
	 * @return
	 * 
	 * <AUTHOR>
	 */
	SysUsers selectDepartName(String creatUserId);
	//根据事项id拿到部门id去取本部门下的人员
	List<SysUsers> getUserList(@Param("itemId")String itemId);
	/**
	 * 查询思路用户
	 * @return
	 */
	List<SysUsers> selectBySiluUser();
	/**
	 * 2019-6-12 一园一档   队伍信息中执法人员来自执法系统(根据所属行政区查询执法人员)
	 * @param code
	 * @return
	 */
	List<SysUsers> getLawPerson(@Param("code")String code);


	//根据手机号获取用户信息

	List<SysUsers> getUserByPhone(@Param("phone")String phone);
}