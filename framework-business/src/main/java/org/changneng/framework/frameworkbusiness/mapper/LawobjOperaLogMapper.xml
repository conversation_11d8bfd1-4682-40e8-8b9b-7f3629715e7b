<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.LawobjOperaLogMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OPER_DATE" jdbcType="TIMESTAMP" property="operDate" />
    <result column="LAWOBJ_NAME" jdbcType="VARCHAR" property="lawobjName" />
    <result column="OPERATION_TYPE" jdbcType="VARCHAR" property="operationType" />
    <result column="OPERA_MAN" jdbcType="VARCHAR" property="operaMan" />
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="OPERA_DEPARTMENT" jdbcType="VARCHAR" property="operaDepartment" />
    <result column="OPERA_BELONGAREA" jdbcType="VARCHAR" property="operaBelongarea" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OPER_DATE, LAWOBJ_NAME, OPERATION_TYPE, OPERA_MAN, OPERA_DEPARTMENT, OPERA_BELONGAREA, AREA_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from LAWOBJ_OPERA_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from LAWOBJ_OPERA_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog">
    insert into LAWOBJ_OPERA_LOG (ID, OPER_DATE, LAWOBJ_NAME, 
      OPERATION_TYPE, OPERA_MAN, OPERA_DEPARTMENT, 
      OPERA_BELONGAREA,AREA_CODE)
    values (#{id,jdbcType=VARCHAR}, #{operDate,jdbcType=TIMESTAMP}, #{lawobjName,jdbcType=VARCHAR}, 
      #{operationType,jdbcType=VARCHAR}, #{operaMan,jdbcType=VARCHAR}, #{operaDepartment,jdbcType=VARCHAR}, 
      #{operaBelongarea,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog">
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into LAWOBJ_OPERA_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="operDate != null">
        OPER_DATE,
      </if>
      <if test="lawobjName != null">
        LAWOBJ_NAME,
      </if>
      <if test="operationType != null">
        OPERATION_TYPE,
      </if>
      <if test="operaMan != null">
        OPERA_MAN,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="operaDepartment != null">
        OPERA_DEPARTMENT,
      </if>
      <if test="operaBelongarea != null">
        OPERA_BELONGAREA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="operDate != null">
        #{operDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawobjName != null">
        #{lawobjName,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="operaMan != null">
        #{operaMan,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="operaDepartment != null">
        #{operaDepartment,jdbcType=VARCHAR},
      </if>
      <if test="operaBelongarea != null">
        #{operaBelongarea,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog">
    update LAWOBJ_OPERA_LOG
    <set>
      <if test="operDate != null">
        OPER_DATE = #{operDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawobjName != null">
        LAWOBJ_NAME = #{lawobjName,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="operaMan != null">
        OPERA_MAN = #{operaMan,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="operaDepartment != null">
        OPERA_DEPARTMENT = #{operaDepartment,jdbcType=VARCHAR},
      </if>
      <if test="operaBelongarea != null">
        OPERA_BELONGAREA = #{operaBelongarea,jdbcType=VARCHAR},
      </if>
      
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog">
    update LAWOBJ_OPERA_LOG
    set OPER_DATE = #{operDate,jdbcType=TIMESTAMP},
      LAWOBJ_NAME = #{lawobjName,jdbcType=VARCHAR},
      OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      OPERA_MAN = #{operaMan,jdbcType=VARCHAR},
      AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      OPERA_DEPARTMENT = #{operaDepartment,jdbcType=VARCHAR},
      OPERA_BELONGAREA = #{operaBelongarea,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <select id="selectListByQueryVo" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from LAWOBJ_OPERA_LOG
    <where>

      <if test="objectName != null and objectName != ''">
        AND LAWOBJ_NAME LIKE '%'||#{objectName,jdbcType=VARCHAR}||'%'
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        AND OPERA_MAN LIKE '%'||#{updateUserName,jdbcType=VARCHAR}||'%'
      </if>
      <if test="areaCode != null and areaCode !=''">
        AND AREA_CODE LIKE #{areaCode,jdbcType=VARCHAR}||'%'
      </if>
      <if test="searchStartTime != null and searchStartTime != ''">
        AND OPER_DATE&gt;= to_date(#{searchStartTime},'yyyy-MM-dd hh24:mi:ss')
      </if>
      <if test="searchEndTime != null and searchEndTime !=''">
        AND OPER_DATE&lt;= to_date(#{searchEndTime},'yyyy-MM-dd hh24:mi:ss')
      </if>
      <if test="operationType != null and operationType != ''">
        AND OPERATION_TYPE = #{operationType, jdbcType=VARCHAR}
      </if>
    </where>
        ORDER BY OPER_DATE DESC,ID DESC
  </select>
</mapper>