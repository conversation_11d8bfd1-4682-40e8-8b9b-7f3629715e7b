package org.changneng.framework.frameworkbusiness.entity.supervise;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

public class TestPagerModel {
	 public static void main(String args[]) {  
		    List<String> list = new ArrayList<String>();  
		    list.add("a");  
		   list.add("c");  
		    list.add("d");  
		    list.add("e");  
		    list.add("f");  
		    list.add("g");  
		    list.add("h");  
		    list.add("h");  
		    list.add("i");  
		    list.add("j");  
		    list.add("k");  
		    list.add("l");  
		    list.add("m");  
		    /*PageModel pm = new PageModel(list,3);  
		    
		    System.out.println(pm.getTotalPages());
		    
		    List sublist = pm.getObjects(2);  
		    for(int i = 0; i < sublist.size(); i++) {  
		      System.out.println(sublist.get(i));  
		    }  */
		    String str = StringUtils.collectionToDelimitedString(list, ","); 
		    
		    System.out.println(str);
		  }  
}
