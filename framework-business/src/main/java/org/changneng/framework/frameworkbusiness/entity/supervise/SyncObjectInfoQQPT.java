package org.changneng.framework.frameworkbusiness.entity.supervise;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SyncObjectInfoQQPT {
    @JsonProperty("STANDENTERID")	 //固定污染源统一编码
    private String standenterid;
    @JsonProperty("ENTERCODE") // 固定源编码（国家下发）
    private String entercode;
    @JsonProperty("ENTERNAME") // 企业名称
    private String entername;
    @JsonProperty("TYSHXYDM") // 统一社会信用代码
    private String tyshxydm;
    @JsonProperty("CORPCODE") // 组织机构代码
    private String  corpcode;
    @JsonProperty("LICENSENUM") // 排污许可证代码
    private String  licensenum;
    @JsonProperty("CODE_REGION_SHI") // 地市代码
    private String code_region_shi;
    @JsonProperty("REGIONNAME_SHI") // 地市名称
    private String regionname_shi;
    @JsonProperty("CODE_REGION") // 行政区编码（现使用版本是2019年3月民政部获取，与生态环境部统一）
    private String code_region;
    @JsonProperty("ENTERADDRESS") // 企业地址
    private String enteraddress;
    @JsonProperty("REGIONNAME") // 行政区名称
    private String regionname;
    @JsonProperty("CODE_TRADE") // 行业编码（现使用版本是2019年3月国家发改委发布，与生态环境部统一）
    private String code_trade;
    @JsonProperty("TRADENAME") // 行业名称
    private String tradename;
    @JsonProperty("LONGITUDE") // 经度
    private String longitude;
    @JsonProperty("LATITUDE") // 纬度
    private String latitude;
    @JsonProperty("STATUS") // 固定源编码
    private String status;
    @JsonProperty("STATUSNAME") // 固定源编码名称
    private String statusname;
    @JsonProperty("UPDATETIME") // 更新时间
    private String updatetime;

//
//    @JsonProperty("LINKMAN") // 联系人
//    private String linkman;
//    @JsonProperty("TELEPHONE") //电话
//    private String telephone;
//    @JsonProperty("FAX") //传真
//    private String fax;
//
//    @JsonProperty("CODE_WSYSTEM") // 流域编码（标准待定）
//    private String code_wsystem;
//    @JsonProperty("WSYSTEMNAME") // 流域名称
//    private String wsystemname;
//
//    @JsonProperty("DATASOURCE") // 企业来源系统
//    private String datasource;
//    @JsonProperty("ISDELETE") // 删除状态（0未删除；1已删除）
//    private String isdelete;
//    @JsonProperty("LS2_STANDENTERID") // 二期污染源统一编码
//    private String ls2_standenterid;
//    @JsonProperty("LS_STANDENTERID") // 一期污染源统一编码
//    private String ls_standenterid;


    public String getStandenterid() {
        return standenterid;
    }

    public void setStandenterid(String standenterid) {
        this.standenterid = standenterid;
    }

    public String getEntercode() {
        return entercode;
    }

    public void setEntercode(String entercode) {
        this.entercode = entercode;
    }

    public String getEntername() {
        return entername;
    }

    public void setEntername(String entername) {
        this.entername = entername;
    }

    public String getTyshxydm() {
        return tyshxydm;
    }

    public void setTyshxydm(String tyshxydm) {
        this.tyshxydm = tyshxydm;
    }

    public String getCorpcode() {
        return corpcode;
    }

    public void setCorpcode(String corpcode) {
        this.corpcode = corpcode;
    }

    public String getLicensenum() {
        return licensenum;
    }

    public void setLicensenum(String licensenum) {
        this.licensenum = licensenum;
    }

    public String getCode_region_shi() {
        return code_region_shi;
    }

    public void setCode_region_shi(String code_region_shi) {
        this.code_region_shi = code_region_shi;
    }

    public String getRegionname_shi() {
        return regionname_shi;
    }

    public void setRegionname_shi(String regionname_shi) {
        this.regionname_shi = regionname_shi;
    }

    public String getCode_region() {
        return code_region;
    }

    public void setCode_region(String code_region) {
        this.code_region = code_region;
    }

    public String getEnteraddress() {
        return enteraddress;
    }

    public void setEnteraddress(String enteraddress) {
        this.enteraddress = enteraddress;
    }

    public String getRegionname() {
        return regionname;
    }

    public void setRegionname(String regionname) {
        this.regionname = regionname;
    }

    public String getCode_trade() {
        return code_trade;
    }

    public void setCode_trade(String code_trade) {
        this.code_trade = code_trade;
    }

    public String getTradename() {
        return tradename;
    }

    public void setTradename(String tradename) {
        this.tradename = tradename;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusname() {
        return statusname;
    }

    public void setStatusname(String statusname) {
        this.statusname = statusname;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }
}
