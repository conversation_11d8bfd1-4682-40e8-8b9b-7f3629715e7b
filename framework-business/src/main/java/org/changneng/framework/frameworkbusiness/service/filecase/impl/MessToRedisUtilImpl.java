package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.changneng.framework.frameworkbusiness.entity.notice.PushMessEntity;
import org.changneng.framework.frameworkbusiness.service.PushService;
import org.changneng.framework.frameworkbusiness.service.filecase.MessToRedisService;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 往redis中存入通知消息推送的轮盘工具类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018年1月11日 上午10:54:14
 */
@Service
public class MessToRedisUtilImpl implements MessToRedisService {
	/**
	 * 晚上发送的定时统一放到redis轮盘中第8个格推送
	 */
	private static final int redisIndex = 7;
	
	/**
	 * 大轮盘默认添加下标
	 */
	private static final int plusOneIndex = 1;
	/**
	 * 小轮盘默认添加下标
	 */
	private static final int plusTwoIndex = 1;

	/**
	 * reids 中24 小时，按照一天为单位发送的轮盘，一共24个格子 list 下标【0-23】个下标
	 */
	private static final String rouletteTwentyFourHours = "roulette-twenty-four-hours-";

	/**
	 * reids 中2分钟，按照一个小时为单位发送的轮盘，一共20个格子 list 下标【0-29】个下标
	 */
	private static final String rouletteThreeMinute = "roulette-three-minute-";

	@Autowired
	private RedisTemplate<String, Map> redisTemplate;
	
	@Autowired
	private PushService pushService;

	@Override
	public void chengeRedisPushList24(List<PushMessEntity> objList, Integer index) {
		if (index == null) { // 如果 没有指定位置，那么都放在8点
			index = redisIndex;
		}
		System.out.println("-----------------------------------------------------------------------------小时："+index+"----------------------------------------------------------------------------------------------");
		//insertRedisSendMessage(objList, rouletteTwentyFourHours, index);
	}

	@Override
	public void chengeRedisPushList3(List<PushMessEntity> objList, Integer index) {
		if (index == null || index == 29) { // 如果 没有指定位置，那么都放在0格子，0-3分钟
			index = 0;
		}else{
			index = index + plusTwoIndex;
		}
		System.out.println("-----------------------------------------------------------------------------分钟："+index+"----------------------------------------------------------------------------------------------");
		//insertRedisSendMessage(objList, rouletteThreeMinute, index);
	}

	private void insertRedisSendMessage(List<PushMessEntity> infoList, String redisKey, int index) {
		try {
			Map<String, List<PushMessEntity>> mapEightAll = redisTemplate.opsForValue().get(redisKey + index);
			// 如果redis中不存在，则第一次则放入新的
			if (mapEightAll == null || mapEightAll.isEmpty()) {
				mapEightAll = new HashMap<>();
				mapEightAll.put(redisKey + index, infoList);
				redisTemplate.opsForValue().set(redisKey + index, mapEightAll);
				return;
			}
			// 如果redis 中存在，那么把这个下标的信息去除，从新组装然后在放入
			List<PushMessEntity> tmpList = mapEightAll.get(redisKey + index);
			// 组装为整体的一个list
			tmpList.addAll(infoList);
			// 在加入轮盘集合中
			mapEightAll.put(redisKey + index, tmpList);
			redisTemplate.opsForValue().set(redisKey + index, mapEightAll);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void sendRedisPushList24(Integer index) {
		Map<String, List<PushMessEntity>> mapEightAll = redisTemplate.opsForValue().get(rouletteTwentyFourHours + index);
		if (mapEightAll!=null && !mapEightAll.isEmpty()) {
			List<PushMessEntity> newInfoList = mapEightAll.get(rouletteTwentyFourHours + index);
			try {
				if(newInfoList!=null && !newInfoList.isEmpty()){ // 轮盘信息存在则开始调用极光推送
					redisTemplate.delete(rouletteTwentyFourHours + index);
					List<PushMessEntity> errorList = pushService.standradPushAlertByRegisterIds(newInfoList); 
					// 1.调用完极光推送后，先清除当前轮盘下标的内容，如果有错误的，追加到下一个格中发送（也就是下个小时）
					if(!errorList.isEmpty()){
						insertRedisSendMessage(errorList, rouletteTwentyFourHours, index+plusOneIndex);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
	}

	@Override
	public void sendRedisPushList3(Integer index) {
		Map<String, List<PushMessEntity>> mapEightAll = redisTemplate.opsForValue().get(rouletteThreeMinute + index);
		if (mapEightAll!=null && !mapEightAll.isEmpty()) {
			List<PushMessEntity> newInfoList = mapEightAll.get(rouletteThreeMinute + index);
			try {
				if(newInfoList!=null && !newInfoList.isEmpty()){ // 轮盘信息存在则开始调用极光推送
					redisTemplate.delete(rouletteThreeMinute + index);
					List<PushMessEntity> errorList = pushService.standradPushAlertByRegisterIds(newInfoList); 
					// 1.调用完极光推送后，先清除当前轮盘下标的内容，如果有错误的，追加到下一个格中发送（也就是下个小时）
					if(!errorList.isEmpty()){
						insertRedisSendMessage(errorList, rouletteThreeMinute, index+plusTwoIndex);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public static int getIndexMinute(){
		return  DateUtil.minute() / 2;
	}
	
	public static int getIndexHours(){
		Date time = new Date();
		return  time.getHours();
	}
	
	
	public static void main(String[] args) {
		 System.out.println(getIndexHours()+"----------"+getIndexMinute());
	}


}
