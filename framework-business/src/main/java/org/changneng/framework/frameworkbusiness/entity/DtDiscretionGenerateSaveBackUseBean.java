package org.changneng.framework.frameworkbusiness.entity;
/** 
* <AUTHOR> 
* @version 2018年12月3日 下午2:03:55 
* 类说明      保存到 自由裁量生产数据表 时返回保存数据的id和生成图片的id
*/
public class DtDiscretionGenerateSaveBackUseBean {
	private String  dataId;
	
	private String pictureId;
	
	private String excption;
	
	
	public String getExcption() {
		return excption;
	}

	public void setExcption(String excption) {
		this.excption = excption;
	}

	public String getDataId() {
		return dataId;
	}

	public void setDataId(String dataId) {
		this.dataId = dataId;
	}

	public String getPictureId() {
		return pictureId;
	}

	public void setPictureId(String pictureId) {
		this.pictureId = pictureId;
	}
	
	
}
