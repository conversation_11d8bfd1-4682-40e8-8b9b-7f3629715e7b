package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.AppSessionRecord;

public interface AppSessionRecordMapper {
    int deleteByPrimaryKey(String id);

    int insert(AppSessionRecord record);

    int insertSelective(AppSessionRecord record);

    AppSessionRecord selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(AppSessionRecord record);

    int updateByPrimaryKey(AppSessionRecord record);
    
    int deleteByLoginToken(String loginToken);
    
    int deleteByUserid(String userid);
    
    List<AppSessionRecord> queryUsernameList(String userid);
}