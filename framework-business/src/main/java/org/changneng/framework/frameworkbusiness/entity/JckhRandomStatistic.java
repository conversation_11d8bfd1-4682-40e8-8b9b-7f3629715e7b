package org.changneng.framework.frameworkbusiness.entity;

import java.io.Serializable;
import java.util.Date;

public class JckhRandomStatistic implements Serializable {
    private String id;
	
	/**
	 * 更新时间（查询时间，更新到统计表中的时间）
	 */
    private Date updateDate;
	
    /**
	 * 执法部门id
	 */
    private String lawDeptId;
	
	/**
	 * 执法部门名称
	 */
    private String lawDeptName;
	
	/**
	 * 所属区划code
	 */
    private String belongCode;
	
	/**
	 * 所属区划名称
	 */
    private String belongName;
	
	/**
	 * 部门编码
	 */
    private String departmentNumber;

	/**
	 * 机构总人数(sys_users表统计)
	 */
    private Integer deptUserCount;

	/**
	 * 双随机执法人员数（random_user_count表查）
	 */
    private Integer deptRandomUserCount;

	/**
	 * 在编在岗人数
	 */
    private Integer deptOnJobUserCount;

	/**
	 * 本部门双随机总对象数
	 */
    private Integer randomObjCount;

	/**
	 * 本部门重点污染源个数
	 */
    private Integer keyAttrCount;

	/**
	 * 本部门一般污染源个数
	 */
    private Integer commonAttrCount;

	/**
	 * 本部门特殊污染源个数
	 */
    private Integer specialAttrCount;

	/**
	 * 本部门自定义污染源个数
	 */
    private Integer customAttrCount;

	/**
	 * 已下发双随机任务数
	 */
    private Integer issueRandomTaskCount;

	/**
	 * 已办结任务数
	 */
    private Integer completedRandomTaskCount;

	/**
	 * 已执法双随机对象数
	 */
    private Integer completedObjectCount;

	/**
	 * 重点污染源库已办结双随机任务数
	 */
    private Integer keyRandomTaskCount;

	/**
	 * 一般污染源库已办结双随机任务数
	 */
    private Integer commonRandomTaskCount;

	/**
	 * 特殊污染源库已办结双随机任务数
	 */
    private Integer specialRandomTaskCount;

	/**
	 * 自定义污染源库已办结双随机任务数
	 */
    private Integer customRandomTaskCount;

	/**
	 * 已下发双随机任务覆盖执法人员数
	 */
    private Integer issueLawUserCount;

	/**
	 * 已完成过双随机执法人员数
	 */
    private Integer completedLawUserCount;
    
    private String randomTaskCompletionRate;//下发任务办结率
    
    private String randomUserCoverRate;//人员覆盖率

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getLawDeptId() {
        return lawDeptId;
    }

    public void setLawDeptId(String lawDeptId) {
        this.lawDeptId = lawDeptId == null ? null : lawDeptId.trim();
    }

    public String getLawDeptName() {
        return lawDeptName;
    }

    public void setLawDeptName(String lawDeptName) {
        this.lawDeptName = lawDeptName == null ? null : lawDeptName.trim();
    }

    public String getBelongCode() {
        return belongCode;
    }

    public void setBelongCode(String belongCode) {
        this.belongCode = belongCode == null ? null : belongCode.trim();
    }

    public String getBelongName() {
        return belongName;
    }

    public void setBelongName(String belongName) {
        this.belongName = belongName == null ? null : belongName.trim();
    }

    public String getDepartmentNumber() {
        return departmentNumber;
    }

    public void setDepartmentNumber(String departmentNumber) {
        this.departmentNumber = departmentNumber == null ? null : departmentNumber.trim();
    }

    public Integer getDeptUserCount() {
        return deptUserCount;
    }

    public void setDeptUserCount(Integer deptUserCount) {
        this.deptUserCount = deptUserCount;
    }

    public Integer getDeptRandomUserCount() {
        return deptRandomUserCount;
    }

    public void setDeptRandomUserCount(Integer deptRandomUserCount) {
        this.deptRandomUserCount = deptRandomUserCount;
    }

    public Integer getDeptOnJobUserCount() {
        return deptOnJobUserCount;
    }

    public void setDeptOnJobUserCount(Integer deptOnJobUserCount) {
        this.deptOnJobUserCount = deptOnJobUserCount;
    }

    public Integer getRandomObjCount() {
        return randomObjCount;
    }

    public void setRandomObjCount(Integer randomObjCount) {
        this.randomObjCount = randomObjCount;
    }

    public Integer getKeyAttrCount() {
        return keyAttrCount;
    }

    public void setKeyAttrCount(Integer keyAttrCount) {
        this.keyAttrCount = keyAttrCount;
    }

    public Integer getCommonAttrCount() {
        return commonAttrCount;
    }

    public void setCommonAttrCount(Integer commonAttrCount) {
        this.commonAttrCount = commonAttrCount;
    }

    public Integer getSpecialAttrCount() {
        return specialAttrCount;
    }

    public void setSpecialAttrCount(Integer specialAttrCount) {
        this.specialAttrCount = specialAttrCount;
    }

    public Integer getCustomAttrCount() {
        return customAttrCount;
    }

    public void setCustomAttrCount(Integer customAttrCount) {
        this.customAttrCount = customAttrCount;
    }

    public Integer getIssueRandomTaskCount() {
        return issueRandomTaskCount;
    }

    public void setIssueRandomTaskCount(Integer issueRandomTaskCount) {
        this.issueRandomTaskCount = issueRandomTaskCount;
    }

    public Integer getCompletedRandomTaskCount() {
        return completedRandomTaskCount;
    }

    public void setCompletedRandomTaskCount(Integer completedRandomTaskCount) {
        this.completedRandomTaskCount = completedRandomTaskCount;
    }

    public Integer getCompletedObjectCount() {
        return completedObjectCount;
    }

    public void setCompletedObjectCount(Integer completedObjectCount) {
        this.completedObjectCount = completedObjectCount;
    }

    public Integer getKeyRandomTaskCount() {
        return keyRandomTaskCount;
    }

    public void setKeyRandomTaskCount(Integer keyRandomTaskCount) {
        this.keyRandomTaskCount = keyRandomTaskCount;
    }

    public Integer getCommonRandomTaskCount() {
        return commonRandomTaskCount;
    }

    public void setCommonRandomTaskCount(Integer commonRandomTaskCount) {
        this.commonRandomTaskCount = commonRandomTaskCount;
    }

    public Integer getSpecialRandomTaskCount() {
        return specialRandomTaskCount;
    }

    public void setSpecialRandomTaskCount(Integer specialRandomTaskCount) {
        this.specialRandomTaskCount = specialRandomTaskCount;
    }

    public Integer getCustomRandomTaskCount() {
        return customRandomTaskCount;
    }

    public void setCustomRandomTaskCount(Integer customRandomTaskCount) {
        this.customRandomTaskCount = customRandomTaskCount;
    }

    public Integer getIssueLawUserCount() {
        return issueLawUserCount;
    }

    public void setIssueLawUserCount(Integer issueLawUserCount) {
        this.issueLawUserCount = issueLawUserCount;
    }

    public Integer getCompletedLawUserCount() {
        return completedLawUserCount;
    }

    public void setCompletedLawUserCount(Integer completedLawUserCount) {
        this.completedLawUserCount = completedLawUserCount;
    }

	public String getRandomTaskCompletionRate() {
		return randomTaskCompletionRate;
	}

	public void setRandomTaskCompletionRate(String randomTaskCompletionRate) {
		this.randomTaskCompletionRate = randomTaskCompletionRate;
	}

	public String getRandomUserCoverRate() {
		return randomUserCoverRate;
	}

	public void setRandomUserCoverRate(String randomUserCoverRate) {
		this.randomUserCoverRate = randomUserCoverRate;
	}


    @Override
    public String toString() {
        return "JckhRandomStatistic{" +
                "id='" + id + '\'' +
                ", updateDate=" + updateDate +
                ", lawDeptId='" + lawDeptId + '\'' +
                ", lawDeptName='" + lawDeptName + '\'' +
                ", belongCode='" + belongCode + '\'' +
                ", belongName='" + belongName + '\'' +
                ", departmentNumber='" + departmentNumber + '\'' +
                ", deptUserCount=" + deptUserCount +
                ", deptRandomUserCount=" + deptRandomUserCount +
                ", deptOnJobUserCount=" + deptOnJobUserCount +
                ", randomObjCount=" + randomObjCount +
                ", keyAttrCount=" + keyAttrCount +
                ", commonAttrCount=" + commonAttrCount +
                ", specialAttrCount=" + specialAttrCount +
                ", customAttrCount=" + customAttrCount +
                ", issueRandomTaskCount=" + issueRandomTaskCount +
                ", completedRandomTaskCount=" + completedRandomTaskCount +
                ", completedObjectCount=" + completedObjectCount +
                ", keyRandomTaskCount=" + keyRandomTaskCount +
                ", commonRandomTaskCount=" + commonRandomTaskCount +
                ", specialRandomTaskCount=" + specialRandomTaskCount +
                ", customRandomTaskCount=" + customRandomTaskCount +
                ", issueLawUserCount=" + issueLawUserCount +
                ", completedLawUserCount=" + completedLawUserCount +
                ", randomTaskCompletionRate='" + randomTaskCompletionRate + '\'' +
                ", randomUserCoverRate='" + randomUserCoverRate + '\'' +
                '}';
    }
}