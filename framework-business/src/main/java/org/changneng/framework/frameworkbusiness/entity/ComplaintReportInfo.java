package org.changneng.framework.frameworkbusiness.entity;


import java.sql.Timestamp;

public class ComplaintReportInfo {

    private String ajid; // 编号
    private String tsrxm; //投诉人中文名称
    private String tsrdh;  // 投诉人手机号码
    private String tsdxmc;  // 投诉对象
    private String xzqy; //所属行政区域code
    private String fywtszd; //反应问题所在地
    private Timestamp tssj; //投诉时间
    private String tsnr; // 投诉内容
    private Timestamp xqblsj; //限期办理时间
    private String blyq; // 上级交办意见，派发意见
    private String blrymc;  // 执法人员姓名
    private String blrybm;  // 办理人员部门

    private String xfzflist;  // 信访下面的 执法编码

    private String xzqyname; //所属行政区域名称

    private String lawobjecttype; //企业类型

    public String getLawobjecttype() {
        return lawobjecttype;
    }

    public void setLawobjecttype(String lawobjecttype) {
        this.lawobjecttype = lawobjecttype;
    }

    public String getXzqyname() {
        return xzqyname;
    }

    public void setXzqyname(String xzqyname) {
        this.xzqyname = xzqyname;
    }

    public String getXfzflist() {
        return xfzflist;
    }

    public void setXfzflist(String xfzflist) {
        this.xfzflist = xfzflist;
    }

    public String getAjid() {
        return ajid;
    }

    public void setAjid(String ajid) {
        this.ajid = ajid;
    }

    public String getTsrxm() {
        return tsrxm;
    }

    public void setTsrxm(String tsrxm) {
        this.tsrxm = tsrxm;
    }

    public String getTsrdh() {
        return tsrdh;
    }

    public void setTsrdh(String tsrdh) {
        this.tsrdh = tsrdh;
    }

    public String getTsdxmc() {
        return tsdxmc;
    }

    public void setTsdxmc(String tsdxmc) {
        this.tsdxmc = tsdxmc;
    }

    public String getXzqy() {
        return xzqy;
    }

    public void setXzqy(String xzqy) {
        this.xzqy = xzqy;
    }

    public String getFywtszd() {
        return fywtszd;
    }

    public void setFywtszd(String fywtszd) {
        this.fywtszd = fywtszd;
    }

    public Timestamp getTssj() {
        return tssj;
    }

    public void setTssj(Timestamp tssj) {
        this.tssj = tssj;
    }

    public String getTsnr() {
        return tsnr;
    }

    public void setTsnr(String tsnr) {
        this.tsnr = tsnr;
    }

    public Timestamp getXqblsj() {
        return xqblsj;
    }

    public void setXqblsj(Timestamp xqblsj) {
        this.xqblsj = xqblsj;
    }

    public String getBlyq() {
        return blyq;
    }

    public void setBlyq(String blyq) {
        this.blyq = blyq;
    }

    public String getBlrymc() {
        return blrymc;
    }

    public void setBlrymc(String blrymc) {
        this.blrymc = blrymc;
    }

    public String getBlrybm() {
        return blrybm;
    }

    public void setBlrybm(String blrybm) {
        this.blrybm = blrybm;
    }

    private String xfajlist;

    public String getXfajlist() {
        return xfajlist;
    }

    public void setXfajlist(String xfajlist) {
        this.xfajlist = xfajlist;
    }
}