package org.changneng.framework.frameworkbusiness.dao.swingtag;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTag;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagManageSeach;

public interface SwingTagMapper {
    int deleteByPrimaryKey(String id);

    int insert(SwingTag record);

    int insertSelective(SwingTag record);

    SwingTag selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(SwingTag record);

    int updateByPrimaryKey(SwingTag record);

	List<SwingTag> swingtagmanageList(
			@Param("swingTagManageSeach")  SwingTagManageSeach swingTagManageSeach);

	List<SwingTag> checkSwingTagMark(@Param("swingTagMark")String swingTagMark);
}