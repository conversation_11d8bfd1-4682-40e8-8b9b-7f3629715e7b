package org.changneng.framework.frameworkbusiness.service.filecase;

import java.util.HashMap;
import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.JsonSimple;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseState;
import org.changneng.framework.frameworkbusiness.entity.filecase.GenerateBean;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.transaction.annotation.Transactional;

public interface CaseStateService {

	/**
	 * 根据案件ID返回该案件各个模块的状态
	 * @param caseState
	 * @return
	 */
	CaseState selectCaseState(CaseState caseState);
	
	/**
	 * 查询出环保部对应的违法行为集合
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	PageBean<TcDictionary> getIllegalBehaviorList(Integer pageNum,Integer pageSize);
	
	/**
	 * 阻断性校验：除基本信息保存完以外，并且有一个模块已经处于完成状态
	 * @param caseId
	 * @return
	 */
	boolean blockCheckCase(String caseId);
	
	/**
	 * 案卷办结
	 * @param caseId
	 * @return
	 * @throws Exception
	 */
	Integer caseEndSubmit(String caseId) throws Exception;
	
	
	/**
	 * 根据不同的表名，查询出 是否已经对接过 环保部 
	 * @param tables
	 * @return 0：从未对接过   1：已经对接过  -1:说明查询表没有该字段
	 */
	Integer getDockSusStateForTable(String tables,String id);
	
	Integer getDockSusStateForTable2(String tables,String id);
	
	/**
	 * 案件专项行动关联表:关联的 专项行动表ID 集合
	 * @param caseId
	 * @return
	 */
	List<String> getCaseSpecialIdList(String caseId);
	
	
	/**
	 * 违法类型案件中间关联表:关联的 违法类型案件ID 集合
	 * @param caseId
	 * @return
	 */
	List<String> getIllegalCaseTableIdList(String caseId);
 
	
	/**
	 * 维护违法类型名称 
	 * @param caseId
	 * @param illegalCaseIds
	 * @param illegaName
	 * @return
	 * @throws Exception
	 */
	JsonResult saveCaseSpecialTable(String caseId, String caseSpecialIds,String caseSpecialName) throws Exception;
	
	/**
	 * 维护违法类型名称 
	 * @param caseId
	 * @param illegalCaseIds
	 * @param illegaName
	 * @return
	 * @throws Exception
	 */
	JsonResult saveIllegalCaseTable(String caseId, String illegalCaseIds,String illegaName) throws Exception;
	
	/**
	 * 思路对接获取案件接口根据案件id
	 * @param caseId 案件id-》case_number
	 */
	JsonSimple getCaseById(HashMap<String, String> map);
	/**
	 * 通过大案件id查询该基本信息是否要显示删除按钮
	 * @param caseId
	 * @return
	 */
	int selectDeleteButtonIsDisplay(String caseId);
}
