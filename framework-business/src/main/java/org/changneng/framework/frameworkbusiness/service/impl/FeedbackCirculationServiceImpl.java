package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.FeedbackCirculationMapper;
import org.changneng.framework.frameworkbusiness.entity.FeedbackCirculation;
import org.changneng.framework.frameworkbusiness.service.FeedbackCirculationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
public class FeedbackCirculationServiceImpl implements FeedbackCirculationService {
	
	@Autowired
	private FeedbackCirculationMapper feedbackCirculationMapper;

	@Override
	public List<FeedbackCirculation> selectFeedbackCirculationByQuesId(String quesId) {
		// TODO Auto-generated method stub
		List<FeedbackCirculation> list = feedbackCirculationMapper.selectByQuesId(quesId);
		return list;
	}

	@Override
	public FeedbackCirculation selectFeedbackCirculationByQuesIdAndState(String quesId, String stateCode) {
		FeedbackCirculation feedbackCirculation = feedbackCirculationMapper.selectByQuesIdAndStateCode(quesId, stateCode);
		return feedbackCirculation;
	}

}
