package org.changneng.framework.frameworkbusiness.entity;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * 综合台账搜索类
 * <p>
 * Title:TaskGeneralSearch
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * @date 2017年4月8日-上午11:45:19
 */
public class TaskGeneralSearch {

	/**
	 * 执法对象名称
	 */
	private String lawObjectName;

	/**
	 * 任务进度状态（是否超时）
	 */
	private String nodeTimeoutState;

	/**
	 * 任务状态code
	 */
	private String taskStateCode;

	/**
	 * 当前环节
	 */
	private String nodeCode;

	/**
	 * 发起部门
	 */
	private String creatDepartmentId;

	/**
	 * 检查人
	 */
	private String checUserNames;

	/**
	 * 经办部门
	 */
	private String handlingUnitId;

	/**
	 * 经办人
	 */
	private String handleUserName;

	/**
	 * 专项行动
	 */
	private String specialActionIds;

	/**
	 * 经办单位和经办人的查询需要独立封装sql
	 */
	private String handlUserSql;

	/**
	 * 查询出主表信息进行关联
	 */
	private String mainJoinSql;

	/**
	 * 当前页数
	 */
	private Integer pageNum;

	/**
	 * 当前页显示多少条
	 */
	private Integer pageSize;

	// v1.1
	/**
	 * 任务来源
	 */
	private String taskFromCode;
	/**
	 * 年份
	 */
	private String belongYear;
	/**
	 * 季度
	 */
	private String quarter;
	/**
	 * 双随机属性
	 */
	private String databaseId;

	/**
	 * 是否双随机
	 */
	private String isAttr;

	/**
	 * 办理时间开始
	 */
	private String processDateBegin;

	/**
	 * 办理时间结束
	 */
	private String processDateEnd;

	/**
	 * 系统创建时间开始
	 */
	private String creatDateBegin;
	/**
	 * 系统创建时间结束
	 */
	private String creatDateEnd;
	/**
	 * 办结时间开始
	 */
	private String endDateBegin;

	/**
	 * 办结时间结束
	 */
	private String endDateEnd;

	/**
	 * 是否选择本机 0：不是 1：是
	 */
	private String isLevelCity;
	/**
	 * 市区划
	 */
	private String belongCity;
	/**
	 * 县区划
	 */
	private String belongCountry;
	/**
	 * 生产状态
	 */
	private String productStateCode;

	/**
	 * 任务到达时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String taskReachTime;

	/**
	 * 任务结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String processingTime;

	private String taskFromType;
	
	/**
	 * 执法编号
	 */
	private String taskNumber;
	 
	/**
	 * 检查开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String checkBeginData;
	
	/**
	 * 检查结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String checkEndData;

	private String lawID;
	
	/**
	 * 新增违法行为涉及污染源类别 模糊搜索
	 */
	private String taskLlegalCode;
	private String taskLlegalName;
	private String handlingUnitName;
	private String specialActionNames;
	private String dbdbh;


	private String specialSuperviseIndustry;//特殊监管行业

	 /**
	  * @Author:
	  * @Date: 2021/9/23 10:53
	  * @description：${新整行业类型}
	  */
	private String industryTypeCode;//行业类型code
	private String industryTypeName;//行业名称

	/**
	 * @Author:
	 * @Date: 2021/9/23 10:53
	 * @description：${新增现场执法时间}
	 */

	private String lawEnforcementTimeBegin;

	private String lawEnforcementTimeEnd;

	public String getLawEnforcementTimeBegin() {
		return lawEnforcementTimeBegin;
	}

	public void setLawEnforcementTimeBegin(String lawEnforcementTimeBegin) {
		this.lawEnforcementTimeBegin = lawEnforcementTimeBegin;
	}

	public String getLawEnforcementTimeEnd() {
		return lawEnforcementTimeEnd;
	}

	public void setLawEnforcementTimeEnd(String lawEnforcementTimeEnd) {
		this.lawEnforcementTimeEnd = lawEnforcementTimeEnd;
	}

	public String getIndustryTypeCode() {
		return industryTypeCode;
	}

	public void setIndustryTypeCode(String industryTypeCode) {
		this.industryTypeCode = industryTypeCode;
	}

	public String getIndustryTypeName() {
		return industryTypeName;
	}

	public void setIndustryTypeName(String industryTypeName) {
		this.industryTypeName = industryTypeName;
	}

	public String getSpecialSuperviseIndustry() {
		return specialSuperviseIndustry;
	}

	public void setSpecialSuperviseIndustry(String specialSuperviseIndustry) {
		this.specialSuperviseIndustry = specialSuperviseIndustry;
	}

	public String getDbdbh() {
		return dbdbh;
	}

	public void setDbdbh(String dbdbh) {
		this.dbdbh = dbdbh;
	}

	public String getSpecialActionNames() {
		return specialActionNames;
	}

	public void setSpecialActionNames(String specialActionNames) {
		this.specialActionNames = specialActionNames;
	}

	public String getHandlingUnitName() {
		return handlingUnitName;
	}

	public void setHandlingUnitName(String handlingUnitName) {
		this.handlingUnitName = handlingUnitName;
	}

	public String getTaskLlegalName() {
		return taskLlegalName;
	}

	public void setTaskLlegalName(String taskLlegalName) {
		this.taskLlegalName = taskLlegalName;
	}

	public String getLawID() {
		return lawID;
	}

	public void setLawID(String lawID) {
		this.lawID = lawID;
	}



	public String getTaskFromType() {
		return taskFromType;
	}

	public void setTaskFromType(String taskFromType) {
		this.taskFromType = taskFromType;
	}

	public String getLawObjectName() {
		return lawObjectName;
	}

	public void setLawObjectName(String lawObjectName) {
		this.lawObjectName = lawObjectName!=null?lawObjectName.trim():"";
	}

	public String getNodeTimeoutState() {
		return nodeTimeoutState;
	}

	public void setNodeTimeoutState(String nodeTimeoutState) {
		this.nodeTimeoutState = nodeTimeoutState;
	}

	public String getTaskStateCode() {
		return taskStateCode;
	}

	public void setTaskStateCode(String taskStateCode) {
		this.taskStateCode = taskStateCode;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public void setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
	}

	public String getCreatDepartmentId() {
		return creatDepartmentId;
	}

	public void setCreatDepartmentId(String creatDepartmentId) {
		this.creatDepartmentId = creatDepartmentId;
	}

	public String getChecUserNames() {
		return checUserNames;
	}

	public void setChecUserNames(String checUserNames) {
		this.checUserNames = checUserNames!=null?checUserNames.trim():"";
	}

	public String getHandlingUnitId() {
		return handlingUnitId;
	}

	public void setHandlingUnitId(String handlingUnitId) {
		this.handlingUnitId = handlingUnitId;
	}

	public String getHandleUserName() {
		return handleUserName;
	}

	public void setHandleUserName(String handleUserName) {
		this.handleUserName = handleUserName!=null?handleUserName.trim():"";
	}

	public String getSpecialActionIds() {
		return specialActionIds;
	}

	public void setSpecialActionIds(String specialActionIds) {
		this.specialActionIds = specialActionIds;
	}

	public String getHandlUserSql() {
		return handlUserSql;
	}

	public void setHandlUserSql(String handlUserSql) {
		this.handlUserSql = handlUserSql;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getMainJoinSql() {
		return mainJoinSql;
	}

	public void setMainJoinSql(String mainJoinSql) {
		this.mainJoinSql = mainJoinSql;
	}

	public String getTaskFromCode() {
		return taskFromCode;
	}

	public void setTaskFromCode(String taskFromCode) {
		this.taskFromCode = taskFromCode;
	}

	public String getBelongYear() {
		return belongYear;
	}

	public void setBelongYear(String belongYear) {
		this.belongYear = belongYear;
	}

	public String getQuarter() {
		return quarter;
	}

	public void setQuarter(String quarter) {
		this.quarter = quarter;
	}

	public String getDatabaseId() {
		return databaseId;
	}

	public void setDatabaseId(String databaseId) {
		this.databaseId = databaseId;
	}

	public String getIsLevelCity() {
		return isLevelCity;
	}

	public void setIsLevelCity(String isLevelCity) {
		this.isLevelCity = isLevelCity;
	}

	public String getBelongCity() {
		return belongCity;
	}

	public void setBelongCity(String belongCity) {
		this.belongCity = belongCity;
	}

	public String getBelongCountry() {
		return belongCountry;
	}

	public void setBelongCountry(String belongCountry) {
		this.belongCountry = belongCountry;
	}

	public String getTaskReachTime() {
		return taskReachTime;
	}

	public void setTaskReachTime(String taskReachTime) {
		this.taskReachTime = taskReachTime;
	}

	public String getProcessingTime() {
		return processingTime;
	}

	public void setProcessingTime(String processingTime) {
		this.processingTime = processingTime;
	}

	public String getProductStateCode() {
		return productStateCode;
	}

	public void setProductStateCode(String productStateCode) {
		this.productStateCode = productStateCode;
	}

	public String getIsAttr() {
		return isAttr;
	}

	public void setIsAttr(String isAttr) {
		this.isAttr = isAttr;
	}

	public String getProcessDateBegin() {
		return processDateBegin;
	}

	public void setProcessDateBegin(String processDateBegin) {
		this.processDateBegin = processDateBegin;
	}

	public String getProcessDateEnd() {
		return processDateEnd;
	}

	public void setProcessDateEnd(String processDateEnd) {
		this.processDateEnd = processDateEnd;
	}

	public String getEndDateBegin() {
		return endDateBegin;
	}

	public void setEndDateBegin(String endDateBegin) {
		this.endDateBegin = endDateBegin;
	}

	public String getEndDateEnd() {
		return endDateEnd;
	}

	public void setEndDateEnd(String endDateEnd) {
		this.endDateEnd = endDateEnd;
	}

	public String getTaskNumber() {
		return taskNumber;
	}

	public void setTaskNumber(String taskNumber) {
		this.taskNumber = taskNumber!=null?taskNumber.trim():"";
	}

	public String getCheckBeginData() {
		return checkBeginData;
	}

	public void setCheckBeginData(String checkBeginData) {
		this.checkBeginData = checkBeginData;
	}

	public String getCheckEndData() {
		return checkEndData;
	}

	public void setCheckEndData(String checkEndData) {
		this.checkEndData = checkEndData;
	}

	public String getTaskLlegalCode() {
		return taskLlegalCode;
	}

	public void setTaskLlegalCode(String taskLlegalCode) {
		this.taskLlegalCode = taskLlegalCode;
	}

	public String getCreatDateBegin() {
		return creatDateBegin;
	}

	public void setCreatDateBegin(String creatDateBegin) {
		this.creatDateBegin = creatDateBegin;
	}

	public String getCreatDateEnd() {
		return creatDateEnd;
	}

	public void setCreatDateEnd(String creatDateEnd) {
		this.creatDateEnd = creatDateEnd;
	}
	
}
