package org.changneng.framework.frameworkbusiness.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.Null;

import org.apache.poi.ss.formula.functions.Today;
import org.changneng.framework.frameworkbusiness.dao.AreaMapper;
import org.changneng.framework.frameworkbusiness.dao.CaseDispatchDao;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseDispatchBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.OneMapLawTableParams;
import org.changneng.framework.frameworkbusiness.service.CaseDispatchService;
import org.changneng.framework.frameworkbusiness.service.JckhStatisticService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("caseDispatchService")
public class CaseDispatchServiceImpl implements CaseDispatchService {

	@Autowired
	private AreaMapper areaMapper;
	
	@Autowired
	private CaseDispatchDao caseDispatchDao;

	@Autowired
	private JckhStatisticService jckhStatisticService;
	
	@Override
	public List<CaseDispatchBean> getCaseDispatchForAreaCode(CaseStatisSearchBean searchBean, SysUsers sysUsers,CaseSumTitleBean titleBean ) {
		// 返回的数据集合
		List<CaseDispatchBean> caseDispatchBeanList = new ArrayList<CaseDispatchBean>();
		
		// 如果没有市、县选中，则可能是第一次，或是省
		if (ChangnengUtil.isNull(searchBean.getBelongCity()) && ChangnengUtil.isNull(searchBean.getBelongCountry())) {
			String areaCode = sysUsers.getBelongAreaId();
			if (areaCode != null && areaCode.endsWith("000000")) {
				//省级，不做处理
			} else if (areaCode != null && areaCode.endsWith("0000")) {
				//市级
				searchBean.setBelongCity(areaCode);
			} else if (areaCode != null && areaCode.endsWith("00")) {
				searchBean.setBelongCity(areaCode.substring(0, 4)+"0000");
				searchBean.setBelongCountry(areaCode);
			}
		}
		// 省的用户默认选中
		if(ChangnengUtil.isNull(searchBean.getCitySum()) && sysUsers.getBelongAreaId().equals("35000000")){
			searchBean.setCitySum("1");
		}
		// 初始类型
		if(ChangnengUtil.isNull(searchBean.getDocType())){
			searchBean.setDocType(1);
		}
		// 封装提示语
		getCaseSumTitleBeanByDocType(searchBean.getDocType(), titleBean);
		// 初始化日期
		if(ChangnengUtil.isNull(searchBean.getSelectDate())){
			searchBean.setSelectDate("thisYear");
		}
		if(!"customDate".equals(searchBean.getSelectDate())){
			// 封装开始结束时间
			JckhSearchListBean searchListBean = new JckhSearchListBean(); // 这个类是引用暂时使用
			searchListBean.setSelectDate(searchBean.getSelectDate());
			calBeginEndDate(searchListBean);
			searchBean.setBeginDate(searchListBean.getBeginDate()!=null?searchListBean.getBeginDate():null);
			searchBean.setEndDate(searchListBean.getEndDate()!=null?searchListBean.getEndDate():null);
		}
		
		
		List<Area> areaList = null;
		if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
			areaList = areaMapper.selectAreaForAreaLevel();
		}else{
			// 经过上面处理后，如果存在市（区），则说明是  3（区、县）级
			if(!ChangnengUtil.isNull(searchBean.getBelongCity()) && !ChangnengUtil.isNull(searchBean.getBelongCountry())){  // 3（区、县）级
				Area area  = areaMapper.queryAreaByAreacode(searchBean.getBelongCountry());
				areaList = new ArrayList<Area>();
				areaList.add(area);
			}else if(!ChangnengUtil.isNull(searchBean.getBelongCity()) && ChangnengUtil.isNull(searchBean.getBelongCountry())){  // 2 市级
				areaList = areaMapper.selectAllCityByCode(searchBean.getBelongCity().substring(0, 4));
			}else{ // 省
				areaList = areaMapper.selectAllOrderByCode();
			}
		}
		// 能看到的区划
		if(!areaList.isEmpty() && areaList.size()>0){
			// 移除指定区划
			returnRemoveAreaListItem(areaList,searchBean.getCitySum());
			// DocType 1：环保部已对接案件总体情况 2：环保部已提交案件总体情况 3：环保部已办理案件总体情况
			// 行政处罚
			String  docTypeSql = "";
			String  dateRangSql = "";
			if(1==searchBean.getDocType()){
				docTypeSql = " and ls.dock_sus_state = 1 ";
			} 
			if(2==searchBean.getDocType()){
				docTypeSql = " and ls.up_state = 3 ";
			} 
			if(4==searchBean.getDocType()){
				docTypeSql = " and (ls.up_state != 3 or ls.up_state is null ) ";
			} 
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.decision_date >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.decision_date < to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			// 后续光谱增加需要，需要直接能进行市汇总，关联区划把没法汇总，则通过截取区划汇总。（违法数据库设计第一范式，建议优化）
			String atvGroupByAreaCode = " cbi.user_belong_area_code";
			String atvColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				atvGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				atvColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  atvSanctionCase = caseDispatchDao.getAtvSanctionCase(docTypeSql+dateRangSql,atvGroupByAreaCode,atvColumn);
			Map<String, CaseDispatchBean> atvMap = new HashMap<>();
			if(!atvSanctionCase.isEmpty() && atvSanctionCase.size()>0){
				for (int i = 0; i < atvSanctionCase.size(); i++) {
					atvMap.put(atvSanctionCase.get(i).getUserAreaCode(), atvSanctionCase.get(i));
				}
			}
			// 查封扣押
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.execution_start_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.execution_start_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss')  ";
			}
			String seqGroupByAreaCode = " cbi.user_belong_area_code";
			String seqColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				seqGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				seqColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean> sequestrationInfo =  caseDispatchDao.getSequestrationInfo(docTypeSql+dateRangSql,seqGroupByAreaCode,seqColumn);
			Map<String, CaseDispatchBean> seqMap = new HashMap<>();
			if(!sequestrationInfo.isEmpty() && sequestrationInfo.size()>0){
				for (int i = 0; i < sequestrationInfo.size(); i++) {
					seqMap.put(sequestrationInfo.get(i).getUserAreaCode(), sequestrationInfo.get(i));
				}
			}
			// 限制生产
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.execution_start_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.execution_start_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss')  ";
			}
			String limitGroupByAreaCode = " cbi.user_belong_area_code";
			String limitColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				limitGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				limitColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean> limitProduct =  caseDispatchDao.getLimitProduct(docTypeSql+dateRangSql,limitGroupByAreaCode,limitColumn);
			Map<String, CaseDispatchBean> limitMap = new HashMap<>();
			if(!limitProduct.isEmpty() && limitProduct.size()>0){
				for (int i = 0; i < limitProduct.size(); i++) {
					limitMap.put(limitProduct.get(i).getUserAreaCode(), limitProduct.get(i));
				}
			}
			// 停产整治
			String stopGroupByAreaCode = " cbi.user_belong_area_code ";
			String stopColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.decision_date >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.decision_date <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				stopGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				stopColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  stopProduct = caseDispatchDao.getStopProduct(docTypeSql+dateRangSql,stopGroupByAreaCode,stopColumn);
			Map<String, CaseDispatchBean> stopMap = new HashMap<>();
			if(!stopProduct.isEmpty() && stopProduct.size()>0){
				for (int i = 0; i < stopProduct.size(); i++) {
					stopMap.put(stopProduct.get(i).getUserAreaCode(), stopProduct.get(i));
				}
			}
			// 移送拘留
			String tivGroupByAreaCode = " cbi.user_belong_area_code ";
			String tivColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.transfer_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.transfer_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				tivGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				tivColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  administrativeDetention = caseDispatchDao.getAdministrativeDetention(docTypeSql+dateRangSql,tivGroupByAreaCode,tivColumn);
			Map<String, CaseDispatchBean> tivMap = new HashMap<>();
			if(!administrativeDetention.isEmpty() && administrativeDetention.size()>0){
				for (int i = 0; i < administrativeDetention.size(); i++) {
					tivMap.put(administrativeDetention.get(i).getUserAreaCode(), administrativeDetention.get(i));
				}
			}
			
			// 环境污染犯罪案件数
			if(2==searchBean.getDocType()){
				docTypeSql = " and FIRST_PART_STATE = 3 ";
			} 
			if(4==searchBean.getDocType()){
				docTypeSql = " and ( ls.FIRST_PART_STATE != 3  or ls.FIRST_PART_STATE is null )";
			} 
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.TRANSFER_DATE >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.TRANSFER_DATE <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			String polGroupByAreaCode = " cbi.user_belong_area_code ";
			String polColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				polGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				polColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  pollutionCrime = caseDispatchDao.getPollutionCrime(docTypeSql+dateRangSql,polGroupByAreaCode,polColumn);
			Map<String, CaseDispatchBean> polMap = new HashMap<>();
			if(!pollutionCrime.isEmpty() && pollutionCrime.size()>0){
				for (int i = 0; i < pollutionCrime.size(); i++) {
					polMap.put(pollutionCrime.get(i).getUserAreaCode(), pollutionCrime.get(i));
				}
			}

			// 按日计罚案件-案件数, 按日计罚案件-罚款金额
			if(2==searchBean.getDocType()){
				docTypeSql = " and FIRST_PART_STATE = 3 ";
			} 
			if(4==searchBean.getDocType()){
				docTypeSql = " and ( ls.FIRST_PART_STATE != 3  or ls.FIRST_PART_STATE is null )";
			} 
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.DECISION_ISSUE_DATE >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
				          "and ls.DECISION_ISSUE_DATE <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			String dayGroupByAreaCode = " cbi.user_belong_area_code ";
			String dayColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				dayGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				dayColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  penaltyDay = caseDispatchDao.getPenaltyDay(docTypeSql+dateRangSql,dayGroupByAreaCode,dayColumn);
			Map<String, CaseDispatchBean> dayMap = new HashMap<>();
			if(!penaltyDay.isEmpty() && penaltyDay.size()>0){
				for (int i = 0; i < penaltyDay.size(); i++) {
					dayMap.put(penaltyDay.get(i).getUserAreaCode(), penaltyDay.get(i));
				}
			}
			
			// 部门表  考核人员基数，从部门表中查询
			String depGroupByAreaCode = " cbi.belong_areaCode ";
			String depColumn = " cbi.belong_areaCode  ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				depGroupByAreaCode = " substr(cbi.belong_areaCode,1,4) ";
				depColumn = " substr(cbi.belong_areaCode,1,4)||'0000' ";
			}
			Map<String, CaseDispatchBean> depMap = new HashMap<>();
			List<CaseDispatchBean>  department =  caseDispatchDao.getSysDepartmentMonitorCount(depGroupByAreaCode, depColumn);
			if(!department.isEmpty() && department.size()>0){
				for (int i = 0; i < department.size(); i++) {
					depMap.put(department.get(i).getUserAreaCode(), department.get(i));
				}
			}
			
			
			
			// 总计，最后插入到集合的最后一行
			CaseDispatchBean sumDispatchBean  = new CaseDispatchBean(); 
			sumDispatchBean.setName("总计");
			sumDispatchBean.setCity("-");
			sumDispatchBean.setAtvCaseNumber(0);
			sumDispatchBean.setAtvCasePrice(BigDecimal.valueOf(0.0));
			sumDispatchBean.setSeqCaseNumber(0);
			sumDispatchBean.setLimitCaseNumber(0);
			sumDispatchBean.setStopCaseNumber(0);
			sumDispatchBean.setDetCaseNumber(0);
			sumDispatchBean.setPolCaseNumber(0);
			sumDispatchBean.setpDayCaseNumber(0);
			sumDispatchBean.setpDayCasePrice(BigDecimal.valueOf(0.0));
			sumDispatchBean.setAssCaseFinalNumber(0);
			sumDispatchBean.setAllCaseFinalNumber(0);
			sumDispatchBean.setAllCaseFinalPrice(BigDecimal.valueOf(0.0));
			sumDispatchBean.setpBaseNumber(0);
			sumDispatchBean.setAvgPriceNumber(BigDecimal.valueOf(0.0000)+"");
			sumDispatchBean.setAvgCaseNumber(BigDecimal.valueOf(0.0000)+"");
			for (int i = 0; i < areaList.size(); i++) {
				Area area  = areaList.get(i);
				CaseDispatchBean fianlDispatchBean  = new CaseDispatchBean(); 
				fianlDispatchBean.setCity(area.getCity());
				fianlDispatchBean.setName(area.getName());
				fianlDispatchBean.setUserAreaCode(area.getCode());
				//行政处罚	
				CaseDispatchBean atvBean = atvMap.get(area.getCode());
				if(atvBean!=null){  //atvCaseNumber,atvCasePrice
					fianlDispatchBean.setAtvCaseNumber(atvBean.getAtvCaseNumber()!=null?atvBean.getAtvCaseNumber():0);
					fianlDispatchBean.setAtvCasePrice(atvBean.getAtvCasePrice()!=null?atvBean.getAtvCasePrice():BigDecimal.valueOf(0.0));
				}else{
					fianlDispatchBean.setAtvCaseNumber(0);
					fianlDispatchBean.setAtvCasePrice(BigDecimal.valueOf(0.0));
				}
				// 总计统计
				sumDispatchBean.setAtvCaseNumber(fianlDispatchBean.getAtvCaseNumber()+sumDispatchBean.getAtvCaseNumber());
				sumDispatchBean.setAtvCasePrice(sumDispatchBean.getAtvCasePrice().add(fianlDispatchBean.getAtvCasePrice()));
				
				//查封扣押
				CaseDispatchBean seqBean = seqMap.get(area.getCode());
				if(seqBean!=null){
					fianlDispatchBean.setSeqCaseNumber(seqBean.getSeqCaseNumber()!=null?seqBean.getSeqCaseNumber():0);
				}else{
					fianlDispatchBean.setSeqCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setSeqCaseNumber(sumDispatchBean.getSeqCaseNumber()+fianlDispatchBean.getSeqCaseNumber());
				
				// 限制生产
				CaseDispatchBean limitBean = limitMap.get(area.getCode());
				if(limitBean!=null){
					fianlDispatchBean.setLimitCaseNumber(limitBean.getLimitCaseNumber()!=null?limitBean.getLimitCaseNumber():0);
				}else{
					fianlDispatchBean.setLimitCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setLimitCaseNumber(sumDispatchBean.getLimitCaseNumber()+fianlDispatchBean.getLimitCaseNumber());
				
				// 停产整治
				CaseDispatchBean stopBean = stopMap.get(area.getCode());
				if(stopBean!=null){
					fianlDispatchBean.setStopCaseNumber(stopBean.getStopCaseNumber()!=null?stopBean.getStopCaseNumber():0);
				}else{
					fianlDispatchBean.setStopCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setStopCaseNumber(sumDispatchBean.getStopCaseNumber()+fianlDispatchBean.getStopCaseNumber());
				
				// 移送拘留
				CaseDispatchBean tivBean = tivMap.get(area.getCode());
				if(tivBean!=null){
					fianlDispatchBean.setDetCaseNumber(tivBean.getDetCaseNumber()!=null?tivBean.getDetCaseNumber():0);
				}else{
					fianlDispatchBean.setDetCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setDetCaseNumber(sumDispatchBean.getDetCaseNumber()+fianlDispatchBean.getDetCaseNumber());
				
				// 环境污染犯罪案件数
				CaseDispatchBean polBean = polMap.get(area.getCode());
				if(polBean!=null){
					fianlDispatchBean.setPolCaseNumber(polBean.getPolCaseNumber()!=null?polBean.getPolCaseNumber():0);
				}else{
					fianlDispatchBean.setPolCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setPolCaseNumber(sumDispatchBean.getPolCaseNumber()+fianlDispatchBean.getPolCaseNumber());
				
				// 按日计罚案件-案件数, 按日计罚案件-罚款金额
				CaseDispatchBean dayBean = dayMap.get(area.getCode());
				if(dayBean!=null){ // pDayCaseNumber pDayCasePrice
					fianlDispatchBean.setpDayCaseNumber(dayBean.getpDayCaseNumber()!=null?dayBean.getpDayCaseNumber():0);
					fianlDispatchBean.setpDayCasePrice(dayBean.getpDayCasePrice()!=null?dayBean.getpDayCasePrice():BigDecimal.valueOf(0.0));
				}else{
					fianlDispatchBean.setpDayCaseNumber(0);
					fianlDispatchBean.setpDayCasePrice(BigDecimal.valueOf(0.0));
				}
				// 部门人员基数统计 
				CaseDispatchBean depBean = depMap.get(area.getCode());
				if(depBean!=null){
					fianlDispatchBean.setpBaseNumber(depBean.getpBaseNumber()!=null?depBean.getpBaseNumber():0);
				}else {
					fianlDispatchBean.setpBaseNumber(0);
				}
				
				
				// 总计统计
				sumDispatchBean.setpDayCaseNumber(sumDispatchBean.getpDayCaseNumber()+fianlDispatchBean.getpDayCaseNumber());
				sumDispatchBean.setpDayCasePrice(sumDispatchBean.getpDayCasePrice().add(fianlDispatchBean.getpDayCasePrice()));
				// 配套措施总案件数
				fianlDispatchBean.setAssCaseFinalNumber(fianlDispatchBean.getSeqCaseNumber()+fianlDispatchBean.getLimitCaseNumber()+fianlDispatchBean.getStopCaseNumber()
														+fianlDispatchBean.getPolCaseNumber()+fianlDispatchBean.getDetCaseNumber()+fianlDispatchBean.getpDayCaseNumber());
				// 总计统计
				sumDispatchBean.setAssCaseFinalNumber(sumDispatchBean.getAssCaseFinalNumber()+fianlDispatchBean.getAssCaseFinalNumber());
				// 所有案件总数量
				fianlDispatchBean.setAllCaseFinalNumber(fianlDispatchBean.getAtvCaseNumber()+fianlDispatchBean.getSeqCaseNumber()+fianlDispatchBean.getLimitCaseNumber()+fianlDispatchBean.getStopCaseNumber()
														+fianlDispatchBean.getPolCaseNumber()+fianlDispatchBean.getDetCaseNumber()+fianlDispatchBean.getpDayCaseNumber());
				// 总计统计
				sumDispatchBean.setAllCaseFinalNumber(sumDispatchBean.getAllCaseFinalNumber()+fianlDispatchBean.getAllCaseFinalNumber());
				// 一般行政处罚和 按日计罚 金额
				fianlDispatchBean.setAllCaseFinalPrice(fianlDispatchBean.getAtvCasePrice().add(fianlDispatchBean.getpDayCasePrice()));
				// 总计统计
				sumDispatchBean.setAllCaseFinalPrice(sumDispatchBean.getAllCaseFinalPrice().add(fianlDispatchBean.getAllCaseFinalPrice()));
				
				// 【人均办案数】，取【总案件数】/【考核人员基数】；
				// 【人均处罚金额（万元）】，取【总罚款金额（万元）】/【考核人员基数】；
				DecimalFormat df=new DecimalFormat("0.0000");
				fianlDispatchBean.setAvgCaseNumber(df.format((float)fianlDispatchBean.getAllCaseFinalNumber()/(fianlDispatchBean.getpBaseNumber()!=0?fianlDispatchBean.getpBaseNumber():1)));
				fianlDispatchBean.setAvgPriceNumber(fianlDispatchBean.getAllCaseFinalPrice().divide(new BigDecimal(fianlDispatchBean.getpBaseNumber()!=0?fianlDispatchBean.getpBaseNumber():1),4, RoundingMode.HALF_UP)+"");
			
				sumDispatchBean.setpBaseNumber(sumDispatchBean.getpBaseNumber()+fianlDispatchBean.getpBaseNumber());	
				sumDispatchBean.setAvgCaseNumber(df.format((float)sumDispatchBean.getAllCaseFinalNumber()/(sumDispatchBean.getpBaseNumber()!=0?sumDispatchBean.getpBaseNumber():1)));
				sumDispatchBean.setAvgPriceNumber(sumDispatchBean.getAllCaseFinalPrice().divide(new BigDecimal(sumDispatchBean.getpBaseNumber()!=0?sumDispatchBean.getpBaseNumber():1),4, RoundingMode.HALF_UP)+"");
				
				caseDispatchBeanList.add(fianlDispatchBean);
			}
			// 添加总计
			caseDispatchBeanList.add(sumDispatchBean);
		}
		
		
		//V1.5.9  如果最终搜索到时间范围是今天，那么需要展示到截止当前服务器的时间，则需要返回页面的时间格式重新封装
		try {
			Date todayTime = new Date();
			String today  = DateUtil.getdefaulSimpleFormate(todayTime);
			if(today.trim().equals(searchBean.getEndDate().trim())){ // 如果搜索的最终时间就是今天
				SimpleDateFormat sdf =new SimpleDateFormat("HH:mm");//只要时分
				searchBean.setHourStr(sdf.format(todayTime));	 
			}else{
				searchBean.setHourStr("24:00");
			}
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
		
		return caseDispatchBeanList;
	}

    @Override
    public List<OneMapLawTableParams> getOneMapLawTableData(OneMapLawTableParams params) {

	    return null;
    }


	/**
	 * 新--清水蓝天统计
	 * @param searchBean
	 * @param sysUsers
	 * @param titleBean
	 * @return
	 */
	@Override
	public List<WaterBlueSearch> getWaterBlueList(JckhSearchListBean searchBean, SysUsers sysUsers, CaseSumTitleBean titleBean) {

		// 返回的数据集合
		List<WaterBlueSearch> WaterBlueList = new ArrayList<WaterBlueSearch>();

		// 如果没有市、县选中，则可能是第一次，或是省
		if (ChangnengUtil.isNull(searchBean.getBelongCity()) && ChangnengUtil.isNull(searchBean.getBelongCountry())) {
			String areaCode = sysUsers.getBelongAreaId();
			if (areaCode != null && areaCode.endsWith("000000")) {
				//省级，不做处理
			} else if (areaCode != null && areaCode.endsWith("0000")) {
				//市级
				searchBean.setBelongCity(areaCode);
			} else if (areaCode != null && areaCode.endsWith("00")) {
				searchBean.setBelongCity(areaCode.substring(0, 4)+"0000");
				searchBean.setBelongCountry(areaCode);
			}
		}
		// 省的用户默认选中
		if(ChangnengUtil.isNull(searchBean.getCitySum()) && sysUsers.getBelongAreaId().equals("35000000")){
			searchBean.setCitySum("1");
		}
		// 封装提示语
		getCaseSumTitleBeanByDocType(3, titleBean);
		// 初始化日期
		if(ChangnengUtil.isNull(searchBean.getSelectDate())){
			searchBean.setSelectDate("thisYear");
		}
		if(!"customDate".equals(searchBean.getSelectDate())){
			// 封装开始结束时间
			JckhSearchListBean searchListBean = new JckhSearchListBean(); // 这个类是引用暂时使用
			searchListBean.setSelectDate(searchBean.getSelectDate());
			calBeginEndDate(searchListBean);
			searchBean.setBeginDate(searchListBean.getBeginDate()!=null?searchListBean.getBeginDate():null);
			searchBean.setEndDate(searchListBean.getEndDate()!=null?searchListBean.getEndDate():null);
		}


		List<Area> areaList = null;
		if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
			areaList = areaMapper.selectAreaForAreaLevel();
		}else{
			// 经过上面处理后，如果存在市（区），则说明是  3（区、县）级
			if(!ChangnengUtil.isNull(searchBean.getBelongCity()) && !ChangnengUtil.isNull(searchBean.getBelongCountry())){  // 3（区、县）级
				Area area  = areaMapper.queryAreaByAreacode(searchBean.getBelongCountry());
				areaList = new ArrayList<Area>();
				areaList.add(area);
			}else if(!ChangnengUtil.isNull(searchBean.getBelongCity()) && ChangnengUtil.isNull(searchBean.getBelongCountry())){  // 2 市级
				areaList = areaMapper.selectAllCityByCode(searchBean.getBelongCity().substring(0, 4));
			}else{ // 省
				areaList = areaMapper.selectAllOrderByCode();
			}
		}
		// 能看到的区划
		if(!areaList.isEmpty() && areaList.size()>0){
			// 移除指定区划
			returnRemoveAreaListItem(areaList,searchBean.getCitySum());
			// DocType 1：环保部已对接案件总体情况 2：环保部已提交案件总体情况 3：环保部已办理案件总体情况
			// 行政处罚
			String  docTypeSql = "";
			String  dateRangSql = "";
			
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.decision_date >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.decision_date < to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			// 后续光谱增加需要，需要直接能进行市汇总，关联区划把没法汇总，则通过截取区划汇总。（违法数据库设计第一范式，建议优化）
			String atvGroupByAreaCode = " cbi.user_belong_area_code";
			String atvColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				atvGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				atvColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  atvSanctionCase = caseDispatchDao.getAtvSanctionCase(docTypeSql+dateRangSql,atvGroupByAreaCode,atvColumn);
			Map<String, CaseDispatchBean> atvMap = new HashMap<>();
			if(!atvSanctionCase.isEmpty() && atvSanctionCase.size()>0){
				for (int i = 0; i < atvSanctionCase.size(); i++) {
					atvMap.put(atvSanctionCase.get(i).getUserAreaCode(), atvSanctionCase.get(i));
				}
			}
			// 查封扣押
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.execution_start_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.execution_start_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss')  ";
			}
			String seqGroupByAreaCode = " cbi.user_belong_area_code";
			String seqColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				seqGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				seqColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean> sequestrationInfo =  caseDispatchDao.getSequestrationInfo(docTypeSql+dateRangSql,seqGroupByAreaCode,seqColumn);
			Map<String, CaseDispatchBean> seqMap = new HashMap<>();
			if(!sequestrationInfo.isEmpty() && sequestrationInfo.size()>0){
				for (int i = 0; i < sequestrationInfo.size(); i++) {
					seqMap.put(sequestrationInfo.get(i).getUserAreaCode(), sequestrationInfo.get(i));
				}
			}
			// 限制生产
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.execution_start_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.execution_start_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss')  ";
			}
			String limitGroupByAreaCode = " cbi.user_belong_area_code";
			String limitColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				limitGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				limitColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean> limitProduct =  caseDispatchDao.getLimitProduct(docTypeSql+dateRangSql,limitGroupByAreaCode,limitColumn);
			Map<String, CaseDispatchBean> limitMap = new HashMap<>();
			if(!limitProduct.isEmpty() && limitProduct.size()>0){
				for (int i = 0; i < limitProduct.size(); i++) {
					limitMap.put(limitProduct.get(i).getUserAreaCode(), limitProduct.get(i));
				}
			}
			// 停产整治
			String stopGroupByAreaCode = " cbi.user_belong_area_code ";
			String stopColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.decision_date >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.decision_date <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				stopGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				stopColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  stopProduct = caseDispatchDao.getStopProduct(docTypeSql+dateRangSql,stopGroupByAreaCode,stopColumn);
			Map<String, CaseDispatchBean> stopMap = new HashMap<>();
			if(!stopProduct.isEmpty() && stopProduct.size()>0){
				for (int i = 0; i < stopProduct.size(); i++) {
					stopMap.put(stopProduct.get(i).getUserAreaCode(), stopProduct.get(i));
				}
			}
			// 移送拘留
			String tivGroupByAreaCode = " cbi.user_belong_area_code ";
			String tivColumn = " cbi.user_belong_area_code";
			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.transfer_time >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.transfer_time <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				tivGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				tivColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  administrativeDetention = caseDispatchDao.getAdministrativeDetention(docTypeSql+dateRangSql,tivGroupByAreaCode,tivColumn);
			Map<String, CaseDispatchBean> tivMap = new HashMap<>();
			if(!administrativeDetention.isEmpty() && administrativeDetention.size()>0){
				for (int i = 0; i < administrativeDetention.size(); i++) {
					tivMap.put(administrativeDetention.get(i).getUserAreaCode(), administrativeDetention.get(i));
				}
			}

			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.TRANSFER_DATE >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.TRANSFER_DATE <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			String polGroupByAreaCode = " cbi.user_belong_area_code ";
			String polColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				polGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				polColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  pollutionCrime = caseDispatchDao.getPollutionCrime(docTypeSql+dateRangSql,polGroupByAreaCode,polColumn);
			Map<String, CaseDispatchBean> polMap = new HashMap<>();
			if(!pollutionCrime.isEmpty() && pollutionCrime.size()>0){
				for (int i = 0; i < pollutionCrime.size(); i++) {
					polMap.put(pollutionCrime.get(i).getUserAreaCode(), pollutionCrime.get(i));
				}
			}

			if(!ChangnengUtil.isNull(searchBean.getBeginDate()) && !ChangnengUtil.isNull(searchBean.getEndDate())){ //查询开始时间和结束时间不能为空
				dateRangSql = " and ls.DECISION_ISSUE_DATE >= to_date('"+searchBean.getBeginDate()+"','yyyy-mm-dd') "+
						"and ls.DECISION_ISSUE_DATE <  to_date('"+searchBean.getEndDate()+" 23:59:59','yyyy-mm-dd hh24:mi:ss') ";
			}
			String dayGroupByAreaCode = " cbi.user_belong_area_code ";
			String dayColumn = " cbi.user_belong_area_code ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				dayGroupByAreaCode = " substr(cbi.user_belong_area_code,1,4) ";
				dayColumn = " substr(cbi.user_belong_area_code,1,4)||'0000' ";
			}
			List<CaseDispatchBean>  penaltyDay = caseDispatchDao.getPenaltyDay(docTypeSql+dateRangSql,dayGroupByAreaCode,dayColumn);
			Map<String, CaseDispatchBean> dayMap = new HashMap<>();
			if(!penaltyDay.isEmpty() && penaltyDay.size()>0){
				for (int i = 0; i < penaltyDay.size(); i++) {
					dayMap.put(penaltyDay.get(i).getUserAreaCode(), penaltyDay.get(i));
				}
			}

			// 部门表  考核人员基数，从部门表中查询
			String depGroupByAreaCode = " cbi.belong_areaCode ";
			String depColumn = " cbi.belong_areaCode  ";
			if(!ChangnengUtil.isNull(searchBean.getCitySum()) && "1".equals(searchBean.getCitySum())){ // 只看省、市的统计
				depGroupByAreaCode = " substr(cbi.belong_areaCode,1,4) ";
				depColumn = " substr(cbi.belong_areaCode,1,4)||'0000' ";
			}
			Map<String, CaseDispatchBean> depMap = new HashMap<>();
			List<CaseDispatchBean>  department =  caseDispatchDao.getSysDepartmentMonitorCount(depGroupByAreaCode, depColumn);
			if(!department.isEmpty() && department.size()>0){
				for (int i = 0; i < department.size(); i++) {
					depMap.put(department.get(i).getUserAreaCode(), department.get(i));
				}
			}

			// 总计，最后插入到集合的最后一行
			WaterBlueSearch sumDispatchBean  = new WaterBlueSearch();
			sumDispatchBean.setName("总计");
			sumDispatchBean.setCity("-");
			sumDispatchBean.setAtvCaseNumber(0);
			sumDispatchBean.setSeqCaseNumber(0);
			sumDispatchBean.setLimitCaseNumber(0);
			sumDispatchBean.setStopCaseNumber(0);
			sumDispatchBean.setDetCaseNumber(0);
			sumDispatchBean.setPolCaseNumber(0);
			sumDispatchBean.setpDayCaseNumber(0);

			sumDispatchBean.setAllCaseFinalNumber(0);

			for (int i = 0; i < areaList.size(); i++) {
				Area area  = areaList.get(i);
				WaterBlueSearch finalWaterBlueSearch  = new WaterBlueSearch();
				finalWaterBlueSearch.setCity(area.getCity());
				finalWaterBlueSearch.setName(area.getName());
				finalWaterBlueSearch.setUserAreaCode(area.getCode());
				//行政处罚
				CaseDispatchBean atvBean = atvMap.get(area.getCode());
				if(atvBean!=null){  //atvCaseNumber,atvCasePrice
					finalWaterBlueSearch.setAtvCaseNumber(atvBean.getAtvCaseNumber()!=null?atvBean.getAtvCaseNumber():0);
				}else{
					finalWaterBlueSearch.setAtvCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setAtvCaseNumber(finalWaterBlueSearch.getAtvCaseNumber()+sumDispatchBean.getAtvCaseNumber());

				//查封扣押
				CaseDispatchBean seqBean = seqMap.get(area.getCode());
				if(seqBean!=null){
					finalWaterBlueSearch.setSeqCaseNumber(seqBean.getSeqCaseNumber()!=null?seqBean.getSeqCaseNumber():0);
				}else{
					finalWaterBlueSearch.setSeqCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setSeqCaseNumber(sumDispatchBean.getSeqCaseNumber()+finalWaterBlueSearch.getSeqCaseNumber());

				// 限制生产
				CaseDispatchBean limitBean = limitMap.get(area.getCode());
				if(limitBean!=null){
					finalWaterBlueSearch.setLimitCaseNumber(limitBean.getLimitCaseNumber()!=null?limitBean.getLimitCaseNumber():0);
				}else{
					finalWaterBlueSearch.setLimitCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setLimitCaseNumber(sumDispatchBean.getLimitCaseNumber()+finalWaterBlueSearch.getLimitCaseNumber());

				// 停产整治
				CaseDispatchBean stopBean = stopMap.get(area.getCode());
				if(stopBean!=null){
					finalWaterBlueSearch.setStopCaseNumber(stopBean.getStopCaseNumber()!=null?stopBean.getStopCaseNumber():0);
				}else{
					finalWaterBlueSearch.setStopCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setStopCaseNumber(sumDispatchBean.getStopCaseNumber()+finalWaterBlueSearch.getStopCaseNumber());

				// 移送拘留
				CaseDispatchBean tivBean = tivMap.get(area.getCode());
				if(tivBean!=null){
					finalWaterBlueSearch.setDetCaseNumber(tivBean.getDetCaseNumber()!=null?tivBean.getDetCaseNumber():0);
				}else{
					finalWaterBlueSearch.setDetCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setDetCaseNumber(sumDispatchBean.getDetCaseNumber()+finalWaterBlueSearch.getDetCaseNumber());

				// 环境污染犯罪案件数
				CaseDispatchBean polBean = polMap.get(area.getCode());
				if(polBean!=null){
					finalWaterBlueSearch.setPolCaseNumber(polBean.getPolCaseNumber()!=null?polBean.getPolCaseNumber():0);
				}else{
					finalWaterBlueSearch.setPolCaseNumber(0);
				}
				// 总计统计
				sumDispatchBean.setPolCaseNumber(sumDispatchBean.getPolCaseNumber()+finalWaterBlueSearch.getPolCaseNumber());

				// 按日计罚案件-案件数, 按日计罚案件-罚款金额
				CaseDispatchBean dayBean = dayMap.get(area.getCode());
				if(dayBean!=null){ // pDayCaseNumber pDayCasePrice
					finalWaterBlueSearch.setpDayCaseNumber(dayBean.getpDayCaseNumber()!=null?dayBean.getpDayCaseNumber():0);
				}else{
					finalWaterBlueSearch.setpDayCaseNumber(0);
				}
				// 部门人员基数统计
				CaseDispatchBean depBean = depMap.get(area.getCode());


				// 总计统计
				sumDispatchBean.setpDayCaseNumber(sumDispatchBean.getpDayCaseNumber()+finalWaterBlueSearch.getpDayCaseNumber());

				// 所有案件总数量
				finalWaterBlueSearch.setAllCaseFinalNumber(finalWaterBlueSearch.getSeqCaseNumber()+finalWaterBlueSearch.getLimitCaseNumber()
						+finalWaterBlueSearch.getStopCaseNumber() + finalWaterBlueSearch.getPolCaseNumber()+finalWaterBlueSearch.getDetCaseNumber()
						+finalWaterBlueSearch.getpDayCaseNumber());
				// 总计统计
				sumDispatchBean.setAllCaseFinalNumber(sumDispatchBean.getAllCaseFinalNumber()+finalWaterBlueSearch.getAllCaseFinalNumber());
				
				WaterBlueList.add(finalWaterBlueSearch);
			}
			// 添加总计
			WaterBlueList.add(sumDispatchBean);
		}


		//V1.5.9  如果最终搜索到时间范围是今天，那么需要展示到截止当前服务器的时间，则需要返回页面的时间格式重新封装
		try {
			Date todayTime = new Date();
			String today  = DateUtil.getdefaulSimpleFormate(todayTime);
			if(today.trim().equals(searchBean.getEndDate().trim())){ // 如果搜索的最终时间就是今天
				SimpleDateFormat sdf =new SimpleDateFormat("HH:mm");//只要时分
				searchBean.setHourStr(sdf.format(todayTime));
			}else{
				searchBean.setHourStr("24:00");
			}
		} catch (ParseException e1) {
			e1.printStackTrace();
		}

		return WaterBlueList;
	}

	/**
	 * 根据不同的类型查看不同的提示语
	 * @param docType
	 * @param bean
	 * @return
	 * <AUTHOR>
	 * @date 2018年3月23日-下午2:31:42
	 */
	private CaseSumTitleBean getCaseSumTitleBeanByDocType(Integer docType,CaseSumTitleBean bean){
		if(docType==1){
			/**
			 * 行政处罚案件-案件数 <br/> 提示语
			 */
			bean.setAtvCaseNumberTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的行政处罚案件数（包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 行政处罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setAtvCasePriceTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接部的行政处罚案件处罚金额包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 查封扣押案件数<br/> 提示语
			 */
			bean.setSeqCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的查封扣押案件数");
			
			/**
			 * 限制生产案件数<br/> 提示语
			 */
			bean.setLimitCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的限制生产案件数");
			
			/**
			 * 停产整治案件数<br/> 提示语
			 */
			bean.setStopCaseNumberTitle("【决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对的接停产整治案件数");
			
			/**
			 * 移送拘留案件数<br/> 提示语
			 */
			bean.setDetCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的移送拘留案件数");
			
			/**
			 * 环境污染犯罪案件数<br/> 提示语
			 */
			bean.setPolCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的环境污染犯罪案件数");
			
			/**
			 * 按日计罚案件-案件数<br/> 提示语
			 */
			bean.setpDayCaseNumberTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的按日计罚案件数");
			
			/**
			 * 按日计罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setpDayCasePriceTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已成功对接的按日计罚罚款金额");
			
		}else if(docType==2){
			/**
			 * 行政处罚案件-案件数 <br/> 提示语
			 */
			bean.setAtvCaseNumberTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的行政处罚案件数（包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 行政处罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setAtvCasePriceTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的行政处罚案件处罚金额包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 查封扣押案件数<br/> 提示语
			 */
			bean.setSeqCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的查封扣押案件数");
			
			/**
			 * 限制生产案件数<br/> 提示语
			 */
			bean.setLimitCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的限制生产案件数");
			
			/**
			 * 停产整治案件数<br/> 提示语
			 */
			bean.setStopCaseNumberTitle("【决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的停产整治案件数");
			
			/**
			 * 移送拘留案件数<br/> 提示语
			 */
			bean.setDetCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的移送拘留案件数");
			
			/**
			 * 环境污染犯罪案件数<br/> 提示语
			 */
			bean.setPolCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的环境污染犯罪案件数");
			
			/**
			 * 按日计罚案件-案件数<br/> 提示语
			 */
			bean.setpDayCaseNumberTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的按日计罚案件数");
			
			/**
			 * 按日计罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setpDayCasePriceTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已提交的按日计罚罚款金额");
		}else if(docType==3){
			/**
			 * 行政处罚案件-案件数 <br/> 提示语
			 */
			bean.setAtvCaseNumberTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的行政处罚案件数（包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 行政处罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setAtvCasePriceTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的行政处罚案件处罚金额包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 查封扣押案件数<br/> 提示语
			 */
			bean.setSeqCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的查封扣押案件数");
			
			/**
			 * 限制生产案件数<br/> 提示语
			 */
			bean.setLimitCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的限制生产案件数");
			
			/**
			 * 停产整治案件数<br/> 提示语
			 */
			bean.setStopCaseNumberTitle("【决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的停产整治案件数");
			
			/**
			 * 移送拘留案件数<br/> 提示语
			 */
			bean.setDetCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的移送拘留案件数");
			
			/**
			 * 环境污染犯罪案件数<br/> 提示语
			 */
			bean.setPolCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理的环境污染犯罪案件数");
			
			/**
			 * 按日计罚案件-案件数<br/> 提示语
			 */
			bean.setpDayCaseNumberTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理按日计罚案件数");
			
			/**
			 * 按日计罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setpDayCasePriceTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理按日计罚罚款金额");
		}else{
			/**
			 * 行政处罚案件-案件数 <br/> 提示语
			 */
			bean.setAtvCaseNumberTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的行政处罚案件数（包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 行政处罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setAtvCasePriceTitle("【处罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的行政处罚案件处罚金额包括简易程序和一般程序，但不包括按日计罚）");
			
			/**
			 * 查封扣押案件数<br/> 提示语
			 */
			bean.setSeqCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的查封扣押案件数");
			
			/**
			 * 限制生产案件数<br/> 提示语
			 */
			bean.setLimitCaseNumberTitle("【实施开始时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的限制生产案件数");
			
			/**
			 * 停产整治案件数<br/> 提示语
			 */
			bean.setStopCaseNumberTitle("【决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的停产整治案件数");
			
			/**
			 * 移送拘留案件数<br/> 提示语
			 */
			bean.setDetCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的移送拘留案件数");
			
			/**
			 * 环境污染犯罪案件数<br/> 提示语
			 */
			bean.setPolCaseNumberTitle("【移送时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的环境污染犯罪案件数");
			
			/**
			 * 按日计罚案件-案件数<br/> 提示语
			 */
			bean.setpDayCaseNumberTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的按日计罚案件数");
			
			/**
			 * 按日计罚案件-罚款金额（万元）<br/> 提示语
			 */
			bean.setpDayCasePriceTitle("【按日计罚决定下达时间】在该时间段内，该行政区环保厅（局）作为处罚主体的，已办理未提交的按日计罚罚款金额");
		}
		/**
		 * 配套措施总案件数<br/> 提示语
		 */
		bean.setAssCaseFinalNumberTitle("【查封扣押案件数】+【限制生产案件数】+【停产整治案件数】+【移送拘留案件数】+【环境污染犯罪案件数】+【按日计罚案件数】");
		
		/**
		 * 总案件数<br/> 提示语
		 */
		bean.setAllCaseFinalNumberTitle("【配套措施总案件数】+【行政处罚案件数】");
		
		/**
		 * 总罚款金额（万元）<br/> 提示语
		 */
		bean.setAllCaseFinalPriceTitle("【行政处罚罚款金额】+【按日计罚罚款金额】");
		
		/**
		 * 考核人员基数 <br/> 提示语
		 */
		bean.setpBaseNumberTitle("考核人员基数");
		
		/**
		 * 人均罚款金额数 <br/> 提示语
		 */
		bean.setAvgPriceNumberTitle("【人均处罚金额（万元）】，【总罚款金额（万元）】/【考核人员基数】");
		
		/**
		 * 人均案件办理数 <br/> 提示语
		 */
		bean.setAvgCaseNumberTitle("【人均办案数】，【总案件数】/【考核人员基数】");
		
		
		return bean;
	}
	
	/**
	 * V1.5.6 赵光普 需求：要把用户不需要看到的几个区划，在 监察考核-案件调度、环保部案件调度  统计中移除<br>
	 * 经济技术开发区(福州市)  35019000 <br>
	 * 高新技术产业开发区(漳州市) 35069300 <br>
	 * 古雷港经济开发区(漳州市)  35069400 <br>
 	 * 招商局经济技术开发区(漳州市)  35069800 <br>aa
	 * 金门县(泉州市) 35052700 <br>
	 * 经开区(高新区)(龙岩市) 35089800 <br>
	 * 东侨开发区(宁德市) 35099800 <br>
	 * 平潭综合实验区(平潭综合实验区) 备注 在2018-05-02 赵光普把平潭综合实验区 变更为 设区市汇总不能去除，详细中可以去除。 35810000 <br>
	 * @param list 区划集合
	 * @return 去除后的区划集合    
	 * <AUTHOR>
	 * @date 2018年4月25日-下午4:59:11  
	 */
	public List<Area> returnRemoveAreaListItem(List<Area> list,String citySum){
		if(!list.isEmpty() && list.size()>0){
			Iterator<Area> iter = list.iterator();
			while (iter.hasNext()) {
				Area ar = iter.next();
				if (ar.getCode().equals("35019000")
//						|| ar.getCode().equals("35069300")
						|| ar.getCode().equals("35069800")
						|| ar.getCode().equals("35059800")
						|| ar.getCode().equals("35052700") || ar.getCode().equals("35089800")
						|| ar.getCode().equals("35099800")) {
					iter.remove();
				}
				if (ChangnengUtil.isNull(citySum) || !"1".equals(citySum)) {
					if (ar.getCode().equals("35810000")) {
						iter.remove();
					}
				}
			}
		}
		return list;
	}
	
	/**
	 * 独立私有的环保部案件调度时间统计
	 * @param searchListBean
	 * @return
	 * <AUTHOR>
	 * @date 2018年7月4日-上午10:03:19
	 */
	public JckhSearchListBean calBeginEndDate(JckhSearchListBean searchListBean) {
		String datePattern = "yyyy-MM-dd";
		String selectDate = searchListBean.getSelectDate();
		if (selectDate != null && !"".equals(selectDate)) {
			switch (selectDate) {
			case "thisYear":
				searchListBean.setBeginDate(DateUtil.year()+"-01-01");
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "thisMonth":
				searchListBean.setBeginDate(DateUtil.getThisMonthBeginAndYest().get(0));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "lastWeek":
				// 最近7天
				searchListBean.setBeginDate(DateUtil.getPastDate(7, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "lastTwoWeek":
				// 最近2周
				searchListBean.setBeginDate(DateUtil.getPastDate(14, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "lastMonth":
				// 最近30天
				searchListBean.setBeginDate(DateUtil.getPastDate(30, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "beforeMonth":
				// 上个月
				String str = DateUtil.getLastMonthBeginDay(-1, datePattern);
				str = str.substring(0,7);
				Map<String, String> map = DateUtil.getStartAndEndDateHaveHourMinutsAndSecondByYearAndMonth(str);
				searchListBean.setBeginDate(map.get("start").substring(0,10));
				searchListBean.setEndDate(map.get("end").substring(0,10));
				break;
			case "lastThreeMonth":
				// 最近3个月
				searchListBean.setBeginDate(DateUtil.getLastMonthBeginDay(-2, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "lastYear":
				// 最近12个月
				searchListBean.setBeginDate(DateUtil.getLastMonthBeginDay(-11, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(0, datePattern));
				break;
			case "yesterday":
				// 昨天
				searchListBean.setBeginDate(DateUtil.getPastDate(1, datePattern));
				searchListBean.setEndDate(DateUtil.getPastDate(1, datePattern));
				break;
			case "today":
				// 今天
				searchListBean.setBeginDate(DateUtil.format(new Date()));
				searchListBean.setEndDate(DateUtil.format(new Date()));
				break;
			default:
				break;
			}
		}
		return searchListBean;
	}
	
	public static void main(String[] args) {
		
		int a = 52;
		int b = 3;
		DecimalFormat df=new DecimalFormat("0.00");
		System.out.println(df.format((float)b/a));
		System.out.println(b/a);
		
		BigDecimal a2;
		BigDecimal b2;
		a2 = new BigDecimal(3);
		b2 = new BigDecimal(81);
		System.out.print(a2.divide(b2, 2, RoundingMode.HALF_UP));
	}
	
}
