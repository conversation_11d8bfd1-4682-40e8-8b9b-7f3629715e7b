package org.changneng.framework.frameworkbusiness.entity.filecase;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

public class AdministrativeDetention {
    private String id;

    private String caseId;

    private String caseNumber;

    private String transferDept;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date transferTime;

    private String illegalCaseType;
    
    private String littleCaseTypeName;

    private Integer adaptSituation;

    private String transferNumber;

    private String acceptDept;

    private Integer isAccept;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date acceptTime;

    private String acceptNumber;

    private Integer detentionNumber;

    private String informant;

    private String informantPhone;

    private String informantDept;

    private String upState;

    private String downState;

    private Date createTime;

    private Date lastedUpdateTime;

    private String informantName;

    private String informantBelongArea;

    private String informantBelongAreaName;

    private Integer isDel;

    private String adaptSituationName;

    private Date generateTimeUp;

    private Date generateTimeDown;

    private Integer submitStatusUp;

    private Integer submitStatusDown;

    private String caseContentNameDown;

    private String caseContentNameUp;

    private String recordsUrlUp;

    private String recordsUrlDown;

    private Date submitTime;

    private String case1;

    private String case2;

    private String case3;
    
    private String saveType; // 保存 1 上 2 下
    
    private String isTmp;// 暂存 1 上2 下
	
	private Integer synchroStatus;
	
	private Integer dockingState;   // 对接状态：0：默认（无需对接）  1：需要对接  （需要对接只监听提交） 2：等待中（客户端提交成功）  3：对接成功   （环保部返回结果）
	private Integer dockSusState;   //是否已经对接过： 0：从未对接过   1：已经对接过（基于docking_state = 3）  NUMBER(5) （基本信息）
	private Date successDate;  //成功回调时间
	private Date sendDockDate; //最后发起对接时间
	private Integer dockingType;  //对接类型: 0 新增 1 删除
	private Integer infoStateUp;  //（上-数据）数据是否需要对接：0 : 默认的（已经对接过后没再保存） 1：已经对接过又保存  2：等待中
	private Integer infoStateDown;//（下-数据）数据是否需要对接：0 : 默认的（已经对接过后没再保存） 1：已经对接过又保存  2：等待中
	private Integer fileDockStateUp;//（上-附件）附件是否需要对接： 0：默认（无修改，无需对接） 1：已修改 （新增）  2：等待中
	private Integer fileDockStateDown;//（下-附件）附件是否需要对接： 0：默认（无修改，无需对接） 1：已修改（新增）   2：等待中
	
	private String adAterialList;
	
	private Integer lastDockState;

	private Integer stageIsComplete;
	
	private Integer allIsComplete;
	
	private Integer isHaveDecisionNumber;//是否已有决定书文号
	
	private String onlineDecisionNumber;//在线决定书文号
	
	private String onlineDecisionCode;
	
	private Date firstSubmitTime; // 第一个提交时间
	
	private Date firstDockingDate; // 第一次对接环保部成功时间
	
	private Integer onlineDecisionYear;//在线决定书文年份
	
	
	
	public Integer getOnlineDecisionYear() {
		return onlineDecisionYear;
	}

	public void setOnlineDecisionYear(Integer onlineDecisionYear) {
		this.onlineDecisionYear = onlineDecisionYear;
	}

	public Integer getIsHaveDecisionNumber() {
		return isHaveDecisionNumber;
	}

	public void setIsHaveDecisionNumber(Integer isHaveDecisionNumber) {
		this.isHaveDecisionNumber = isHaveDecisionNumber;
	}

	public String getOnlineDecisionNumber() {
		return onlineDecisionNumber;
	}

	public void setOnlineDecisionNumber(String onlineDecisionNumber) {
		this.onlineDecisionNumber = onlineDecisionNumber;
	}
	
	public Integer getSynchroStatus() {
		return synchroStatus;
	}

	public void setSynchroStatus(Integer synchroStatus) {
		this.synchroStatus = synchroStatus;
	}
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId == null ? null : caseId.trim();
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber == null ? null : caseNumber.trim();
    }

    public String getTransferDept() {
        return transferDept;
    }

    public void setTransferDept(String transferDept) {
        this.transferDept = transferDept == null ? null : transferDept.trim();
    }

    public String getIllegalCaseType() {
        return illegalCaseType;
    }

    public void setIllegalCaseType(String illegalCaseType) {
        this.illegalCaseType = illegalCaseType == null ? null : illegalCaseType.trim();
    }

    public Integer getAdaptSituation() {
        return adaptSituation;
    }

    public void setAdaptSituation(Integer adaptSituation) {
        this.adaptSituation = adaptSituation;
    }

    public String getTransferNumber() {
        return transferNumber;
    }

    public void setTransferNumber(String transferNumber) {
        this.transferNumber = transferNumber == null ? null : transferNumber.trim();
    }

    public String getAcceptDept() {
        return acceptDept;
    }

    public void setAcceptDept(String acceptDept) {
        this.acceptDept = acceptDept == null ? null : acceptDept.trim();
    }

    public Integer getIsAccept() {
        return isAccept;
    }

    public void setIsAccept(Integer isAccept) {
        this.isAccept = isAccept;
    }

    public String getAcceptNumber() {
        return acceptNumber;
    }

    public void setAcceptNumber(String acceptNumber) {
        this.acceptNumber = acceptNumber == null ? null : acceptNumber.trim();
    }

    public Integer getDetentionNumber() {
        return detentionNumber;
    }

    public void setDetentionNumber(Integer detentionNumber) {
        this.detentionNumber = detentionNumber;
    }

    public String getInformant() {
        return informant;
    }

    public void setInformant(String informant) {
        this.informant = informant == null ? null : informant.trim();
    }

    public String getInformantPhone() {
        return informantPhone;
    }

    public void setInformantPhone(String informantPhone) {
        this.informantPhone = informantPhone == null ? null : informantPhone.trim();
    }

    public String getInformantDept() {
        return informantDept;
    }

    public void setInformantDept(String informantDept) {
        this.informantDept = informantDept == null ? null : informantDept.trim();
    }

    public String getUpState() {
        return upState;
    }

    public void setUpState(String upState) {
        this.upState = upState == null ? null : upState.trim();
    }

    public String getDownState() {
        return downState;
    }

    public void setDownState(String downState) {
        this.downState = downState == null ? null : downState.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastedUpdateTime() {
        return lastedUpdateTime;
    }

    public void setLastedUpdateTime(Date lastedUpdateTime) {
        this.lastedUpdateTime = lastedUpdateTime;
    }

    public String getInformantName() {
        return informantName;
    }

    public void setInformantName(String informantName) {
        this.informantName = informantName == null ? null : informantName.trim();
    }

    public String getInformantBelongArea() {
        return informantBelongArea;
    }

    public void setInformantBelongArea(String informantBelongArea) {
        this.informantBelongArea = informantBelongArea == null ? null : informantBelongArea.trim();
    }

    public String getInformantBelongAreaName() {
        return informantBelongAreaName;
    }

    public void setInformantBelongAreaName(String informantBelongAreaName) {
        this.informantBelongAreaName = informantBelongAreaName == null ? null : informantBelongAreaName.trim();
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public String getAdaptSituationName() {
        return adaptSituationName;
    }

    public void setAdaptSituationName(String adaptSituationName) {
        this.adaptSituationName = adaptSituationName == null ? null : adaptSituationName.trim();
    }

    public Date getGenerateTimeUp() {
        return generateTimeUp;
    }

    public void setGenerateTimeUp(Date generateTimeUp) {
        this.generateTimeUp = generateTimeUp;
    }

    public Date getGenerateTimeDown() {
        return generateTimeDown;
    }

    public void setGenerateTimeDown(Date generateTimeDown) {
        this.generateTimeDown = generateTimeDown;
    }

    public Integer getSubmitStatusUp() {
        return submitStatusUp;
    }

    public void setSubmitStatusUp(Integer submitStatusUp) {
        this.submitStatusUp = submitStatusUp;
    }

    public Integer getSubmitStatusDown() {
        return submitStatusDown;
    }

    public void setSubmitStatusDown(Integer submitStatusDown) {
        this.submitStatusDown = submitStatusDown;
    }

    public String getCaseContentNameDown() {
        return caseContentNameDown;
    }

    public void setCaseContentNameDown(String caseContentNameDown) {
        this.caseContentNameDown = caseContentNameDown == null ? null : caseContentNameDown.trim();
    }

    public String getCaseContentNameUp() {
        return caseContentNameUp;
    }

    public void setCaseContentNameUp(String caseContentNameUp) {
        this.caseContentNameUp = caseContentNameUp == null ? null : caseContentNameUp.trim();
    }

    public String getRecordsUrlUp() {
        return recordsUrlUp;
    }

    public void setRecordsUrlUp(String recordsUrlUp) {
        this.recordsUrlUp = recordsUrlUp == null ? null : recordsUrlUp.trim();
    }

    public String getRecordsUrlDown() {
        return recordsUrlDown;
    }

    public void setRecordsUrlDown(String recordsUrlDown) {
        this.recordsUrlDown = recordsUrlDown == null ? null : recordsUrlDown.trim();
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getCase1() {
        return case1;
    }

    public void setCase1(String case1) {
        this.case1 = case1 == null ? null : case1.trim();
    }

    public String getCase2() {
        return case2;
    }

    public void setCase2(String case2) {
        this.case2 = case2 == null ? null : case2.trim();
    }

    public String getCase3() {
        return case3;
    }

    public void setCase3(String case3) {
        this.case3 = case3 == null ? null : case3.trim();
    }

	public Date getTransferTime() {
		return transferTime;
	}

	public void setTransferTime(Date transferTime) {
		this.transferTime = transferTime;
	}

	public Date getAcceptTime() {
		return acceptTime;
	}

	public void setAcceptTime(Date acceptTime) {
		this.acceptTime = acceptTime;
	}

	public String getLittleCaseTypeName() {
		return littleCaseTypeName;
	}

	public void setLittleCaseTypeName(String littleCaseTypeName) {
		this.littleCaseTypeName = littleCaseTypeName;
	}

	public String getSaveType() {
		return saveType;
	}

	public void setSaveType(String saveType) {
		this.saveType = saveType;
	}

	public Integer getDockingState() {
		return dockingState;
	}

	public void setDockingState(Integer dockingState) {
		this.dockingState = dockingState;
	}

	public Integer getDockSusState() {
		return dockSusState;
	}

	public void setDockSusState(Integer dockSusState) {
		this.dockSusState = dockSusState;
	}

	public Date getSuccessDate() {
		return successDate;
	}

	public void setSuccessDate(Date successDate) {
		this.successDate = successDate;
	}

	public Date getSendDockDate() {
		return sendDockDate;
	}

	public void setSendDockDate(Date sendDockDate) {
		this.sendDockDate = sendDockDate;
	}

	public Integer getDockingType() {
		return dockingType;
	}

	public void setDockingType(Integer dockingType) {
		this.dockingType = dockingType;
	}

	public Integer getInfoStateUp() {
		return infoStateUp;
	}

	public void setInfoStateUp(Integer infoStateUp) {
		this.infoStateUp = infoStateUp;
	}

	public Integer getInfoStateDown() {
		return infoStateDown;
	}

	public void setInfoStateDown(Integer infoStateDown) {
		this.infoStateDown = infoStateDown;
	}

	public Integer getFileDockStateUp() {
		return fileDockStateUp;
	}

	public void setFileDockStateUp(Integer fileDockStateUp) {
		this.fileDockStateUp = fileDockStateUp;
	}

	public Integer getFileDockStateDown() {
		return fileDockStateDown;
	}

	public void setFileDockStateDown(Integer fileDockStateDown) {
		this.fileDockStateDown = fileDockStateDown;
	}

	public String getAdAterialList() {
		return adAterialList;
	}

	public void setAdAterialList(String adAterialList) {
		this.adAterialList = adAterialList;
	}

	public Integer getLastDockState() {
		return lastDockState;
	}

	public void setLastDockState(Integer lastDockState) {
		this.lastDockState = lastDockState;
	}

	public Integer getStageIsComplete() {
		return stageIsComplete;
	}

	public void setStageIsComplete(Integer stageIsComplete) {
		this.stageIsComplete = stageIsComplete;
	}

	public Integer getAllIsComplete() {
		return allIsComplete;
	}

	public void setAllIsComplete(Integer allIsComplete) {
		this.allIsComplete = allIsComplete;
	}

	public String getIsTmp() {
		return isTmp;
	}

	public void setIsTmp(String isTmp) {
		this.isTmp = isTmp;
	}

	public String getOnlineDecisionCode() {
		return onlineDecisionCode;
	}

	public void setOnlineDecisionCode(String onlineDecisionCode) {
		this.onlineDecisionCode = onlineDecisionCode;
	}

	public Date getFirstSubmitTime() {
		return firstSubmitTime;
	}

	public void setFirstSubmitTime(Date firstSubmitTime) {
		this.firstSubmitTime = firstSubmitTime;
	}

	public Date getFirstDockingDate() {
		return firstDockingDate;
	}

	public void setFirstDockingDate(Date firstDockingDate) {
		this.firstDockingDate = firstDockingDate;
	}
	
}