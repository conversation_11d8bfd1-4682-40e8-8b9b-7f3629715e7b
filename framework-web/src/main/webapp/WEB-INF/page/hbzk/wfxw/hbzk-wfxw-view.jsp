<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<!-- ww -->
<html lang="en">
<head>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<%String app_myenvirprotect = PropertiesHandlerUtil.getValue("app_myenvirprotect","fastdfs");%>
<c:set var="SERVER_APP"><%=app_myenvirprotect%></c:set>
<meta charset="utf-8">
<title>福建环境监察全过程业务智能办理系统</title>
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
	<style>
		body {
			position: relative;
		}
		ul.nav-pills {
			top: 120px;
			position: fixed;
		}
	
		@media screen and (max-width: 810px) {
			#section1, #section2, #section3, #section4, #section5, #section6, #section7  {
				margin-left: 150px;
			}
		}
	</style>
<body class="overflow-hidden" >
				<div style="position:absolute; top: 0px;left:0px;">
				 <input type="text" class="copy-id text-input" id="copyInput" value="【设施设置情况】执法人员现场检查发现，该公司厂房有XXXX台生产设备，分别为XXXXXXXXXXXX、XXXXXXXXXXXX和XXXXXXXXXXXX。
				【暗管排放痕迹】执法人员对位于厂房北侧印刷机进行检查，发现印刷机旁堆放有部分油墨桶，执法人员绕至印刷机后侧，发现地面上蔓延有明显的红色清洗废水，印刷机后有一条印刷机清洗管道，清洗管道下方，有建设一个约长0.5米、宽0.4米、高0.2米左右的蓄水池，蓄水池里有明显红色油墨沾染痕迹。
				【暗管设置情况】蓄水池下方挖有一个小孔，蓄水池里的油墨废水可通过小孔处从该公司私自建设的PVC管道引至厂房西侧，执法人员绕至PVC管尽头处，发现PVC联通厂外沟渠，废水经私自建设的PVC管排至沟渠中。
				【当前排放情况】步行至该公司南面的XXXX河（渠、江、河）沿岸，发现水渠中仍有从该公司排出的红色废水痕迹。
				【采样情况】XXXXXXX环境监测站工作人员XXXXXXX立即对该公司直排的油墨废水汇入XXXX河（渠、江、河）处采样水样一瓶。">
				</div>
			<div class="main-container"   id ="behaviourData">
				<div class="padding-md" > 
					<!--结果查看row-->
                    <div class="row" >
                        <div class="col-md-9"> 
                            <div class="smart-widget widget-blue" style="background-color:#e2e2e2;">
                                <div class="smart-widget-header font-16">
                                <body class="overflow-hidden">
                                    <i class="fa fa-arrow-right"></i> {{items.title}}
                                </div>
                                <div class="smart-widget-inner table-responsive" >
                                  <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div>
                               
                                  <input type="hidden" id ="behaviourId" value="${behaviour_id }">
                                  	<div class="smart-widget-body form-horizontal font-14" id="section" >
                                  <div class="smart-widget widget-blue "style="margin-top: 10px;">
                                  	  <div id="section1"  >
                                          <p  style="margin-left: 20px;margin-top: 20px;" id="uptime">发布时间：<span>{{items.uptime}}</span></p>
                                          <p style="margin-left: 20px;"  id="beh_fact">监察要素：<span>
                                          <span  v-for="(factorList, index) in items.factor">{{factorList.factor_name}}&nbsp;</span>
                                          </span></p>
                                          <p   style="margin-left: 20px;"  id="typename">行为类型：<span>{{items.typename}}</span></p>
                                         
                                        </div>
                                      </div>
                                       <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                         <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px;margin-top: 16px;">取证要点:</h4>
                                          <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                          <div>
                                          		<p style="margin-left: 20px;">{{items.evidence}}</p>
                                          </div>
                                       
                                       </div>
                                        <div	v-if="behaviourData.beh_id ==13">
                                        <div id="section7">
                                      	 <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                          <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px;margin-top: 16px;">勘察笔录模板</h4>
                                            <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                            	<div style="margin-left: 15px;" >
                                            	【设施设置情况】执法人员现场检查发现，该公司厂房有XXXX台生产设备，分别为XXXXXXXXXXXX、XXXXXXXXXXXX和XXXXXXXXXXXX。<br/>
                                            	【暗管排放痕迹】执法人员对位于厂房北侧印刷机进行检查，发现印刷机旁堆放有部分油墨桶，执法人员绕至印刷机后侧，发现地面上蔓延有明显的红色清洗废水，印刷机后有一条印刷机清洗管道，清洗管道下方，有建设一个约长0.5米、宽0.4米、高0.2米左右的蓄水池，蓄水池里有明显红色油墨沾染痕迹。<br/>
                                            	【暗管设置情况】蓄水池下方挖有一个小孔，蓄水池里的油墨废水可通过小孔处从该公司私自建设的PVC管道引至厂房西侧，执法人员绕至PVC管尽头处，发现PVC联通厂外沟渠，废水经私自建设的PVC管排至沟渠中。<br/>
                                            	【当前排放情况】步行至该公司南面的XXXX河（渠、江、河）沿岸，发现水渠中仍有从该公司排出的红色废水痕迹。<br/>
                                            	【采样情况】XXXXXXX环境监测站工作人员XXXXXXX立即对该公司直排的油墨废水汇入XXXX河（渠、江、河）处采样水样一瓶。
			                                    <div class="text-right" style="margin-right: 30px;margin-top: 20px;">
			                                       <button type="button" onclick="copyClick()" class="btn btn-info no-shadow text-right padding-sm" style="margin-bottom: 20px;" >复制到笔录</button>
                                           		 </div>
                                            </div>
                                         </div>
                                      </div>
                                      </div>
                                         <div  id="section2"   >
                                        <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                          <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px;margin-top: 16px;">违法事实认定的法律依据</h4>
                                          <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                          
                                           <div  v-for="(lawList, index) in lowsArr1">
                                           		<p> <a v-on:click="lawDetailBtn(lawList.law_id)" style="margin-left: 20px;margin-top: 16px;color:#23b7e5;cursor: pointer;"> {{lawList.law_name}}</a></p>
		                                         <div  v-for="(articleWebList, index) in lawList.article_web" >
		                                            <p style="margin-left: 20px;margin-top: 16px;">第{{articleWebList.article}}条</p>
		                                         	<p style="margin-left: 20px;margin-top: 16px;">{{articleWebList.content}}</p>
		                                         </div>
			                                </div>
			                              </div>
			                              </div>
			                                 
                                      <div id="section3" data-spy="affix" data-offset="190">
			                         <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                           <h4 style="font-size: 20px;font-weight:bold ;margin-left:20px; margin-top: 16px;">环境行政处罚的法律依据</h4>
                                            <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                       		  <div v-for="(lawList, index) in lowsArr2">
                                               <p> <a v-on:click="lawDetailBtn(lawList.law_id)" style="margin-left: 20px;margin-top: 16px;color:#23b7e5;curosr: pointer;"> {{lawList.law_name}}</a></p>
			                                         <div  v-for="(articleWebList, index) in lawList.article_web" >
			                                          	<p style="margin-left: 20px;margin-top: 16px;">第{{articleWebList.article}}条</p>
			                                         	<p style="margin-left: 20px;">{{articleWebList.content}}</p>
			                                         </div>
	                                       </div>
	                                       </div>
                                      </div>
                                     	 <div id="section4">
                                      <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                          <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px; margin-top: 16px;">对拒不改正违法行为的深度处罚依据</h4>
  										    <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
  										  <div v-for="(lawList, index) in lowsArr3">
                                           <p> <a  v-on:click="lawDetailBtn(lawList.law_id)" style="margin-left: 20px;margin-top: 16px;color:#23b7e5;curosr: pointer;">     {{lawList.law_name}}</a></p>
			                                         <div  v-for="(articleWebList, index) in lawList.article_web" >
			                                          	<p style="margin-left: 20px;">第{{articleWebList.article}}条</p>
			                                         	 <p style="margin-left: 20px;">{{articleWebList.content}}</p>
			                                         </div>
			                                </div>
                                     	 </div>
                                     	</div>
                                     	
                                       <div id="section5">
                                      <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                          <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px; margin-top: 16px;">对持续违法行的处罚依据</h4>
                                               <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                             <div v-for="(lawList, index) in lowsArr4">
                                                <p> <a  v-on:click="lawDetailBtn(lawList.law_id)" style="margin-top: 16px;margin-left: 20px;color:#23b7e5;curosr: pointer;">     {{lawList.law_name}}</a></p>
			                                         <div  v-for="(articleWebList, index) in lawList.article_web" >
			                                          	<p style="margin-left: 20px;margin-top: 16px;">第{{articleWebList.article}}条</p>
			                                         	<p style="margin-left: 20px;margin-top: 16px;">{{articleWebList.content}}</p>
			                                         </div>
			                                </div>
                                      	</div>
                                      </div>
                                    	  <div id="section6">
                                      <div class="smart-widget widget-blue" style="margin-top: 20px;">
                                          <h4 style="font-size: 20px;font-weight:bold ;margin-left: 20px;margin-top: 16px;">司法解释</h4>
                                            <hr style="height:2px;border:0;border-top:2px solid #23b7e5;padding:5px;margin:5px;"  />
                                           <div v-for="(lawList, index) in lowsArr5">
                                           <p> <a v-on:click="lawDetailBtn(lawList.law_id)" style="margin-left: 20px;margin-top: 16px;color:#23b7e5;curosr: pointer;">     {{lawList.law_name}}</a></p>
			                                         <div  v-for="(articleWebList, index) in lawList.article_web" >
			                                          	<p style="margin-left: 20px;margin-top: 16px;">第{{articleWebList.article}}条</p>
			                                         	 <p style="margin-left: 20px;margin-top: 16px;">{{articleWebList.content}}</p>
			                                         </div>
			                                </div>
                                         </div>
                                      </div>
                                      </div>
                                  </div>
                                </div>
                    </div>   
                        <!--滚动监听row-->
                        <div class="col-md-3"> 
                        	<div class="text-right"><a class="btn btn-info no-shadow"  onclick="goBack('${preUrl}')">返回</a></div>
                              <nav id="myScrollspy">
                                <div class="container-fluid"> 
                                    <div class="container-fluid"> 
                                        <ul class="nav nav-pills nav-stacked" id="leftMeum">
                                            <h4>章节目录</h4>
                                            <li class="active"><a href="#section1" >取证要点</a></li>
                                             <li> <a v-if="behaviourData.beh_id ==13" href="#section7" >勘察笔录模板</a></li>
                                            <li><a href="#section2" >违法事实认定的法律依据</a></li>
                                            <li><a href="#section3" >环境行政处罚的法律依据</a></li>
                                            <li><a href="#section4" >对拒不改正违法行为的深度处罚依据</a></li>
                                            <li><a href="#section5" >对持续违法行的处罚依据</a></li>
                                            <li><a href="#section6" >司法解释</a></li>
                                        </ul>                                
                                    </div>	
                                    </div>
                                  
                                </div>		
                            </nav>                                      
                        </div>
                          </div>
                        <!--./滚动监听row-->                              
					</div>
			   </div>  
                    <!--./结果查看row-->
			<script type="text/javascript">
			function copyClick() {
				 $('.copy-id').select();
			        var a  = document.execCommand("copy",false,null);
				  alert("复制剪贴板成功！")
			 }
				
				var SERVER_APP = '${SERVER_APP}';
				var behaviourData = new Object();
					behaviourData.beh=null;
					behaviourData.beh_fact=null;
					behaviourData.beh_id=null;
					behaviourData.behtype_id=null;
					behaviourData.evidence=null;
					behaviourData.factor=null;
					behaviourData.factor_id=null;
					behaviourData.laws=null;
					behaviourData.title=null;
					behaviourData.typename=null;
					behaviourData.uptime=null;
					behaviourData.user_id=null;
					behaviourData.username=null;
				var lowsArr1 = null;
				var lowsArr2 =null;
				var lowsArr3 = null;
				var lowsArr4 =null;
				var lowsArr5 = null;
					
				$(document).ready(function(){
					//页面初始化加载
					 var behaviour_id=$("#behaviourId").val();
					   $.ajax({
				     		cache : true,
				     		type : "GET",
				     		async : false,
				     		//api/test/detail/behaviour/74
				     		url :SERVER_APP+"/api/test/detail/behaviour/"+behaviour_id,
				     		error : function(request) {
				     			swal("错误!","请求异常！", "error");
				     		},
				     		success : function(data) {
				     			var dataObj =$.parseJSON(data) ;
				     			behaviourData=dataObj.behaviour;
				     			lowsArr1 = behaviourData.laws[1];
								lowsArr2 =behaviourData.laws[2];
								lowsArr3 = behaviourData.laws[3];
								lowsArr4 =behaviourData.laws[4];
								lowsArr5 = behaviourData.laws[5];
				     			console.log(behaviourData)
				     		} 
					   });
				})
				  var vue = new Vue({
							  el: '#behaviourData',
							  data: {
							    items:behaviourData,
							    lowsArr1 : lowsArr1,
								lowsArr2 : lowsArr2,
								lowsArr3 : lowsArr3,
								lowsArr4 :lowsArr4,
								lowsArr5 : lowsArr5
							  },
							  methods: {
								lawDetailBtn:function (law_id){
									business.addMainContentParserHtml(WEBPATH + '/law/law_detail_page', {law_id:law_id});
								  }
							  }
				  });
				
				//返回上一步主菜单
				function goBack(preUrl) {
					if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
						business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId="+'${menuId}', $("#searchForm").serialize());
					} else {
						swal({
							title : "提示！",
							text : "返回信息错误，请刷新后重试。",
							type : "error"
						})
					}
				}
				
				//Delete Widget Confirmation
		</script>
        
</body>
</html>
