<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>
<body>
<div class="main-container">
	<div class="padding-md">
             
             	<!--第一层快速查询row-->
		<div class="row">
						<div class="col-lg-12">
							<div class="smart-widget">
								<div class="smart-widget-inner">	
								 <div class="col-lg-12 col-md-12" style="float:left;">
                                      <div  style="font-size:14px;color:red; padding:10px 20px;">本数据来源于数据资源中心接入的“福建省污染源监测数据管理及信息发布系统”数据</div>
                                 </div>								
									<div class="smart-widget-body form-horizontal">
                                    	<div class="form-group">
                                            <label class="control-label col-lg-2">执法对象所在行政区</label>
                                            <div class="col-lg-2">
                                                <select
                                                    class="form-control">
                                                    <option value="福建省">福建省</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-3">
                                                <select  class="form-control" id="deptBelongCity" name="deptBelongCity" onclick="getCity()">
                                                   
                                                </select>
                                            </div>
                                            <div class="col-lg-3">
                                                <select class="form-control" id="deptBelongCountry" name="deptBelongCountry"></select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-lg-2"  >执法对象名称</label>
                                            <div class="col-lg-3">
                                                <input id="objName" name="objName"   type="text" placeholder="执法对象名称" class="form-control" data-parsley-required="true">
                                            </div>
                                            <label class="control-label col-lg-2">监测日期</label>
                                            <div class="col-lg-3" style="padding:0;">
                                                <div class="col-lg-6">
                                                    <input type="text" placeholder="开始日期" class="form-control" data-parsley-required="true" readonly="readonly"
                                						id="quesDateBegin" name="quesDateBegin">
                                                </div>
                                                <div class="col-lg-6">
                                                    <input type="text" placeholder="结束日期" class="form-control" data-parsley-required="true" readonly="readonly"
                                						id="quesDateEnd" name="quesDateEnd">
                                                </div>
                                            </div>
                                        </div>
                                    	<div class="form-group">                                                                                    
                                            <div class="col-lg-10 text-right">
                                                <button class="btn btn-info" id="searchSubmit"  name = "searchSubmit" type="button" style="width:120px;">查询</button>
                                            </div>
                                        </div>                                            
									</div>
								</div>
							</div>
						</div>
					</div>
					
		
                 <!--./第一层快速查询row-->
                 
		<!--第二层任务办理row-->
                 <div class="row">
                     <!--任务办理-->
                     <div class="col-lg-12">                         	  
                         <div class="smart-widget widget-blue">
                             <div class="smart-widget-header font-16">
                                 <i class="fa fa-arrow-right"></i> 监督性监测超标执法
                                 <span class="smart-widget-option">
                                     <span class="refresh-icon-animated">
                                         <i class="fa fa-circle-o-notch fa-spin"></i>
                                     </span>
                                    <!--  <a href="#" class="widget-toggle-hidden-option">
                                         <i class="fa fa-cog"></i>
                                     </a>
                                     <a href="#" class="widget-collapse-option" data-toggle="collapse">
                                         <i class="fa fa-chevron-up"></i>
                                     </a>
                                     <a href="#" class="widget-refresh-option">
                                         <i class="fa fa-refresh"></i>
                                     </a>
                                      -->
                                 </span>
                             </div>
                           <div class="smart-widget-inner table-responsive">
                             <div class="smart-widget-hidden-section">
                                     <ul class="widget-color-list clearfix">
                                         <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                         <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                         <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                         <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                         <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                         <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                         <li style="background-color:#e36159;" data-color="widget-red"></li>
                                         <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                         <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                         <li style="background-color:#fff;" data-color="reset"></li>
                                     </ul>
                               </div>
                            
                                <table class="table-no-bordered"
									id="swingTagManageTable"></table>
                             </div>
                         </div>
                     </div>
		</div>
	</div>
</div>


</body>
<script type="text/javascript">
var webpath = '${webpath}';
var htmlCity = "<option value=''>请选择</option>"; 
var htmlCounty = "<option value=''>请选择</option>"; 
var pageSize=10;
var pageNumber=1;
$(document).ready(function(){
	$.ajax({
		type:"post",
		url:WEBPATH+"/tArea/chickUserArea",
		dataType:"json",
		async:false,
		data:{},
		success:function(data){//省级用户+
			if(data.cityStatus =='1'){
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/cityList",
					async:false,
					dataType:"json",
					success:function(data){
						$("#deptBelongCity").append("<option value=''>请选择</option>"); 
						$("#deptBelongCountry").append("<option value=''>请选择</option>"); 
						$.each(data,function(i,item){
							$("#deptBelongCity").append("<option value="+item.code+">"+item.name+"</option>");
						});
					}
				});
			}else if(data.cityStatus =="2"){ //市级用户
				$("#deptBelongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/countyListByCode",
					dataType:"json",
					data:{parentCode:data.cityCode},
					success:function(data){
						$("#deptBelongCountry").append("<option value=''>请选择</option>"); 
						$.each(data,function(i,item){
							$("#deptBelongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
						});
					}
				});
			}else{
				//县级用户
				$("#deptBelongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$("#deptBelongCountry").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
			}
		}
	});
 	// 市联动县
	$("#deptBelongCity").change(function(){
		if ($(this).val() == ""){
			$("#deptBelongCountry option").remove();
			$("#deptBelongCountry").html(htmlCounty);
			return;
		}
		var parentCode = $(this).val();
		$("#deptBelongCountry option").remove();
		$.ajax({
			type:"post",
			url:WEBPATH+"/tArea/countyListByCode",
			async:false,
			dataType:"json",
			data:{parentCode:parentCode},
			success:function(data){
				$("#deptBelongCountry").html(htmlCounty);
				$.each(data,function(i,item){
					$("#deptBelongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
				});
			}
		});
	});
	
 	$("[name='quesDateBegin']").datetimepicker({
		language:'cn',
	    format:'yyyy-mm-dd',
	    autoclose: true,
	    todayBtn: true,
	    clearBtn: true,
	    minView:'month',
	    maxView:'decade'
	 });
	$("[name='quesDateEnd']").datetimepicker({
		language:'cn',
	    format:'yyyy-mm-dd',
	    autoclose: true,
	    todayBtn: true,
	    clearBtn: true,
	    minView:'month',
	    maxView:'decade'
	 });
	$('#searchSubmit').click(function() {
		$('#swingTagManageTable').bootstrapTable('refreshOptions',{pageNum:1,pageSize:10});
	});
	//返回按钮之后参数和页码的回显
	var params = '${params}';
	//console.log("返回参数："+params);
	if(params != null && params != '' && params != 'undefined'){
		var jsonParam = $.parseJSON(params);
		pageNumber = parseInt(jsonParam['pageNum']);
		pageSize = parseInt(jsonParam['pageSize']);
			
		for(var key in jsonParam){
			//绑定设定条件
			if(key=='deptBelongCity'){
				$("#"+key).find("option[value='"+jsonParam[key]+"']").attr("selected","selected");
				//$("#"+key).val(jsonParam[key]);
				$("#"+key).trigger('change');
				continue;
			}
			$("#"+key).val(jsonParam[key]);
		}
	}
	LoadingSwingTagManageItems();
	//$('#swingTagManageTable').bootstrapTable('refresh',{pageNumber:pageNumber,pageSize:pageSize});
});
function LoadingSwingTagManageItems() {
	$('#swingTagManageTable').bootstrapTable({
		method : 'post',
		dataType : "json",
		//url : webpath + '/swingtagmanage/swingtagmanageList',
		url : webpath + '/overlaw/overLawList',
		undefinedText : '-',
		pagination : true, // 分页  
		striped : true, // 是否显示行间隔色  
		queryParamsType : "",
		locale : 'zh-CN',
		pageSize : 10, // 设置默认分页为 15
		pageNumber : 1,
		clickToSelect : true,
		pageList : [10, 20, 30, 50 ], // 自定义分页列表
		contentType : "application/x-www-form-urlencoded",
		// showColumns : true, // 显示隐藏列  
		sidePagination : "server", //服务端请求
		queryParams:queryParams,//参数
		uniqueId : "id", // 每一行的唯一标识  
		columns : [ {
			field : "",
			title : "序号",
			align : 'center',
			formatter:function(value,row,index){
				return index+1;
			}
		}, {
			field : "lawobjectName",
			title : "执法对象名称",
			align : 'center',
			/* formatter:function(value,row,index){
				if(value ==1){
					return "已发起";
				}else{
					return "暂存中";
				}
			} */
		}, {
			field : "",
			title : "执法对象所在行政区",
			align : 'center',
			 formatter: function (value, row, index) {
				return row.lawobjectProvince+row.lawobjectCity+row.lawobjectCounty;
            } 
		}, {
			field : "monitorDate",
			title : "监测日期",
			align : 'center',
			formatter: function (value, row, index) {
					if(value==null){
						return "";
					}else {
						var date = new Date(value);
		                var y = date.getFullYear();
		                var m = date.getMonth() + 1;
		                var d = date.getDate();
		                return y + '-' +m + '-' + d;
					}
            }
		}, {
			field : "overproofInfo",
			title : "超标信息",
			align : 'center'
		}, {
			field : "",
			title : "已关联执法编号",
			align : 'center',
		 	formatter: function (value, row, index) {
				
				var temp = '';
				$.ajax({
					data:{id:row.id},
					dataType:"json",
					type:"POST",
					url:webpath+"/overlaw/getLinkTask",
					async : false,
					success:function(data){
						temp = data.data;
					},
					error:function(data){
						temp = data.code;
					}
				});
				return temp;
            } 
		},{
			field : "",
			title : "已关联案件编号",
			align : 'center',
			formatter: function (value, row, index) {
					var temp = '';
					$.ajax({
						data:{id:row.id},
						dataType:"json",
						type:"POST",
						url:webpath+"/overlaw/getLinkCase",
						async : false,
						success:function(data){
							temp = data.data;
						},
						error:function(data){
							temp = data.data;
						}
					});
					return temp;
	            } 
		},{
			field : "",
			title : "操作",
			align : 'center',
			formatter:function(value,row,index){
				
				return "<a href='#' onclick=\"linkCaseAndLaw('"+row.lawobjectId+"','"+row.id+"')\"><i class=\"fa fa-pencil-square-o\" style='color:#23b7e5;'>关联</i></a>";
			}
			//<a href="gpdb-fqgp.html"><i class="fa fa-pencil-square-o" style="color:#23b7e5;">编辑</i></a>
		} ],
		responseHandler : function(res) {
			return {
				total : res.data.total,
				rows : res.data.list
			};
		},
		onCheck : function(row, $element) {

		},//单击row事件
		onUncheck : function(row, $element) {

		},
		onUncheckAll : function(row, $element) {

		},
		onCheckAll : function(row, $element) {

		},
		onRefresh : function() {

		},
		formatLoadingMessage : function() {
			return "请稍等，正在加载中...";
		},
		formatNoMatches : function() { //没有匹配的结果
			return '无符合条件的记录';
		}
	});
	}
function queryParams(params) {
	var deptBelongCity = $("#deptBelongCity").val();
	var deptBelongCountry =$("#deptBelongCountry").val();
	var objName =$("#objName").val();
	var quesDateBegin =$("#quesDateBegin").val();
	var quesDateEnd = $("#quesDateEnd").val();
    var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
		 pageNum: params.pageNumber,
         pageSize: params.pageSize,
         deptBelongCity:deptBelongCity,
         deptBelongCountry:deptBelongCountry,
         objName:objName,
         quesDateBegin:quesDateBegin,
         quesDateEnd:quesDateEnd,
    };
    return temp;
}


function linkCaseAndLaw(lawObjectId,lawID){
	//macroMgr.onLevelTwoMenuClick(null, '/overlaw/linkCaseAndLaw?monitorID='+id);
	business.addMainContentParserHtml(WEBPATH+'/overlaw/linkCaseAndLaw?lawObjectId='+lawObjectId+"&lawID="+lawID, null);
}
//根据任务编号跳转任务页面
function linkToTask(taskId){
	var obj={taskId:taskId,lawObjectType:'1',parentUrl:'0',nodeCode:'0'};
	 business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType=0',obj);
}
//根据案件调转案件页面
function linkToCase(caseId){
	//business.addMainContentParserHtml(WEBPATH+'/caseInfo/baseInfoCasePage?caseId='+caseId+"&parentUrl=1&selectType=1", null);
	//business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?caseId='+caseId+"&parentUrl=0&selectType=0&lawObjectTrpe=1", null);
	business.addMainContentParserHtml(WEBPATH+'/caseInfo/baseInfoCasePage?caseId='+caseId+"&selectType=1", null);
}

</script>
</html>