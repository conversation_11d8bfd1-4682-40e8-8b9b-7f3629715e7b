<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<html>
<body>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
	<div class="modal-header">
					<input type="hidden" id="pageLayer" name="pageLayer" value='${pageLayer }'>
					<div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info"
						id="industryconfirm" data-dismiss="modal">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
					<h4 class="modal-title" id="myModalLabel">行业类型</h4>
	</div>
				<div class="modal-body">
					<div class="smart-widget-body form-horizontal">
						<form id="searchForm" role="form">
							<div class="form-group">
								<label for="行业名称" class="col-lg-2 control-label">行业名称（说明）</label>
								<div class="col-lg-8">
									<input type="text" class="form-control" value="" name='name'
										id='name' placeholder="请输入行业名称（说明）">
								</div>
							</div>
							<div class="form-group">
								<label for="小类代码" class="col-lg-2 control-label">小类代码</label>
								<div class="col-lg-8">
									<input type="text" class="form-control" value="" name='smallCode'
										id='smallCode' placeholder="请输入小类代码">
								</div>
							</div>
							
							<div class="form-group">
								<label for="门类" class="col-lg-2 control-label">门类</label>
								<div class="col-lg-8">
									<select class="form-control" name="firstCalss" id="firstCalss">
										<option value="">——请选择——</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="大类" class="col-lg-2 control-label">大类</label>
								<div class="col-lg-8">
									<select class="form-control" name="secondClass"
										id="secondClass">
									<option value="">——请选择——</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="中类" class="col-lg-2 control-label">中类</label>
								<div class="col-lg-8">
									<select class="form-control" name="thirdClass" id="thirdClass">
										<option value="">——请选择——</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="小类" class="col-lg-2 control-label">小类</label>
								<div class="col-lg-8">
									<select class="form-control" name="fourthClass"
										id="fourthClass">
										<option value="">——请选择——</option>
									</select>
								</div>
							</div>
						</form>
						<div class="form-group">.
						<label  class="col-lg-9 col-sm-9 col-xs-9 control-label"></label>
							<div class="col-lg-2 col-sm-2 col-xs-2">
								<button type="button" class="btn btn-info" id="searchButt"  onclick="checkIndustryBtn()"
								>查 询</button>
							</div>
						</div>
					</div>
					<table class="table table-striped table-bordered no-margin">
						<thead>
							<tr>
								<th class="text-center" style="width: 5%;">门类</th>
								<th class="text-center" style="width: 6%;">大类</th>
								<th class="text-center" style="width: 6%;">中类</th>
								<th class="text-center" style="width: 6%;">小类</th>
								<th class="text-center" style="width: 30%;">类别名称</th>
								<th class="text-center" style="width: 40%;">说明</th>
								<th class="text-center" style="width: 7%;">操作</th>
							</tr>
						</thead>
						<tbody id="CheckUserChooseTr">
						</tbody>
						<tr>
							<td colspan="7"><span id="reminder"></span></td>
						</tr>
					</table>
				</div>
			<!--第三层翻页-->
            	<div id ="pageTotal" style="float: left;color:#0099cc; margin:10px 0 0 10px;"></div>
				<div style="margin: 0; float: right; ">
					<div style="float: left; margin:-15px 10px 0 0;">
						<div class="page" id ="pageDiv">
							<ul class="pagination" id="pageCon">
							</ul>
                        </div>
					</div>
                    <div style="float: right; width:100px;padding:5px;">
                        <select class="form-control" id="pageSizeId">
                            <option value="5">5条</option>
                            <option value="10" selected>10条</option>
                            <option value="15" >15条</option>
                            <option value="20" >20条</option>
                            <option value="30" >30条</option>
                        </select>
                    </div>
				</div>
				<%--<div style="float: left;">
                <span id ="pageTotal" style="padding:12px;  float:left; color:#0099cc;"></span>
				</div>
			   <span  style=" margin: 5px 10px 5px 0;  float: right;">
			   	
			   </span>
				<div style="margin: 30px 0 0px 0; float: right;">
					<div style="float: left;">
						<div class="page" id ="pageDiv">
							<ul class="pagination" id="pageCon">
							</ul>
                        </div>
                        <div style="float: right;">
                            <select class="form-control" id="pageSizeId">
								<option value="5">5条</option>
								<option value="10" selected>10条</option>
								<option value="15" >15条</option>
								<option value="20" >20条</option>
								<option value="30" >30条</option>
							</select>
                        </div>
					</div>
				</div>--%>
				<!--./第三层翻页-->
				<!--./确定 关闭按钮-->
				<div class="modal-footer" style="margin-top: 50px;">
					<!-- <button type="button" class="btn btn-info"
						id="industryconfirm" data-dismiss="modal">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button> -->
				</div>
	<!-- ./检查人选择（Modal） -->
	<script type="text/javascript">
	$(function(){
		$('#jlxjcx').on('hide.bs.modal', function () {
			  $(this).removeData("bs.modal");
		});
	})
	$(document).ready(function(){
		//监听enter查询，内包含监听回退键
	    business.listenEnter("searchButt");
		$('#Industrytype').on('hidden.bs.modal', function () {
			   $(this).removeData("bs.modal");
			   $(this).removeData("bs.modal");  
			   var pageLayer = $("#pageLayer").val();
			   if(pageLayer!=1){
			  		document.getElementsByTagName('body')[0].className = 'modal-open';
			   }else{
				   
			   }	
		})
		$('#IndustrytypeModel').on('hidden.bs.modal', function () {
			   $(this).removeData("bs.modal");
			   $(this).removeData("bs.modal");  
			   var pageLayer = $("#pageLayer").val();
			   if(pageLayer!=1){
			  		document.getElementsByTagName('body')[0].className = 'modal-open';
			   }else{
				   
			   }		
		})
		/*$('#selectIndustry').on('hidden.bs.modal', function () {
			   $(this).removeData("bs.modal");
			   $(this).removeData("bs.modal");
			   document.getElementsByTagName('body')[0].className = 'modal-open';

		})*/
	});
	$("#secondClass").change(
				function() {
					var secondClass = $("#secondClass").val();
					$("#fourthClass option").remove();
					$("#fourthClass").html("<option value=''>——请选择—— </option>");
					$("#thirdClass").html("<option value=''>——请选择—— </option>");
					$.ajax({
						type : "POST",
						url : WEBPATH + '/zfdx/getIindustryTypeById',
						data : {
							id : secondClass,
							industryLevel : 3
						},
						success : function(msg) {
							$("#thirdClass").html(
									"<option value=''>——请选择—— </option>");
							if (msg.length != 0) {
								$.each(msg, function(i, item) {
									$("#thirdClass").append("<option value="+item.thirdClass+"  >"+ item.name + "</option>");
								});
							}
						}
					});
				});
		//  小类加载
		$("#thirdClass").change(
				function() {
					var thirdClass = $("#thirdClass").val();

					$("#fourthClass option").remove();
					$.ajax({
						type : "POST",
						url : WEBPATH + '/zfdx/getIindustryTypeById',
						data : {
							id : thirdClass,
							industryLevel : 4
						},
						success : function(msg) {
							$("#fourthClass").html(
									"<option value=''>——请选择—— </option>");
							if (msg.length != 0) {
								$.each(msg, function(i, item) {
									$("#fourthClass").append("<option value="+item.fourthClass+"  >"+ item.name + "</option>");
								});
							}
						}
					});
				});
		//da 类加载
		$("#firstCalss").change(
				function() {
					var firstCalss = $("#firstCalss").val();
					$("#thirdClass option").remove();
					$("#fourthClass option").remove();
					$("#fourthClass").html("<option value=''>——请选择—— </option>");
					$("#secondClass").html("<option value=''>——请选择—— </option>");
					$("#thirdClass").html("<option value=''>——请选择—— </option>");
					$.ajax({
						type : "POST",
						url : WEBPATH + '/zfdx/getIindustryType',
						data : {
							firstCalss : firstCalss,
							industryLevel : 2
						},
						success : function(msg) {
							// $("#firstCalss").html(html);
							if (msg.length != 0) {
								$.each(msg, function(i, item) {
									$("#secondClass").append("<option value="+item.secondClass+"  >"+ item.name + "</option>");
								});
							}
						}
					});
				});
		$.ajax({
			type : "POST",
			url : WEBPATH + '/zfdx/getIindustryType',
			data : "industryLevel=1",
			success : function(msg) {
				if (msg.length != 0) {
					$.each(msg, function(i, item) {
						$("#firstCalss").append("<option value="+item.firstCalss+"  >" + item.name + "</option>");
					});
				}
			}
		});
		//行业类型选择

			var industryTypeCode = $("#industryTypeCode").val();
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/industryTypecheckList',
				data : $('#searchForm').serialize(),
				success : function(msg) {
					$("#reminder").html("");
					$("#CheckUserChooseTr").children().remove();
					if (msg.total != 0) {
						var temp = msg.list;
						for (var i = 0; i < temp.length; i++) {
							if (temp[i].secondClass != "" && temp[i].secondClass != null) {
								if(industryTypeCode !=null || industryTypeCode !=''){
									if(temp[i].fourthClass == industryTypeCode){
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' class='tampredio'   name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
									}else{
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio'  class='tampredio' name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
	
									}
								}
							}else{
								$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
										+ temp[i].firstCalss
										+ "</td><td class='text-center'>"
										+ temp[i].secondClass
										+ "</td><td class='text-center'>"
										+ temp[i].thirdClass
										+ "</td><td class='text-center'>"
										+ temp[i].fourthClass
										+ "</td><td class='text-center'>"
										+ temp[i].name
										+ "</td><td class='text-center'>"
										+ temp[i].industryDesc
										+ "</td><td class='text-center'></td></tr>")
							}
						}
						var curentPage = msg.pageNum;
						var totalPage = msg.pages;
						if (totalPage > 0) {
							var optionsPage = {
								bootstrapMajorVersion : 3,
								currentPage : curentPage,
								totalPages : totalPage,
								numberOfPages : 5,
								itemTexts : function(type, page,current) {
									switch (type) {
									case "first":
										return "首页";
									case "prev":
										return "&laquo;";
									case "next":
										return "&raquo;";
									case "last":
										return "尾页";
									case "page":
										return page;
									}
								},
								onPageClicked : function(event,originalEvent,type, page) {
									var pageSizeId = $("#pageSizeId").val();
									//alert('ssss')
									//business.addMainContentParserHtml(WEBPATH+'/zfdx/industryTypecheckList?pageNum='+page+'&pageSize='+pageSizeId,$("#searchForm").serialize());
									 $.ajax({
										type : "POST",
										url : WEBPATH
												+ '/zfdx/industryTypecheckList?pageNum='
												+ page
												+ '&pageSize='
												+ pageSizeId,
										data : $('#searchForm').serialize(),
										success : function(msg) {
											$("#reminder").html("");
											$("#CheckUserChooseTr").children().remove();
											if (msg.total != 0) {
												var temp = msg.list;
												for (var i = 0; i < temp.length; i++) {
													if (temp[i].secondClass != "" && temp[i].secondClass != null) {
														if(industryTypeCode !=null || industryTypeCode !=''){
															if(temp[i].fourthClass == industryTypeCode){
																$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																		+ temp[i].firstCalss
																		+ "</td><td class='text-center'>"
																		+ temp[i].secondClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].thirdClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].fourthClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].name
																		+ "</td><td class='text-center'>"
																		+ temp[i].industryDesc
																		+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
															}else{
																$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																		+ temp[i].firstCalss
																		+ "</td><td class='text-center'>"
																		+ temp[i].secondClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].thirdClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].fourthClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].name
																		+ "</td><td class='text-center'>"
																		+ temp[i].industryDesc
																		+ "</td><td class='text-center'><input type='radio' class='tampredio'   name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
							
															}
														}
													}else{
														$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																+ temp[i].firstCalss
																+ "</td><td class='text-center'>"
																+ temp[i].secondClass
																+ "</td><td class='text-center'>"
																+ temp[i].thirdClass
																+ "</td><td class='text-center'>"
																+ temp[i].fourthClass
																+ "</td><td class='text-center'>"
																+ temp[i].name
																+ "</td><td class='text-center'>"
																+ temp[i].industryDesc
																+ "</td><td class='text-center'></td></tr>")
													}
												}
											} else {
												$("#reminder").html("无符合条件的记录")
												 $('#pageCon').remove();
											}
											curentPage = msg.pageNum;
											totalPage = msg.pages;
										}
									}); 
								}
							}
							$('#pageCon').bootstrapPaginator(optionsPage);
						}
						$("#pageTotal").html("共"+msg.total+"条记录");
					} else {
						$("#pageTotal").html("共"+msg.total+"条记录");
						$("#reminder").html("无符合条件的记录")
						 $('#pageCon').remove();
					}
				}
			});
		
		  $('#pageSizeId').change(function() {
				$("#pageDiv").html("<ul class='pagination' id='pageCon'> </ul>")
				var pageSizeId = $("#pageSizeId").val();
				$.ajax({
					type : "POST",
					url : WEBPATH+ '/zfdx/industryTypecheckList?pageSize='+ pageSizeId,
					data : $('#searchForm').serialize(),
					success : function(msg) {
						$("#CheckUserChooseTr").children().remove();
						$("#reminder").html("");
						if (msg.total != 0) {
							var temp = msg.list;
							for (var i = 0; i < temp.length; i++) {
								if (temp[i].secondClass != "" && temp[i].secondClass != null) {
									if(industryTypeCode !=null || industryTypeCode !=''){
										if(temp[i].fourthClass == industryTypeCode){
											$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
													+ temp[i].firstCalss
													+ "</td><td class='text-center'>"
													+ temp[i].secondClass
													+ "</td><td class='text-center'>"
													+ temp[i].thirdClass
													+ "</td><td class='text-center'>"
													+ temp[i].fourthClass
													+ "</td><td class='text-center'>"
													+ temp[i].name
													+ "</td><td class='text-center'>"
													+ temp[i].industryDesc
													+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
										}else{
											$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
													+ temp[i].firstCalss
													+ "</td><td class='text-center'>"
													+ temp[i].secondClass
													+ "</td><td class='text-center'>"
													+ temp[i].thirdClass
													+ "</td><td class='text-center'>"
													+ temp[i].fourthClass
													+ "</td><td class='text-center'>"
													+ temp[i].name
													+ "</td><td class='text-center'>"
													+ temp[i].industryDesc
													+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
		
										}
									}
								}else{
									$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
											+ temp[i].firstCalss
											+ "</td><td class='text-center'>"
											+ temp[i].secondClass
											+ "</td><td class='text-center'>"
											+ temp[i].thirdClass
											+ "</td><td class='text-center'>"
											+ temp[i].fourthClass
											+ "</td><td class='text-center'>"
											+ temp[i].name
											+ "</td><td class='text-center'>"
											+ temp[i].industryDesc
											+ "</td><td class='text-center'></td></tr>")
								}
							}
							var curentPage = msg.pageNum;
							var totalPage = msg.pages;
							if (totalPage > 0) {
								var optionsPage = {
									bootstrapMajorVersion : 3,
									currentPage : curentPage,
									totalPages : totalPage,
									numberOfPages : 5,
									itemTexts : function(type, page,current) {
										switch (type) {
										case "first":
											return "首页";
										case "prev":
											return "&laquo;";
										case "next":
											return "&raquo;";
										case "last":
											return "尾页";
										case "page":
											return page;
										}
									},
									onPageClicked : function(event,originalEvent,type, page) {
										var pageSizeId = $("#pageSizeId").val();
										 $.ajax({
											type : "POST",
											url : WEBPATH
													+ '/zfdx/industryTypecheckList?pageNum='
													+ page
													+ '&pageSize='
													+ pageSizeId,
											data : $('#searchForm').serialize(),
											success : function(msg) {
												$("#reminder").html("");
												$("#CheckUserChooseTr").children().remove();
												if (msg.total != 0) {
													var temp = msg.list;
													for (var i = 0; i < temp.length; i++) {
														if (temp[i].secondClass != "" && temp[i].secondClass != null) {
															if(industryTypeCode !=null || industryTypeCode !=''){
																if(temp[i].fourthClass == industryTypeCode){
																	$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																			+ temp[i].firstCalss
																			+ "</td><td class='text-center'>"
																			+ temp[i].secondClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].thirdClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].fourthClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].name
																			+ "</td><td class='text-center'>"
																			+ temp[i].industryDesc
																			+ "</td><td class='text-center'><input type='radio'  class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
																}else{
																	$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																			+ temp[i].firstCalss
																			+ "</td><td class='text-center'>"
																			+ temp[i].secondClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].thirdClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].fourthClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].name
																			+ "</td><td class='text-center'>"
																			+ temp[i].industryDesc
																			+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
								
																}
															}
														}else{
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'></td></tr>")
														}
													}
												} else {
													$("#reminder").html("无符合条件的记录")
												}
												curentPage = msg.pageNum;
												totalPage = msg.pages;
											}
										}); 
									}
								}
								$('#pageCon').bootstrapPaginator(optionsPage);
								$("#pageTotal").html("共"+msg.total+"条记录");
							}
						} else {
							$("#pageTotal").html("共"+msg.total+"条记录");
							$("#reminder").html("无符合条件的记录");
							 $('#pageCon').remove();
						}
					}
				});
			}); 

		//行业类型查询
		 function checkIndustryBtn() {
			var pageSizeId = $("#pageSizeId").val();
			$("#pageDiv").html("<ul class='pagination' id='pageCon'> </ul>")
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/industryTypecheckList?pageSize='+ pageSizeId,
				data : $('#searchForm').serialize(),
				success : function(msg) {
					 $("#CheckUserChooseTr").children().remove();
					 $("#reminder").html("");
					if (msg.total != 0) {
						var temp = msg.list;
						for (var i = 0; i < temp.length; i++) {
							if (temp[i].secondClass != "" && temp[i].secondClass != null) {
								if(industryTypeCode !=null || industryTypeCode !=''){
									if(temp[i].fourthClass == industryTypeCode){
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
									}else{
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio'  class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
	
									}
								}
							}else{
								$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
										+ temp[i].firstCalss
										+ "</td><td class='text-center'>"
										+ temp[i].secondClass
										+ "</td><td class='text-center'>"
										+ temp[i].thirdClass
										+ "</td><td class='text-center'>"
										+ temp[i].fourthClass
										+ "</td><td class='text-center'>"
										+ temp[i].name
										+ "</td><td class='text-center'>"
										+ temp[i].industryDesc
										+ "</td><td class='text-center'></td></tr>")
							}
						}
						$("#pageTotal").html("共"+msg.total+"条记录");
					} else {
						$("#pageTotal").html("共"+msg.total+"条记录");
						$("#reminder").html("无符合条件的记录")
						$('#pageCon').remove();
					} 
					var curentPage = msg.pageNum;
					var totalPage = msg.pages;
					if (totalPage > 0) {
						var optionsPage = {
							bootstrapMajorVersion : 3,
							currentPage : curentPage,
							totalPages : totalPage,
							numberOfPages : 5,
							itemTexts : function(type, page,current) {
								switch (type) {
								case "first":
									return "首页";
								case "prev":
									return "&laquo;";
								case "next":
									return "&raquo;";
								case "last":
									return "尾页";
								case "page":
									return page;
								}
							},
							onPageClicked : function(event,originalEvent,type, page) {
								var pageSizeId = $("#pageSizeId").val();
								//alert('ssss')
								//business.addMainContentParserHtml(WEBPATH+'/zfdx/industryTypecheckList?pageNum='+page+'&pageSize='+pageSizeId,$("#searchForm").serialize());
								 $.ajax({
									type : "POST",
									url : WEBPATH
											+ '/zfdx/industryTypecheckList?pageNum='
											+ page
											+ '&pageSize='
											+ pageSizeId,
									data : $('#searchForm').serialize(),
									success : function(msg) {
										$("#reminder").html("");
										$("#CheckUserChooseTr").children().remove();
										if (msg.total != 0) {
											var temp = msg.list;
											for (var i = 0; i < temp.length; i++) {
												if (temp[i].secondClass != "" && temp[i].secondClass != null) {
													if(industryTypeCode !=null || industryTypeCode !=''){
														if(temp[i].fourthClass == industryTypeCode){
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'><input type='radio' class='tampredio'   name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
														}else{
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'><input type='radio' class='tampredio'  name='chosseIndustry'  value='"+temp[i].name+"#"+temp[i].firstCalss+"#"+temp[i].secondClass+"#"+temp[i].thirdClass+"#"+temp[i].fourthClass+"' > </td></tr>")
						
														}
													}
												}else{
													$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
															+ temp[i].firstCalss
															+ "</td><td class='text-center'>"
															+ temp[i].secondClass
															+ "</td><td class='text-center'>"
															+ temp[i].thirdClass
															+ "</td><td class='text-center'>"
															+ temp[i].fourthClass
															+ "</td><td class='text-center'>"
															+ temp[i].name
															+ "</td><td class='text-center'>"
															+ temp[i].industryDesc
															+ "</td><td class='text-center'></td></tr>")
												}
											}
										} else {
											$("#reminder").html("无符合条件的记录")
											 $('#pageCon').remove();
										}
										curentPage = msg.pageNum;
										totalPage = msg.pages;
									}
								}); 
							}
						}
					}
						$('#pageCon').bootstrapPaginator(optionsPage);
				}
			});
		}
		//模态窗的保存按钮点击事件
		$("#industryconfirm").click(function(){
			var temp = $(".tampredio:checked").val();
			//var temp = $("[name='chosseIndustry' .]").val();
			if (temp != null && temp != "" && temp != undefined) {
				var arr = temp.split("#");
				//取消全部行业复选框的选中
				var allId = '#'+'${all}';
				$(allId).removeAttr('checked');
				//由于拼接时将名称放在第一位所以直接取下标为0的数
				var nameId = '#'+'${name}';
				var codeId ='#'+'${code}';
					$(nameId).val(arr[0]);//给按行业查询文本框赋值(为用户提供显示功能)
 		  		    //alert(arr[0]+'------'+arr[1]+'---------'+arr[2]+'-------'+arr[3]+'-------'+arr[4]);
					//当小类不为空时 取小类的值
 		  		    	if(arr[4]!=null && arr[4]!= ''){
 	 		  		    	$(codeId).val(arr[4]);
 	 		  		    }else{
 	 		  		    	//若小类为空 则判断中类不为空
 	 		  		    	if(arr[3]!=null && arr[3]!= ''){
 	 		  		    		$(codeId).val(arr[3]);
 	 		  		   	 	}else{
 	 		  		   	 		//若中类为空则判断大类是否为空
 	 		  		   	 		if(arr[2]!=null && arr[2]!= ''){
	 		  		    			$(codeId).val(arr[2]);
	 		  		   	 		}else{
	 		  		   	 			//若大类为空 否则返回空
	 		  		   	 			$(codeId).val('');
	 		  		   	 		}
 	 		  		   	 	}
 	 		  		    }
			}else{
				var nameId = '#'+'${name}';
				var codeId ='#'+'${code}';
				$(codeId).val("");
				$(nameId).val("");
				
			}
		})
		
		

</script>
</body>

</html> 