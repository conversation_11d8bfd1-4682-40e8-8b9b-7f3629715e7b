<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script src="${webpath }/static/TaskManager/ReadOnlyBusinessFlow.js"></script>
</head>
<body>
<!--关联执法-->
<div class="main-container">
    <div class="padding-md">
        <div class="row">
            <!--任务办理-->
            <div class="col-lg-12">
                <div class="smart-widget widget-blue">
                    <div class="smart-widget-header font-16">
                        <i class="fa fa-arrow-right"></i> 关联执法
                        <span class="smart-widget-option">
                            <i class="fa fa-chevron-left"></i>
                            <a href="#" onclick="goBack('${preUrl}')" class="font-16">返回</a>
                        </span>
                    </div>
                    <div class="smart-widget">
                        <div class="form-horizontal" style="padding: 15px;">
                            <div class="form-group">
                                <label class="control-label col-lg-2"  >检查人</label>
                                <div class="col-lg-3">
                                    <input id="checUserNames" name="checUserNames"   type="text" placeholder="检查人" class="form-control" data-parsley-required="true">
                                </div>
                                <label class="control-label col-lg-2"  >执法编号</label>
                                <div class="col-lg-3">
                                    <input id="taskNumber" name="taskNumber"   type="text" placeholder="执法编号" class="form-control" data-parsley-required="true">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-lg-2">检查时间</label>
                                <div class="col-lg-3" style="padding:0;">
                                    <div class="col-lg-6">
                                        <input type="text" placeholder="开始日期" class="form-control" data-parsley-required="true" readonly="readonly"
                                               id="quesDateBegin" name="quesDateBegin">
                                    </div>
                                    <div class="col-lg-6">
                                        <input type="text" placeholder="结束日期" class="form-control" data-parsley-required="true" readonly="readonly"
                                               id="quesDateEnd" name="quesDateEnd">
                                    </div>
                                </div>
                                <div class="col-lg-5 text-right">
                                    <button class="btn btn-info" onclick="searchList()" name = "searchSubmit" type="button" style="width:120px;">查询</button>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-lg-12 text-right">
                                    <button onclick="openModal()" class="btn btn-info" style="margin-right: 10px;">关联</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="smart-widget-inner table-responsive">
                        <table class="table-no-bordered" id="swingTagManageTable">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 判断属实（Modal） -->
<div class="modal fade" id="openModal" tabindex="-1" role="dialog" aria-labelledby="openModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true">×
                </button>
                <h4 class="modal-title" id="myModalLabel">
                    投诉内容是否属实
                </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <input hidden id="idsArr">
                        <label class="col-lg-4 control-label">投诉内容是否属实</label>
                        <div class="col-lg-6">
                            <label class="checkbox-inline">
                                <input type="radio" name="isCon" value="属实"> 属实
                            </label>
                            <label class="checkbox-inline">
                                <input type="radio" name="isCon" value="不属实"> 不属实
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="saveIsLink()" class="btn btn-primary">
                    确定
                </button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">关闭
                </button>

            </div>
        </div>
    </div>
</div>

</body>

<script type="text/javascript">
    var webpath = '${webpath}';
    var lawObjectId = '${lawObjectId}';
    var lawID = '${lawID}';
    var taskNumberList = [];
    //监测日期
    var pageNum = '${pageNum}';
    //关联执法
    function linkLawModeler(){
        //console.log(lawObjectId);
        var options1= {
            remote:WEBPATH+'/taskGeneral/task-monitor-page?lawObjectId='+lawObjectId+"&lawID="+lawID
        };
        $('#linkLawModeler').modal(options1);
    }
    //关联案件
    function linkCaseModeler(){
        var options2= {
            remote:WEBPATH+'/ajtz/case-monitor-page?lawObjectId='+lawObjectId+"&lawID="+lawID
        };
        $('#linkCaseModeler').modal(options2);
    }

    //返回上一步主菜单
    function goBack(preUrl) {
        if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
            business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", null);
        } else {
            swal({
                title : "提示！",
                text : "返回信息错误，请刷新后重试。",
                type : "error",
                allowOutsideClick :true
            })
        }
        sessionStorage.clear();
    }
    $(document).ready(function(){
        $("[name='quesDateBegin']").datetimepicker({
            language:'cn',
            format:'yyyy-mm-dd',
            autoclose: true,
            todayBtn: true,
            clearBtn: true,
            minView:'month',
            maxView:'decade'
        });
        $("[name='quesDateEnd']").datetimepicker({
            language:'cn',
            format:'yyyy-mm-dd',
            autoclose: true,
            todayBtn: true,
            clearBtn: true,
            minView:'month',
            maxView:'decade'
        });
        LoadingSwingTagManageItems();

    })
    function searchList() {
        $('#swingTagManageTable').bootstrapTable('refreshOptions',{pageNum:1,pageSize:10});
    }
    function LoadingSwingTagManageItems() {
        var ajid = sessionStorage.getItem('Ajid');
        $('#swingTagManageTable').bootstrapTable({
            method : 'post',
            dataType : "json",
            //url : webpath + '/swingtagmanage/swingtagmanageList',
            url : webpath + '/xfzf/linkLawlist/?Ajid='+ajid,
            undefinedText : '-',
            pagination : true, // 分页
            striped : true, // 是否显示行间隔色
            queryParamsType : "",
            locale : 'zh-CN',
            pageSize : 10, // 设置默认分页为 15
            pageNumber : 1,
            clickToSelect : true,
            pageList : [10, 20, 30, 50 ], // 自定义分页列表
            contentType : "application/x-www-form-urlencoded",
            // showColumns : true, // 显示隐藏列
            sidePagination : "server", //服务端请求
            queryParams:queryParams,//参数
            uniqueId : "id", // 每一行的唯一标识
            columns : [ {
                field : "taskNumber",
                title : "执法编号",
                align : 'center',
                formatter:function(value,row,index){
                    var str = "<a href='#' onclick=\"zfDetail('"+row.id+"')\" style='color:#23b7e5;'>"+row.taskNumber+"</a>";
                    return str;
                }
            }, {
                field : "lawObjectName",
                title : "执法对象名称",
                align : 'center',
            },  {
                field : "handlingUnitName",
                title : "执法部门",
                align : 'center',
            },  {
                field : "taskLawobjectStatus",
                title : "执法类型",
                align : 'center',
            }, {
                field : "taskFromName",
                title : "执法来源",
                align : 'center',
            },{
                field : "checUserNames",
                title : "检查人",
                align : 'center',
            },{
                field : "checkStartDate",
                title : "检查日期",
                align : 'center',
                formatter: function (value, row, index) {
                    if(value==null){
                        return "";
                    }else {
                        var date = new Date(value);
                        var y = date.getFullYear();
                        var m = date.getMonth() + 1;
                        var d = date.getDate();
                        return y + '-' +m + '-' + d;
                    }
                }
            }, {
                checkbox: true,
                title: "操作",
                align: 'center'
            } ],
            responseHandler : function(res) {
                return {
                    total : res.data.total,
                    rows : res.data.list
                };
            },
            onCheck : function(row, $element) {
            },//单击row事件
            onUncheck : function(row, $element) {

            },
            onUncheckAll : function(row, $element) {

            },
            onCheckAll : function(row, $element) {

            },
            onRefresh : function() {

            },
            formatLoadingMessage : function() {
                return "请稍等，正在加载中...";
            },
            formatNoMatches : function() { //没有匹配的结果
                return '无符合条件的记录';
            }
        });
    }
    function queryParams(params) {
        var ajid = sessionStorage.getItem('Ajid');
        var taskNumber =$("#taskNumber").val();
        var checUserNames =$("#checUserNames").val();
        var quesDateBegin =$("#quesDateBegin").val();
        var quesDateEnd = $("#quesDateEnd").val();
        var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            id:ajid,
            taskNumber:taskNumber,
            checUserNames:checUserNames,
            startime:quesDateBegin,
            endtime:quesDateEnd,
        };
        return temp;
    }
    function zfDetail(id) {
        var obj={taskId:id,lawObjectType:'1',parentUrl:'0',nodeCode:'0'};
        business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType=0',obj);
        //business.addMainContentParserHtml(WEBPATH+'/xfzf/Details',null);
    }
    function openModal() {
        var taskList= $("#swingTagManageTable").bootstrapTable('getSelections');
        var ids = [];
        if(taskList){
            $.each(taskList,function(i,item){
                ids.push(taskList[i].id+':'+taskList[i].taskNumber)
            });
        }
        if(taskList.length == 0){
            swal({title:'提示',text: '请勾选您要关联的执法对象',type:'info',allowOutsideClick :true});
            return;
        }
        $('#openModal').modal('show');
        $("#idsArr").val(ids);
    }
    function saveIsLink(){
        if(!$(':radio[name=isCon]:checked').length) {
            swal({title:'提示',text: '请选择投诉内容是否属实',type:'info',allowOutsideClick :true});
        }else {
            var iscode = $('input[name=isCon]:checked').val();
            $('#openModal').modal('hide');
            var data = {
                isrelation: iscode == '属实' ? 1 : 0,
                ajid: sessionStorage.getItem('Ajid'),
                taskidarr: $("#idsArr").val(),
                insert: "1"
            };
            $('#openModal').on('hidden.bs.modal', function () {
                $(this).removeData("bs.modal");
                $.ajax({
                    type:"POST",
                    url:webpath+"/xfzf/wglinkRelation",
                    data: JSON.stringify(data),
                    //data:JSON.parse(data),
                    dataType:"json",  //返回的数据类型
                    //contentType:"application/json;charset=utf-8",
                    success:function(data){
                        if(data.meta.statusCode == '200'){
                            business.addMainContentParserHtml(WEBPATH+'/xfzf/wgOne?tsdxmc=${tsdxmc}',null);
                            sessionStorage.setItem('Ajid',sessionStorage.getItem('Ajid'));
                        }else{
                            swal({title:"加载失败", text:data.meta.message, type:"error",allowOutsideClick :true});
                        }
                    }
                })
            })

        }
    }

    function startCase(lawObjectId){
        $.ajax({
            data:{lawID:lawID,sign:'2'},
            dataType:"json",
            type:"POST",
            url:webpath+"/overlaw/judgeCaseTask",
            success:function(data){
                if(data.code == '500'){
                    swal({title:'提示',text:data.message,type:'info',allowOutsideClick :true});
                }else{
                    //macroMgr.onLevelTwoMenuClick(null, 'overlaw/startCase?lawObjectId='+lawObjectId+'&selectType=1&parentUrl=1');
                    business.addMainContentParserHtml(WEBPATH+'/overlaw/startCase?lawObjectId='+lawObjectId+'&selectType=1&parentUrl=1',null);
                }
            }
        })
    }
    function linkToTask(taskId){
        var obj={taskId:taskId,lawObjectType:'1',parentUrl:'0',nodeCode:'0'};
        business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType=0',obj);
    }
</script>
</html>