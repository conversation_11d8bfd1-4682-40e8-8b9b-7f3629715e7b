<%@ page language="java" contentType="text/html; charset=utf-8"
		 pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec"
		   uri="http://www.springframework.org/security/tags"%>
<sec:authentication property="principal" var="authentication" />
<script
		src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"
		type="text/javascript"></script>
<div class="main-container">
	<div class="padding-md">

		<div class="smart-widget">
			<div class="smart-widget-inner">
				<jsp:include page="../caseBusiness.jsp"></jsp:include>
				<div class="row">

					<div class="col-lg-9">
						<div class="tab-content" style="margin-top: -20px;">
							<div class="tab-pane fade in active" id="style1Tab1">
								<div class="smart-widget-inner table-responsive">
									<div class="smart-widget-body form-horizontal">
										<div style="padding: 5px 0;">
											<span
													style="font-size: 16px; font-weight: bold; color: #666;">一般行政处罚案件编号：</span><span
												style="font-size: 16px; font-weight: bold; color: #666;">${generalCaseInfo.easySanctionNumber }</span>
										</div>
										<!--start-->
										<input type="hidden" id="id" name="id" value="${generalCaseInfo.id }"/>
										<legend class="font-16"
												style="font-weight: bold; color: #23b7e5;">撤案情况</legend>
										<div style="float: right; margin: -55px 0 0 100px;">
											<c:if test="${caseStateInfo.parentUrl=='2' && not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
												<button class="btn btn-danger btn-sm" onclick="deleteById()">删除</button>
											</c:if>
											<c:if test="${caseStateInfo.parentUrl eq 0 and caseStateInfo.isPowerEdit==1 and not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
												<c:if test="${generalCaseInfo.upState < 3 and empty generalCaseInfo.punishStartDate}">
													<!-- 如果没有提交，就可以删除 -->
													<button class="btn btn-danger btn-sm" onclick="deleteById()">删除</button>
												</c:if>
											</c:if>
											<c:if test="${caseStateInfo.parentUrl=='2' && empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">新增</button>
											</c:if>
										</div>
										<form id="backoutForm" name="backoutForm">
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">本环节开始办理时间</label>
												<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;" readonly="readonly">
													<span><fmt:formatDate value='${generalCaseInfo.backoutCaseStartDate }' type='date' pattern='yyyy-MM-dd'/></span>
												</div>
											</div>

											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">撤案时间</label>
												<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;" readonly="readonly">
													<span><fmt:formatDate value='${generalCaseInfo.backoutCaseDate }' type='date' pattern='yyyy-MM-dd'/></span>
												</div>
											</div>
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">
													撤案理由</label>
												<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;" readonly="readonly">
													<span>${generalCaseInfo.backoutCaseReasons }</span>
												</div>
											</div>
											<div class="form-group">
												<label for=""
													   class="col-lg-3 col-sm-3 col-xs-5 control-label">撤案审批表</label>
												<div class="col-sm-5 col-xs-12" style="margin-top: 7px;" readonly="readonly">
													<div id="fjck601"></div>
												</div>
											</div>

										</form>
									</div>
									<!--end-->
								</div>
							</div>

						</div>
					</div>
					<!--时间轴-->
					<jsp:include page="./timeAxis.jsp"></jsp:include>
				</div>
			</div>
		</div>

	</div>
</div>
<!-- 附件预览  -->
<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
	 aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">

		</div>
	</div>
</div>

<!--  附件查看 -->
<div class="modal fade" id="view" tabindex="-1" role="dialog"
	 aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content"></div>
	</div>
</div>

<script type="text/javascript">
    $(function(){
        // hisShowFiles("atv_sanction_case","${generalCaseInfo.id}","一般行政处罚-撤案审批表","6");
        hisShowFiles("${caseStateInfo.caseId }",null,601);
        showGeneFile("atv_sanction_case","${generalCaseInfo.id}","fjckybcf1","一般处罚部分案卷");
    })
    //编辑
    function editCaseInfo(){
        swal({
            title: "请确认环保部已同意该案件信息进行修改，否则会造成本地数据污染无法更新环保部情况。",
            type: "warning",
            showCancelButton: true,
            closeOnConfirm: true,
            confirmButtonText: "确认，我要修改",
            confirmButtonColor: "#ec6c62"
        }, function() {
            //跳转到编辑页面
            business.addMainContentParserHtml(WEBPATH + '/generalCase/toCheAnPage?selectType=3&editType=1', $("#caseStateObjectForm").serialize());
        });
    }

    //删除
</script>