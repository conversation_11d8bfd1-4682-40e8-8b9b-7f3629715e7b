<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<c:set var="webpath">
${pageContext.request.contextPath}
</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
	String current_version=PropertiesHandlerUtil.getValue("current_version","version");
	String current_version_date=PropertiesHandlerUtil.getValue("current_version_date","version");
	String snsServer=PropertiesHandlerUtil.getValue("outerSnsServer","sns_server");
	String downloadAppUrl=PropertiesHandlerUtil.getValue("downloadAppUrl","version");
%>
<c:set var="FASTDFS_ADDR">
<%=fastdfs_addr%>
</c:set>
<c:set var="CURRENT_VERSION">
<%=current_version%>
</c:set>
<c:set var="CURRENT_VERSION_DATE">
<%=current_version_date%>
</c:set>
<c:set var="snsServer">
<%=snsServer%>
</c:set>
<c:set var="downloadAppUrl">
<%=downloadAppUrl%>
</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>福建省生态云环境执法平台</title>
<!-- <title>污染源精细化监管系统</title> -->
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<script type="text/javascript">
	var WEBPATH = '${webpath}';
	var FASTDFS_ADDR='${FASTDFS_ADDR}';
	var snsServer = '${snsServer}';
</script>
<link rel="shortcut icon" href="${webpath }/static/img/zhn_favicon.ico" />
<!-- Bootstrap core CSS -->
<link rel="stylesheet" type="text/css" href="${webpath }/static/libs/bootstrap/3.3.4/css/bootstrap.css">
<%-- <link rel="stylesheet" type="text/css" href="${webpath }/static/css/bootstrap.min.css"> --%>
<!--<link rel="stylesheet" type="text/css" href="${webpath }/static/jquery/bootstrap-3.3.4.css">-->
<!-- Font Awesome -->
<link rel="stylesheet" type="text/css" href="${webpath }/static/font-awesome/4.7.0/css/font-awesome.min.css">
<!-- Morris -->
<link href="${webpath }/static/css/morris.css" rel="stylesheet" />
<!-- bootstrapValidator -->
<link href="${webpath }/static/css/formValidation.min.css" rel="stylesheet" type="text/css"/>
<!-- Animate -->
<link href="${webpath }/static/css/animate.min.css" rel="stylesheet">
<link href="${webpath }/static/css/bootstrap-table.css" rel="stylesheet">
<!-- Owl Carousel -->
<link href="${webpath }/static/css/owl.carousel.min.css" rel="stylesheet">
<link href="${webpath }/static/css/owl.theme.default.min.css" rel="stylesheet">
<!-- sweetalert  -->
<link href="${webpath }/static/css/sweetalert.css" rel="stylesheet">
<!-- Simplify -->
<link href="${webpath }/static/css/simplify.min.css" rel="stylesheet">
<!-- 上传附件 CSS -->
<link href="${webpath }/static/bootstrap-fileinput-4.3.9/css/fileinput.css" rel="stylesheet" type="text/css" />
<link href="${webpath }/static/bootstrap-fileinput-4.3.9/themes/explorer/theme.css" rel="stylesheet" type="text/css" />
<link href="${webpath }/static/css/style.css" rel="stylesheet">
<!-- 遮罩层 -->
<link href="${webpath }/static/css/loding.css" rel="stylesheet">

<!-- bootstrap-datetimepicker.css -->
<link href="${webpath }/static/css/bootstrap-datetimepicker.css" rel="stylesheet" />
<!--select2-bootstrap.css  -->
<!-- 联想搜索 -->
<link href="${webpath }/static/css/select2.css" rel="stylesheet"  type="text/css"/>
<link href="${webpath }/static/css/select2-bootstrap.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="${webpath }/static/css/iconfont.css">

<!-- Custom CSS -->
<link href="${webpath }/static/dist/css/sb-admin-2.css" rel="stylesheet">

<!-- bootstraptable固定行和列 -->
<link href="${webpath }/static/css/bootstrap-table-fixed-columns.css" rel="stylesheet">

<!-- 时间轴 CSS -->
<link href="${webpath }/static/css/time/css.css" rel="stylesheet" type="text/css" />

<!-- 拖拽Css -->
<link href="${webpath }/static/css/Sortable_app.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=YKzcYZgo2WQNgtrAg4yoRcxZxEbdG6Dh"></script>
<script src="${webpath }/static/jquery/2.1.1/jquery.min.js"></script>
<script src="${webpath }/static/js/vue.min.js"></script>
<script type="text/javascript" src="${webpath }/static/js/formValidation.min.js"></script>
<script src="${webpath }/static/js/bootstrap-table.min.js"></script>
<script src="${webpath }/static/js/bootstrap-table-zh-CN.js"></script>
<%-- <script type="text/javascript" src="${webpath }/static/businessJs/sysHome/sysMain.js"></script> --%>
<script type="text/javascript" src ='${webpath }/static/js/qrcode.min.js'></script>
<script type="text/javascript" src ='${webpath }/static/js/jquery.qrcode.min.js'></script>
<script type="text/javascript" src ='${webpath }/static/js/utf16toutf8.js'></script>
<script src="${webpath }/static/TaskManager/ChnBase64.js"  type="text/javascript"></script>
<script type="text/javascript" src="${webpath }/static/jquery/bootstrap-typeahead.js"></script>
<style type="text/css">
.tab_item {
	position: absolute;
	left: 33%;
    display: inline-block;
}
.tab_item >a {
	cursor: pointer;
	width: 113px;
	display: inline-block;
	text-align: center;
	/* margin: 0 5%; */
	padding-top: 4px;
}
.tab_item >a>i {
	font-size: 14px;
	color: #23b7e5;
}
.tab_item a>p {
	font-size: 14px;
	color: #23b7e5;
	font-weight: bold;
}
.tab_item >a:hover {
	background: #23b7e5;
}
.tab_item >a:hover i {
	color: #ffffff;
}
.tab_item >a:hover p {
	color: #ffffff;
}
.panel-body {
	padding:0;
}
/*.top-nav-inner {
	overflow: hidden;
}
.nav-container {
	margin-left: 0px;
}*/
</style>
<script type="text/javascript">
	var isIEWhether=false;	
	$(document).ready(function(){
		isIEWhether=isIE();
	})
	
	function isIE() { //ie?
		 if (!!window.ActiveXObject || "ActiveXObject" in window){
			 return true;
		 }else{
			 return false;
		 }
	}

</script>
</head>
<body class="overflow-hidden" data-spy="scroll" data-target="#myScrollspy" data-offset="20">
<div class="wrapper preload"> 
  <!--框架头 开始-->
  <header class="top-nav">
    <div class="top-nav-inner"> 
      <!--手机端呼出左侧菜单-->
      <div class="nav-header">
        <button type="button"
                        class="navbar-toggle pull-left sidebar-toggle"
                        id="sidebarToggleSM"> <span class="icon-bar"></span> <span class="icon-bar"></span> <span
                            class="icon-bar"></span> </button>
        <!--手机端个人信息-->
        <ul class="nav-notification pull-right">
          <li><a href="#" class="dropdown-toggle"
                            data-toggle="dropdown"><i class="fa fa-cog fa-lg"></i></a> <span
                            class="badge badge-danger bounceIn">1</span>
            <ul class="dropdown-menu dropdown-sm pull-right user-dropdown">
              <li class="user-avatar"><img
                                    src="${webpath }/static/img/mep.png" alt=""
                                    class="img-circle">
                <div class="user-content">
                  <h5 class="no-m-bottom">管理员</h5>
                  <div class="m-top-xs"> <a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'grkj/userInfo')" class="m-right-sm">修改个人信息</a> <a
                                                href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sysUser/toPassword')" class="m-right-sm">修改密码</a> <a onclick ="logout()" href="#">退出系统</a> </div>
                </div>
              </li>
              <li><a href="#"> 我的邮件 <span
                                        class="badge badge-danger bounceIn animation-delay2 pull-right">0</span> </a></li>
              <li><a href="#"> 站内信息 <span
                                        class="badge badge-purple bounceIn animation-delay3 pull-right">0</span> </a></li>
              <li class="divider"></li>
              <li><a href="#">设置</a></li>
            </ul>
          </li>
        </ul>
        <!--logo 系统首页--> 
        <a> <span style="position: absolute;left: 10px; width: 400px;"><img src="${webpath }/static/img/logo.png"
                            style="margin: 5px 0 0 0;" /></span> </a> </div>
      <!--./手机端呼出左侧菜单-->
      <%--<div class="tab_item">--%>
         <%--<a onclick="macroMgr.onLevelTwoMenuClick('4AE820E38F176585E055000000000001', 'sysIndex/indexHome')" href="javascript:;"> <i class="icon iconfont icon-gongping"></i>--%>
            <%--<p>执法监管</p>--%>
            <%--</a> --%>
             <%--<a onclick="toWgYzt();" href="javascript:;"> <i class="icon iconfont icon-diqiu"></i>--%>
            <%--<p>网格一张图</p>--%>
            <%--</a> --%>
             <%--<a onclick="toWgRdzs();"  href="javascript:;"> <i class="icon iconfont icon-jianguan"></i>--%>
            <%--<p>热点指数</p>--%>
            <%--</a> --%>
             <%--<a onclick="toWgWgjg();" href="javascript:;"> <i class="icon iconfont icon-dangan"></i>--%>
            <%--<p>网格监管</p>--%>
            <%--</a> --%>
      <%--</div>--%>
      <sec:authentication property="principal" var="authentication"/>
      <!-- 框架top -->
      <div class="nav-container">
        <div class="pull-right m-right-sm"> 
          <!-- <span style="color:red; position:absolute; left:230px; top:12px; font-size:18px;">（测试环境）</span> --> 
          <!--用户信息-->
          <div class="user-block hidden-xs"> 
            <!--  <span  onclick="toWgYzt();"     >
                       一张图
                       </span>
                       <span  onclick="toWgRdzs();"     >
                        热点指数
                       </span>
                       <span  onclick="toWgWgjg();"     >
                       网格监管
                       </span>macroMgr.onLevelTwoMenuClick('4AE820E38F176585E055000000000001', 'sysIndex/indexHome')
                         --> 
            
            <span style="margin: 0 10px;"><img
									src="${webpath }/static/img/app+.png"
									style="width: 23px; height: 28px; margin: 0 5px; border: 0; cursor: pointer;"
									id="folder1" data-container="body" data-toggle="popover"
									data-placement="bottom" data-html="true"
									data-content="<div id='appQrCode' style=' width:200px; height:200px;'><div>" > </span> <a href="#" id="userToggle" data-toggle="dropdown">
            <%--  	<c:choose>
                            	 <c:when test="${ authentication.isDefaultAvatar !=1}">
                            	 	 <c:if test="${avarUrl!=null&&avarUrl!=''}">  
                            	  		<img style='width:30px;height:30px;border-radius:15px 15px 15px 15px;' src='${FASTDFS_ADDR}${avarUrl}' class='file-preview-image'>
								  	 </c:if>
                            	 </c:when>
                            	 <c:when test="${authentication.isDefaultAvatar =1}">
                            	 	   <c:when test="${authentication.defaultAvatarUrl!=null&&authentication.defaultAvatarUrl!=''}">  
                            	  		<img style='width:30px;height:30px;border-radius:15px 15px 15px 15px;' src='${webpath }/static/img/${authentication.defaultAvatarUrl }' class='file-preview-image'>
								  	 </c:when> 
                            	 </c:when>
                            	
								   <c:otherwise> 
								   <i class="fa fa-user-circle fa-lg"></i>
								   </c:otherwise>
                            	</c:choose>  --%>
            <c:choose>
              <c:when test='${(authentication.isDefaultAvatar == 0 or authentication.isDefaultAvatar==null)  and not empty authentication.avatarUrl}'> <img style='width:30px;height:30px;border-radius:15px 15px 15px 15px;' src='${FASTDFS_ADDR}${authentication.avatarUrl}' class='file-preview-image'> </c:when>
              <c:when test='${authentication.isDefaultAvatar == 1   and not empty authentication.defaultAvatarUrl}'> <img style='width:30px;height:30px;border-radius:15px 15px 15px 15px;' src='${webpath }/static/img/${authentication.defaultAvatarUrl }' class='file-preview-image'> </c:when>
              <c:otherwise> <i class="fa fa-user-circle fa-lg"></i> </c:otherwise>
            </c:choose>
            <div class="user-detail inline-block"> ${authentication.username}您好!<i class="fa fa-angle-down"></i> </div>
            </a>
            <div class="panel border dropdown-menu user-panel">
              <div class="panel-body paddingTB-sm">
                <ul>
                  <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'grkj/userInfo')"> <i
                                                class="fa fa-edit fa-lg"></i><span class="m-left-xs" style="margin-left:5px;">修改个人信息</span> </a></li>
                  <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sysUser/toPassword')"> <i
                                                class="fa fa-unlock-alt fa-lg"></i><span class="m-left-xs" style="margin-left:12px;">修改密码</span> </a></li>
                  <li><a href="#" onclick ="logout()"> <i
                                                class="fa fa-power-off fa-lg"></i><span class="m-left-xs" style="margin-left:8px;">退出系统</span> </a></li>
                </ul>
              </div>
            </div>
          </div>
          <ul class="nav-notification">
            <li> <a href="#"  onclick="myQuestions()"   title="问题反馈"><i class="fa fa-question-circle fa-lg"></i></a> 
              <!-- <span class="badge badge-info bounceIn animation-delay6 active"></span> --> 
            </li>
            <!--站内信息-->
            <li> <a href="#" data-toggle="dropdown"><i class="fa fa-envelope fa-lg"></i></a> 
              <!--  <span class="badge badge-purple bounceIn animation-delay5 active"></span>  --> 
            </li>
            <!--信息提醒-->
            <li> <a href="#" data-toggle="dropdown" onclick="getNewMessage()"><i class="fa fa-bell fa-lg"></i></a> 
              <!--  <span class="badge badge-info bounceIn animation-delay6 active"></span>  --> 
              <span class="badge badge-info bounceIn animation-delay6 active" id="messageCount"></span>
              <ul class="dropdown-menu notification dropdown-3 pull-right" id="newMessages">
                <%-- <c:forEach items="${newMessage }" var="obj" varStatus="status">
									      <li><a href="systeminfo-list.html">你有4个新的系统消息</a></li>	<li>
											<a href="xczf/xtxx-zf-view.html">
												<!--<span class="notification-icon bg-success">
													<i class="fa fa-comment-o"></i>
												</span>-->
												<span class="m-left-xs">XXX给您分配了一个执法任务</span>
												<span class="time text-muted">2分钟前</span>
											</a>
										</li>
									  </c:forEach> --%>
                
                <!-- <li>
											<a href="ajxt/xtxx-zf-jbxx-view.html">
												<span class="notification-icon bg-success">
													<i class="fa fa-comment-o"></i>
												</span>
												<span class="m-left-xs">您的待办执法XXXXXX距离限办...</span>
												<span class="time text-muted">5分钟前</span>
											</a>
										</li>
										<li>
											<a href="ajxt/xtxx-aj-jbxx-view.html">
												<span class="notification-icon bg-success">
													<i class="fa fa-comment-o"></i>
												</span>
												<span class="m-left-xs">您的案件XXXXXX需要在5天内...</span>
												<span class="time text-muted">8分钟前</span>
											</a>
										</li>
										<li>
											<a href="ajxt/xtxx-aj-cf-view.html">
												<span class="notification-icon bg-success">
													<i class="fa fa-comment-o"></i>
												</span>
												<span class="m-left-xs">您的案件XXXXXX需要在3天内...</span>
												<span class="time text-muted">10分钟前</span>
											</a>
										</li> -->
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </header>
  <!--框架头 结束--> 
  
  <!--当前位置 开始--> 
  <!--<div class="center_home_weizhi">当前位置：系统首页</div>--> 
  <!--当前位置 结束--> 
  
  <!-- 页面中部~中间 start  -->
  
  <aside id = "sslideleft" class="sidebar-menu fixed">
    <div class="sidebar-inner scrollable-sidebar">
      <div class="main-menu">
        <ul class="accordion">
          <li class="menu-header">Main Menu</li>
          <c:forEach items="${menu}" var="menu" varStatus="status"> 
            <!--一级子菜单没有parentId,有url ，即没有任何子菜单的菜单 -->
            <c:if test="${empty menu.parent and not empty menu.moduleUrl}">
              <li class="bg-palette2" id ='${menu.elementId}'>
                <div class="padding-sm text-center" id="" style="cursor:pointer;"> <i class="fa fa-bars fa-lg" style="color:#FFF;"></i> 
                  
                  <!-- <i class="fa fa-bars fa-lg" id="sidebarToggleLG" style="color:#585858;"></i> --> 
                </div>
                <a  id ='${menu.elementId}-a' onclick="macroMgr.onLevelTwoMenuClick('${menu.id}', '${menu.moduleUrl}')" style="cursor:pointer;"> <span class="menu-content block"> <span class="menu-icon"><i class="${menu.iconUrl}"></i></span> <span class="text m-left-sm">${menu.moduleName} </span> </span> <span class="menu-content-hover block">${menu.moduleName}</span> </a> </li>
            </c:if>
            <!-- 可展开的一级菜单，没有parentId,没有有url -->
            <c:if test="${empty menu.parent and empty menu.moduleUrl}">
              <li class="openable bg-palette2 tagOneClass"  id ='${menu.elementId}'> <a href="#" style="cursor:pointer;"> <span class="menu-content block"> <span class="menu-icon"><i class="${menu.iconUrl}"></i></span> <span class="text m-left-sm">${menu.moduleName}</span> <span class="submenu-icon"></span> </span> <span class="menu-content-hover block">${menu.moduleName}</span> </a>
                <ul class="submenu tagOneClassUl">
                  <!-- 没有url的是三级菜单，有url的直接输出到li中 -->
                  <c:forEach items="${menu.children}" var="secondChild" varStatus="status">
                    <c:if test="${not empty secondChild.moduleUrl }">
                      <li  id ='${secondChild.elementId}' ><a href="#" onclick="macroMgr.onLevelTwoMenuClick('${secondChild.id}', '${secondChild.moduleUrl}')"  style="cursor:pointer;"><span
																	class="submenu-label">${secondChild.moduleName}</span></a></li>
                    </c:if>
                    <!-- 二级菜单url为空，表示还有三级菜单 -->
                    <c:if test="${empty secondChild.moduleUrl }">
                      <li class="openable" id ='${secondChild.elementId}'> <a href="#"  style="cursor:pointer;"> <small class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">${fn:length(secondChild.children)}</small> <span class="submenu-label">${secondChild.moduleName}</span> </a>
                        <ul class="submenu third-level tagThreeClass">
                          <c:forEach items="${secondChild.children}" var="thirdChild" varStatus="status">
                            <li id ='${thirdChild.elementId}'> <a onclick="macroMgr.onLevelTwoMenuClick('${thirdChild.id}', '${thirdChild.moduleUrl}')" style="cursor:pointer;"><span class="submenu-label">${thirdChild.moduleName }</span></a> </li>
                            <%-- <li>
										                                           <a href="<c:url value='${thirdChild.moduleUrl }'/>"></a>
										                                       	</li> --%>
                          </c:forEach>
                        </ul>
                      </li>
                    </c:if>
                  </c:forEach>
                </ul>
              </li>
            </c:if>
          </c:forEach>
        </ul>
      </div>
    </div>
  </aside>
  <div id="mask">
    <div class="spinner">
      <div class="spinner-container container1">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
      <div class="spinner-container container2">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
      <div class="spinner-container container3">
        <div class="circle1"></div>
        <div class="circle2"></div>
        <div class="circle3"></div>
        <div class="circle4"></div>
      </div>
    </div>
  </div>
  <div id="main_content"> 
    <!-- 右侧信息内容 --> 
    <!-- 右侧信息内容 --> 
  </div>
  
  <!--框架底部版权 开始-->
  <%--<footer class="footer"> <span style="font-size:16px;"> <strong>福建省生态环境厅</strong> </span>
    <p class="no-margin"> &copy; 2017 <strong>北京长能环境大数据科技有限公司</strong> 研发并提供技术支持 <span style="padding:0 10px;">技术支持：010-88067626</span><strong style="margin-left:10px;">推荐谷歌及IE11浏览器</strong> <span style="margin:0 10px;font-size:14px;">最近更新日期：${CURRENT_VERSION_DATE}</span><span><a href="#" data-toggle="modal" data-remote="${webpath}/sysUser/toUpdateRecordPage" data-target="#updateRecord"  style="color:#23b7e5;font-size:14px;"><i class="fa fa-pencil"></i>更新记录</a></span> </p>
  </footer>--%>
  <!--框架底部版权 结束--> 
</div>
<!-- 百度地图api -->
<div class="modal fade" id="updateRecord" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content"> </div>
  </div>
</div>
<!-- 修改密码模态框 -->
<div class="modal fade" id="resetPwd" tabindex="-1" role="dialog" data-remote="${webpath }/auth/toResetPwd"
            aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog">
    <div class="modal-content"> </div>
  </div>
</div>
<!-- 消息详情模态框 -->
<div class="modal fade" id="xxxq" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog" style="width: 50%; font-size: 14px;">
    <div class="modal-content"></div>
  </div>
</div>
<script type="text/javascript">
	$(function () {


		$('#folder1').on('shown.bs.popover', function () {
			$('#appQrCode').empty();
			$('#appQrCode').qrcode({
				render : 'canvas',
				text : "${downloadAppUrl}",
				height : 200,
				width : 200,
			});
			});
		
	});
	function toWgYzt(){
		window.open("http://************:11027/zhjg/auth/goHome?openPageType=toYzt");
	}
	function toWgRdzs(){
		window.open("http://************:11027/zhjg/auth/goHome?openPageType=toRdzs");
	}
	function toWgWgjg(){
		window.open("http://************:11027/zhjg/auth/goHome?openPageType=toWgjg");
	}
		$(".submenu-label.menu").click(function() {
			$(".submenu-label.menu").css("color", "#A6DAF0");
			this.style.color = "white";
		})
		//退出系统
		function logout(){
			$.ajax({
				type : "get",
				dataType:'jsonp',
				async:false,
				url :snsServer+'/?/account/ajax/app_logout/',
				error : function(request) {
					window.location.href="/logout";
				},
				success : function(item) {
					window.location.href="/logout";
				}
			})
		}
		
		// 当点击系统首页的时候 
		$(function() {
			//监听回退键
			business.listenBackSpace();
			
			$("#element-one-xtsy-a").click(function() {
				ClickTag()
			})
			
			// 获取所有的项
			// 获取所有的项
			var spans = document.querySelectorAll(".submenu-label");
			var sslideleft = document.getElementById("sslideleft");
			for (i = 0; i < spans.length; i++) {//遍历处理，对于每个块都有onclick函数

				spans[i].onclick = function() {

					for (j = 0; j < spans.length; j++) {//在点击事件中再加载一个遍历，当点击事件触发时，先让其他元素的颜色保持不变

						spans[j].style.color = "#A6DAF0";
					}
					this.style.color = "white";//要点击的事件块发生颜色变化，同时上一步使得其他的块颜色保持不变，这就让上一次点击变化<br>//的颜色恢复到原来的颜色

					//隐藏侧边栏 显示
					if (sslideleft.className != null
							&& sslideleft.className.indexOf(' sidebar-mini') > -1) {
						//连空格一起替换
						sslideleft.className = sslideleft.className.replace(
								' sidebar-mini', '');
					}
					var twoparent = this.parentNode.parentNode.parentNode;
					var threeparent = this.parentNode.parentNode.parentNode.parentNode.parentNode;
					twoparent.style.display = "block";
					threeparent.style.display = "block";
				}
			
			}
		});
		function GetQueryString(name)
		{
		     var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		     var r = window.location.search.substr(1).match(reg);
		     if(r!=null)return  unescape(r[2]); return null;
		}
		$(document).ready(function(){
			  //绑定主菜单单击方法，设置样式
			  $("#sslideleft li").bind("click",function(e){
                  sessionStorage.clear();
			      if ( e && e.stopPropagation ){
			            e.stopPropagation();
				    } else{
				            window.event.cancelBubble = true;
				    }
			      $("#sslideleft li").removeClass("active");
			      $(this).addClass("active");
			  });
			  //若是点击案件上的裁量工具打开新页面则进行如下操作
			  if(GetQueryString("clgj") == 'yes'){
				  setTimeout(function() {
				// 1.关闭所有菜单 
			        ClickTag();
			         // 2.触发加载信息
					macroMgr.onLevelTwoMenuClick(null, 'discretionaryTool/discretionaryToolMain')
			         // 3.指定打开标签 
			        openClickTag("element-one-xtsy","element-one-zycl","element-two-zyclgj",'');
				  },500)
			  }
			  //一园一档跳转监管对象
			  if(GetQueryString("yyydjgdx") == 'yes'){
				  var id = GetQueryString("id");
				  var code = GetQueryString("typeCode");
				  setTimeout(function() {
			        ClickTag();
					macroMgr.onLevelTwoMenuClick(null, 'zfdx/detailedInformationObject?id='+id+'&typeCode='+code);
			        openClickTag("element-one-xtsy","element-one-gyjjq","element-two-yyyd",'');
				  },500)
			  }
			  //一园一档跳转执法
			  if(GetQueryString("yyydhjzf") == 'yes'){
				  var taskId = GetQueryString("taskId");
				  var lawObjectType = GetQueryString("lawObjectType");
				  var nodeCode = GetQueryString("nodeCode");
				  setTimeout(function() {
			        ClickTag();
					macroMgr.onLevelTwoMenuClick(null, 'taskManager/xczf?selectType=0'+'&taskId='+taskId+'&lawObjectType='+lawObjectType +'&parentUrl=0'+'&nodeCode='+nodeCode);
			        openClickTag("element-one-xtsy","element-one-gyjjq","element-two-yyyd",'');
				  },500)
			  }
			  //一园一档跳转监管案件
			  if(GetQueryString("yyydajck") == 'yes'){
				  var caseId = GetQueryString("caseId");
				  setTimeout(function() {
			        ClickTag();
					macroMgr.onLevelTwoMenuClick(null, 'caseInfo/baseInfoCasePage?caseId='+caseId+"&parentUrl=3&selectType=1");
			        openClickTag("element-one-xtsy","element-one-gyjjq","element-two-yyyd",'');
				  },500)
			  }
			  //专项台账跳转环境执法
			  if(GetQueryString("zxtzhjzf") == 'yes'){
				  var taskId = GetQueryString("taskId");
				  var lawObjectType = GetQueryString("lawObjectType");
				  var nodeCode = GetQueryString("nodeCode");
				  setTimeout(function() {
			        ClickTag();
					macroMgr.onLevelTwoMenuClick(null, 'taskManager/xczf?selectType=0'+'&taskId='+taskId+'&lawObjectType='+lawObjectType +'&parentUrl=0'+'&nodeCode='+nodeCode);
			        openClickTag("element-one-xtsy","element-one-jcbl","element-two-rwgl",'element-three-xczxtz');
				  },500)
			  }
			  //专项台账跳转监管案件
			  if(GetQueryString("zxtzajck") == 'yes'){
				  var caseId = GetQueryString("caseId");
				  setTimeout(function() {
			        ClickTag();
					macroMgr.onLevelTwoMenuClick(null, 'caseInfo/baseInfoCasePage?caseId='+caseId+"&parentUrl=3&selectType=1");
			        openClickTag("element-one-xtsy","element-one-jcbl","element-two-rwgl",'element-three-xczxtz');
				  },500)
			  }

		 	  // 判断是否已经给出指定的菜单ID， 下面代码可以加载到页面，但是不能自动打开菜单。 需要配合moreTolsrw 打开指定菜单
			  var openMenuId = '${openMenuId}';
			  if(typeof(openMenuId)!= "undefined" && openMenuId!=""){
				 var infoId = '${infoId}'; 
				 var systemId = '${systemId}'; 
				 if(openMenuId=='element-two-ajtz'){
				   //案件详情
					 var obj={caseId:infoId,selectType:1,parentUrl:2,case1:'FF'};
					 business.addMainContentParserHtml(WEBPATH+"/caseInfo/baseInfoCasePage",obj); 
				 }
                 if(openMenuId=='element-two-ajtzxq'){ // 案件台账详情（只查看）
                    //案件详情
                    var obj={caseId:infoId,selectType:1,case1:'FF'};
                    business.addMainContentParserHtml(WEBPATH+"/caseInfo/baseInfoCasePage",obj);
                 }
				 if(openMenuId=='element-three-rwzhtz'){
				   //任务详情-基本信息
					 var obj={taskId:infoId,lawObjectType:1,parentUrl:0,nodeCode:1,selectType:0,case1:'FF'};
					 business.addMainContentParserHtml(WEBPATH+"/taskManager/xczf",obj); 
				 }
				 if(openMenuId=='element-two-sydxgl'){
				   //事业对象管理 --对象基本信息
					 var case1='FF';
					 if(typeof(systemId)!= "undefined" && systemId!=""){  // 系统标识， 之前只有 福富公司调用  现在综合网格也需要调用 
						  case1 = systemId;
					 }
					 var obj={standenterid:infoId,typeCode:1,menuId:'4AE820E38F236585E055000000000001',menuIdAll:'4AE820E38F226585E055000000000001',case1:case1};
					 business.addMainContentParserHtml(WEBPATH+"/zfdx/detailedInformationObject",obj); 
				 }
                  if(openMenuId=='element-two-zfdxInfo'){
                      //对象基本信息
                      var obj={id:infoId,typeCode:1,menuId:'4AE820E38F236585E055000000000001',menuIdAll:'4AE820E38F226585E055000000000001'};
                      business.addMainContentParserHtml(WEBPATH+"/zfdx/detailedInformationObject",obj);
                  }

				 if(openMenuId=='element-four-tdzxjc'){
                    // 超标督办专项台账
					 var objData = {sceneSysPecailModelId:'7A0E9505C2BF0A40E055000000000001'};
					 business.addMainContentParserHtml(WEBPATH + '/taskGeneral/special-task', objData);
				 }


				 
			  }else{ 
				  $("#sslideleft li a:first").click();
			  }




		});

</script>
<script type="text/javascript">

  	//根据返回的状态码进行判断是否弹出模态框
	var isInit = '${isInit}';
	if(isInit=='true'){
		console.info("需要修改密码。。。。。。。。。");
		$(document).ready(function(){

		    $('#resetPwd').modal('show');
		    //window.location.href=WEBPATH+'/auth/toResetPwd';
		});
	}else{

		console.info("不需要强制修改密码。。。。。。。");
	}
	</script>
<script type="text/javascript">
	   //异步请求站内消息
	   function getNewMessage(){
	      $.ajax({
			  method:'POST',
			  url:WEBPATH+'/message/show5Messages',
			  data:{},
			  success:function(data){
				  //把消息展示出来
				  var list = data.list;
				  $("#newMessages").empty();
				 if(list.length>0){
					  var leng = list.length;
					  $("#newMessages").append('<li id="unreadCount"><a href="#">你有'+data.count+'个新的系统消息</a></li>');
					  for (var i=0;i < leng;i++){
						  var newtitle = "";
						  if(list[i].title.length>10){
							  newtitle = list[i].title.substr(0,10)+"...";
						  }else{
							  newtitle = list[i].title;
						  }
						  var html = '<li><a href="#" onclick="showDetail(\''+list[i].id+'\')">'+
							'<span class="notification-icon bg-success"><i class="fa fa-comment-o"></i></span>'+
							'<span class="m-left-xs" title="'+list[i].title+'">'+newtitle+'</span>'+
							'<span class="time text-muted">'+differTime(list[i].createDate)+'</span></a></li>';
							$("#newMessages").append(html);
					  }
					  
				  }else{
					  $("#newMessages").append('<li><a href="#">暂时没有新的系统消息</a></li>');
				  } 
				  $("#newMessages").append('<li><a href="#" onclick="toMessagePage()">查看所有的系统消息</a></li>');
			  }
		   });
	   }
	   
	   function showDetail(id){
			var options = {
					remote:WEBPATH+'/message/showDetailMessage?isFirstPage=1&id='+id
			};
			$('#xxxq').modal(options);
			$.ajax({
				  method:'get',
				  url:WEBPATH+'/message/getMessagesCount',
				  data:{},
				  success:function(data){
					  if(data.meta.code==200){
						  $("#msgNumber").html(data.data);
						  $("#messageCount").html(data.data);
					  }else{
						  $("#msgNumber").html(0);
						  $("#messageCount").html(0);
					  } 
				  }
			  }); 
		}
	   
	   //获取当前用户未读消息条数
	   $(function() {
		  $.ajax({
			  method:'get',
			  url:WEBPATH+'/message/getMessagesCount',
			  data:{},
			  success:function(data){
				  if(data.meta.code==200){
					  $("#messageCount").html(data.data);
				  }else{
					  $("#messageCount").html(0);
				  } 
			  }
		  }); 
	   });
	   
	   function toMessagePage(){
		   business.addMainContentParserHtml(WEBPATH + '/message/toAllMessagesPage', null);
	   }
	   
	   function closeModal(){
		   $('#xxxq').modal('hide');
	   }
	   
	   function differTime(sendDate){
	          // 计算差距时间
	          sendDate = parseInt(sendDate/1000);
	          var endTime,dateTexts ,result;
	          endTime = Date.parse(new Date()) / 1000;
	          var times = endTime - sendDate;
	          if (times < 60){
	            dateTexts = times + '秒';
	          }else if(times < 3600){
	            dateTexts =  parseInt(times / 60) + '分钟前';
	          }else if(times < 86400){
	            dateTexts = parseInt(times / 3600) + '小时前';
	          }else if(times < 259200){
	            dateTexts = parseInt(times / 86400) + '天前';
	          }else if(times < 1814400){
	            dateTexts = parseInt(times / 259200) + '周前';
	          }else if(times < 7257600){
	            dateTexts = parseInt(times / 1814400) + '月前';
	          }else if(times < 87091200){
	            dateTexts = parseInt(times / 7257600) + '年前';
	          }  
	          return dateTexts;
	      }
	</script>
<script type="text/javascript">
  $(document).ready(function(){
    document.getElementById("mask").style.display = "none";
  });
</script>


<script type="text/javascript" src="${webpath }/static/js/jquery.form.js"></script> 
<script type="text/javascript" src="${webpath }/static/libs/bootstrap/3.3.4/js/bootstrap.min.js"></script> 
<script type="text/javascript" src="${webpath }/static/js/bootstrap.min.js"></script> 

<!-- Slimscroll --> 
<script src='${webpath }/static/js/jquery.slimscroll.min.js'></script>
<%-- <script type="text/javascript" src="${webpath}/static/easyui/jquery.easyui.min.js"></script> --%>
<!-- Morris --> 
<script src='${webpath }/static/js/rapheal.min.js'></script> 
<script src='${webpath }/static/js/morris.min.js'></script> 
<!-- Sparkline --> 
<script src='${webpath }/static/js/sparkline.min.js'></script> 
<!-- Skycons --> 
<script src='${webpath }/static/js/uncompressed/skycons.js'></script> 
<!-- Popup Overlay --> 
<script src='${webpath }/static/js/jquery.popupoverlay.min.js'></script> 
<!-- Easy Pie Chart --> 
<script src='${webpath }/static/js/jquery.easypiechart.min.js'></script> 
<!-- Sortable --> 
<script src='${webpath }/static/js/uncompressed/jquery.sortable.js'></script> 
<!-- Owl Carousel --> 
<script src='${webpath }/static/js/owl.carousel.min.js'></script> 
<!-- Modernizr --> 
<script src='${webpath }/static/js/modernizr.min.js'></script> 
<!-- Simplify --> 
<script src="${webpath }/static/js/simplify/simplify.js"></script> 

<!--  注释一下两个js，是因为该js中报错，影响子页面嵌套中的$(function(){})初始化方法的加载，若需要这两个js，直接在子页面上在放在最下面引用，  最终解释权：池哥 -->
<%--<script src="${webpath }/static/js/simplify/simplify_dashboard.js"></script>
		<!-- Flot -->
	<script src='${webpath }/static/js/jquery.flot.min.js'></script> --%>

<!-- sweetalert --> 
<script src="${webpath }/static/js/sweetalert.min.js"></script> 
<!-- bootstrap-datetimepicker --> 
<script src='${webpath }/static/js/bootstrap-datetimepicker.js'></script> 
<script src='${webpath }/static/js/bootstrap-paginator.js'></script>
<%-- <script type="text/javascript" src="${webpath}/static/easyui/easyui-lang-zh_CN.js"></script> --%>
<!-- 联想搜索js --> 
<script type="text/javascript" src="${webpath }/static/js/select2.full.js"></script> 
<script type="text/javascript" src="${webpath }/static/js/locales/select2/zh-CN.js" ></script>
<!--任务要求附件上传-->
<%-- <script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script> --%>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/fileinput.js" type="text/javascript"></script> 
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/themes/explorer/theme.js" type="text/javascript"></script>
<%--<script src='${webpath }/static/js/iview.js'></script> --%>

<!-- 业务逻辑相关js --> 
<script type="text/javascript" src="${webpath}/static/common/js/loding.js"></script> 
<script type="text/javascript" src="${webpath}/static/common/js/macro.js"></script> 
<script type="text/javascript" src="${webpath}/static/common/js/business.js"></script> 
<script type="text/javascript" src="${webpath}/static/common/js/message.js"></script> 
<script type="text/javascript" src="${webpath}/static/common/js/phone.js"></script> 
<script type="text/javascript" src="${webpath }/static/easyui/jquery.easyui.min.js"></script> 
<script type="text/javascript" src="${webpath }/static/easyui/easyui-lang-zh_CN.js"></script> 
<!-- zTree -->
<link rel="stylesheet" href="${webpath }/static/zTree_v3-master/css/zTreeStyle/zTreeStyle.css" type="text/css">
<script type="text/javascript" src="${webpath }/static/zTree_v3-master/js/jquery.ztree.core.js"></script> 
<script type="text/javascript" src="${webpath }/static/zTree_v3-master/js/jquery.ztree.excheck.js"></script> 
<!-- pdfobject -->
<%-- <script src="${webpath }/static/pdfjs/web/jQuery.XDomainRequest.js"></script> --%>
<script type="text/javascript" src="${webpath }/static/PDFObject/pdfobject.min.js"></script> 
<!-- 禁止使用F5刷新功能 --> 
<!--<script type="text/javascript" src="${webpath }/static/js/nof5.js"></script>--> 

<script charset="utf-8" type="text/javascript" src="${webpath }/static/js/echarts-all-3.js"></script> 
<!-- 打开关闭标签js --> 
<script type="text/javascript" src="${webpath }/static/js/openCloseTag.js"></script> 

<!-- bootstraptable固定行和列 --> 
<script type="text/javascript" src="${webpath }/static/js/bootstrap-table-fixed-columns.js"></script> 

<!-- ueditor --> 
<!-- 有一个引用的js文件在编辑器所在页面单独引用 -->
<link rel="stylesheet" href="${webpath }/static/ueditor/themes/default/css/ueditor.min.css" media="all">
<script type="text/javascript" charset="utf-8" src="${webpath }/static/ueditor/ueditor.config.js"></script> 
<script type="text/javascript" charset="utf-8" src="${webpath }/static/ueditor/lang/zh-cn/zh-cn.js"></script> 

<!-- Sortable --> 
<script src="${webpath }/static/js/jquery-ui.js"></script> 
<script type="text/javascript" src="http://api.tianditu.gov.cn/api?v=4.0&tk=8a3338b7a936b8e4b394f6ac6bfe191e"></script>
<%--<script type="text/javascript"--%>
            <%--src="${webpath }/static/js/HeatmapOverlay.js"></script>--%>
</body>
