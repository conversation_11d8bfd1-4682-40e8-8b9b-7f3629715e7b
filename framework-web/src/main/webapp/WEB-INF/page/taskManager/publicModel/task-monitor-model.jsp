<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Insert title here</title>
</head>
<body>
    <script type="text/javascript">
	 var webpath = '${webpath}';
	 var lawObjectId = '${lawObjectId}';
	 var lawID = '${lawID}';
    $(document).ready(function(){
    	// 注释 模态框清理 
	 	$('#linkLawModeler').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})   
	     $('#monitorTaskModle').bootstrapTable({
			 method: 'post',
			 dataType: "json", 
			 url:  WEBPATH+'/taskGeneral/task-monitor-list',
		     undefinedText : '-',  
		     pagination : true, // 分页  
		     striped : true, // 是否显示行间隔色  
		     cache : false, // 是否使用缓存  
		     pageSize:10, // 设置默认分页为 20
		     pageNumber: 1,
		     queryParamsType: "",
		     clickToSelect:true,
		     checkboxHeader:false,
		     locale:'zh-CN',
		     pageList: [5, 10, 20,30,50], // 自定义分页列表
		     singleSelect: false,
		     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
		     // showColumns : true, // 显示隐藏列  
		     sidePagination: "server", //服务端请求
		     queryParams:function (params) {
		    		var handlingPersonName=$("[name='handlingPersonName']").val(); 
		    		var taskNumber=$("[name='taskNumber']").val();
		    		var checkBeginData=$("[name='checkBeginData']").val();
		    		var checkEndData=$("[name='checkEndData']").val();
		            var objectName = $("#objectName").val();
		            var lawObjectName = '${lawObjectId}';
		            var lawID = '${lawID}';
		            var temp = {   
		            	checkBeginData:checkBeginData,
		            	checkEndData:checkEndData,
		            	taskNumber:taskNumber,
		            	handlingPersonName:handlingPersonName,
		            	lawObjectName:lawObjectId, // 后台没有ID接收，直接对应到name上去
	          		 	pageNum: params.pageNumber,
	          		 	lawID:lawID,
	                    pageSize: params.pageSize 
		            };
		            return temp;
		     },//参数
		     uniqueId : "id", // 每一行的唯一标识  
			 columns: [
			           {
			        	   field: "taskNumber",
				           title: "执法编号",
				           align: 'center'
			           },
			           {
				           field: "lawObjectName",
				           title: "执法对象名称",
				           align: 'center'
				       },
			           {
				           field: "handlingUnitName",
				           title: "执法部门",
				           align: 'center'
				       },
			           {
				           field: "taskLawobjectStatus",
				           title: "执法类型",
				           align: 'center',
				           formatter:function(value){
				        	   //正常分配为空（null）：分配执法任务，1：直接现场执法、2：双随机分配任务、3：特定专项检查,5后督察，4调查取证
				        	   var name = '';
				        	   if(value==1){
				        		   name ='直接现场执法';
				        	   }else if(value==2){
				        		   name ='双随机分配任务';
				        	   }else if(value==2){
				        		   name ='特定专项检查';
				        	   }else if(value==4){
				        		   name ='调查取证';
				        	   }else if(value==5){
				        		   name ='后督察';
				        	   }else{
				        		   name ='分配执法任务';
				        	   }
				        	return name;
				           }
				       },
				       {
				           field: "taskFromName",
				           title: "执法来源",
				           align: 'center'
				       },
				       {
				           field: "handlingPersonName",
				           title: "检查人",
				           align: 'center'
				       },
				       {
				           field: "checkStartDate",
				           title: "检查日期",
				           align: 'center',
				   		   formatter : function(value){
								if(value==null || value==''){return '';}
				                var date = new Date(value);
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                var h = date.getHours();  
				                var i = date.getMinutes(); //分
				                var s = date.getSeconds(); //秒
				                return y + '-' +m + '-' + d+' '+h+':'+i+':'+s;
				            }
				       },
			           {
				    	   checkbox: true,
				           title: "操作",
				           align: 'center'
			           } 
			 ],
			 responseHandler : function(res) {  
	             return {  
	                 total : res.total,  
	                 rows : res.list  
	             };  
	       },
	  /*    rowStyle:function(row,index) {
	       	return {
	       		   css: {"cursor": "pointer"}
	       		 };
	       }, */
	   	 	onCheck: function(row, $element) {
		  		/*  var rowCode = row.code;
		  		 var rowName = row.name;
				// 全局变量 	
				if($.inArray(rowCode, entProtectionBaseIDArr) == -1){ // 不在加就追加   
					entProtectionBaseIDArr.push(rowCode);
					entProtectionBaseNameArr.push(rowName);
				}  */
	  		}, 
	        onUncheck: function(row, $element) {
	     /*    	var rowCode = row.code;
		  		var rowName = row.name;
				// 全局变量 	
				if($.inArray(rowCode, entProtectionBaseIDArr) >-1){ // 若在就移除 
					entProtectionBaseIDArr.splice($.inArray(rowCode,entProtectionBaseIDArr),1); 
					entProtectionBaseNameArr.splice($.inArray(rowName,entProtectionBaseNameArr),1); 
				}  */
		     },
		     onUncheckAll: function(row, $element) {
		       			
		     },
		     onCheckAll:function(row, $element) {
		        		
		     },
		     onRefresh: function () {
		        		
		     }, // 双击事件
		     onDblClickRow: function(row, $element){
					    	 
		     }, // 列表加载成功后
		     onLoadSuccess:function(data,e){
		/*    		 if(entProtectionBaseIDArr !='' && entProtectionBaseIDArr.length>0){ 
		   			var rows = data.rows;
	  				if(rows.length>0){ // 若数据，则不许要循环
	  					for( var j =0; j < rows.length; j++ ){
	  						if($.inArray(rows[j].code, entProtectionBaseIDArr) >-1){
	  							$('#monitorTaskModle').bootstrapTable('check', j);
	  						}
	  					}
	  				}
		   		 } */
	     	 },
	       formatLoadingMessage: function () {
	      	   return "玩命加载中...";
	       },
	       formatNoMatches: function () { //没有匹配的结果
	      		   return '无符合条件的记录';
	       }
		});
    	
		// 监听回车和回退键
	 	business.listenEnter("searchMonitor");
		
		//绑定搜索按钮
		$('#searchMonitor').click(function() {
			$('#monitorTaskModle').bootstrapTable('refresh');
			//$('#monitorTaskModle').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
	   	});
    });
    
	// 搜索时间控件的控制 
	$("[name='checkBeginData']").datetimepicker({
		 	format:'yyyy-mm-dd',
			todayBtn : true,
			language: 'cn',
			autoclose : true,
			clearBtn : true,
			minView : 'month',
			maxView : 'decade' 
	}).on('changeDate',function(ev){
		$("[name='checkEndData']").datetimepicker('setStartDate',new Date(ev.date.valueOf()));
	});
	$("[name='checkEndData']").datetimepicker({
		 	format:'yyyy-mm-dd',
			todayBtn : true,
			language: 'cn',
			autoclose : true,
			clearBtn : true,
			minView : 'month',
			maxView : 'decade' 
	}).on('changeDate',function(ev){
		$("[name='checkBeginData']").datetimepicker('setEndDate',new Date(ev.date.valueOf()));
	});
    
    function saveBtnForm(){
    	//选中
    	var taskList= JSON.stringify($("#monitorTaskModle").bootstrapTable('getSelections'));
		//console.log("save..."+JSON.stringify(taskList));
		if(taskList.length == 2){
			return ;
		}
		$.ajax({
			dataType:"json",
			type:"POST",
			url:webpath+"/overlaw/saveLaw",
			data:{taskList:taskList,lawObjectId:lawObjectId,lawID:lawID},
			success:function(data){
				if(data.code == '001'){
					swal({title:"操作成功!", text:"已成功关联！", type:"success",allowOutsideClick :true});
					//business.addMainContentParserHtml(WEBPATH+'/overlaw/linkCaseAndLaw?lawObjectId='+lawObjectId+"&lawID="+lawID,null);
					//console.log(taskList);
					//vueTaskTag.taskList.push(data.result);
					console.log(data.result.length);
					if(data.result.length!=0){
						var taskListTemp = eval(data.result);
						vueTaskTag.taskList=taskListTemp;
					}
				}else if(data.code == '002'){
					swal({title:"提示!", text:"关联监察执法不能超过5条！", type:"info",allowOutsideClick :true});
					//macroMgr.onLevelTwoMenuClick(null, 'overlaw/linkCaseAndLaw?lawObjectId='+lawObjectId+"&lawID="+lawID);
				}else {
					swal({title:"提示!", text:"关联发生错误！", type:"info",allowOutsideClick :true});
					//macroMgr.onLevelTwoMenuClick(null, 'overlaw/linkCaseAndLaw?lawObjectId='+lawObjectId+"&lawID="+lawID);
				}
			}
		});
    }
    </script>
	  	 <div class="modal-header">
             <div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info" onclick="saveBtnForm()" data-dismiss="modal">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
             </div>
             <h4 class="modal-title">关联执法任务</h4>
         </div>
         <div class="modal-body">
			<div class="smart-widget-body">
			
			<form class="form-horizontal">
				<div class="form-group">
					<label for="检查人" class="col-lg-2 control-label">检查人</label>
					<div class="col-lg-3">
						<input type="text" class="form-control" name="handlingPersonName" placeholder="检查人">
					</div>
					<label for="执法编号" class="col-lg-2 control-label">执法编号</label>
					<div class="col-lg-3">
						<input type="text" class="form-control" name="taskNumber" placeholder="执法编号">
					</div>
				</div>
				<div class="form-group">
                   	 <label for="检查时间" class="col-lg-2 control-label">检查时间</label>
                       <div class="col-md-3" style="padding:0px;">
                           <div class="col-md-6">
                           <input type="text" placeholder="开始时间" class="form-control" name="checkBeginData" data-parsley-required="true">
                           </div> 
                           <div class="col-md-6">
                           <input type="text" placeholder="结束时间" class="form-control" name="checkEndData" data-parsley-required="true">
                           </div>
                       </div>
                       <div class="col-lg-offset-2 col-lg-2">
						<button type="button" id="searchMonitor" class="btn btn-info" style="width: 120px;">查询</button>
					</div>                                    
                </div>
            </form>

			</div>
			<table class="table table-no-bordered" id="monitorTaskModle">
            </table>
        </div>
</body>
</html>