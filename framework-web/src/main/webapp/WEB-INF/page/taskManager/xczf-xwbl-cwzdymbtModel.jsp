<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript">
	$('#cwzdymb').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	});
	$("#templateContributionName").val($("#contributionName").val());
	//保存自定义模板 
	$("#saveSceneBtn").click(function(){
		//获取区划
		var belongProvince = $("#belongProvince").val();
		var belongCity =$("#belongCity").val();
		var belongCountry =$("#belongCountry").val();
		if(belongCountry!= null && belongCountry !=''){
			$("#templateArea").val(belongCountry);
			$("#templateAreaname").val($('#belongCountry option:selected').text());
		}else if(belongCountry =='' && belongCity != '' ){
				//选择市
			$("#templateArea").val(belongCity);
			$("#templateAreaname").val($('#belongCity option:selected').text());
		}else{
			$("#templateArea").val('35000000');
			$("#templateAreaname").val("福建省");
		}
		//判断模板的名称不能为空
		var templateName  =$("#templateName").val();
		if(templateName == '' || templateName == null){
			swal({title:"提示 ", text:"模板的名称不能为空!", type:"error",allowOutsideClick :true});
			return false;
		}
		if(kyblVue.items.siteCondition.length>5000){
			swal({title:"提示 ", text:"现场情况最大5000字符!", type:"error",allowOutsideClick :true});
			return false;
		}
		//适用行业名称
	    $("#templateIndustryName").val($("#templateIndustryNameTemp").html());
		var text = kyblVue.items;
		var siteConditionList = new Array();
			var obj ={siteCondition:kyblVue.items.siteCondition,surveyModelerId:kyblVue.items.surveyModelerId,templateObjectType:kyblVue.items.lawObjectType};
			siteConditionList.push(obj);
		$("#siteConditionList").val(JSON.stringify(siteConditionList));
			var options = {
				url : WEBPATH + '/inquestRec/saveTemplate',
				type : 'post',
				dataType:"json",
				success : function(data) {
					if(data.meta.result =='success'){
						if(data.data.tempStatus =='1'){
							swal({title:"提示 ", text:data.meta.message, type:"error",allowOutsideClick :true});
						}else{
						$("#contributionName").val(data.contributionName);
						$("#templateContributionName").val(data.contributionName);
						swal({title:"提示 ", text:data.meta.message, type:"success",allowOutsideClick :true});
						}
					}
				},
				error : function() {
					swal({title:"提示 ", text:"保存模板信息失败!", type:"error",allowOutsideClick :true});
				}
			}
			$('#templeteForm').ajaxSubmit(options);
	});
	
	//		适用所用行业
		var templateIndustryNameTemp = null;
		var templateIndustryCodeTemp =null;
		$("#templateIndustryStatus").click(function(){
		    if($('input[name="templateIndustryStatus"]').prop("checked"))
	        {
		    	templateIndustryNameTemp = $("#templateIndustryNameTemp").html();
		    	templateIndustryCodeTemp =	$("#templateIndustry").val();
	            $("#templateIndustryNameTemp").html("所有行业");
	            $("#templateIndustry").val("all");
	        }
	        else
	        	$("#templateIndustryNameTemp").html(templateIndustryNameTemp);
		    	$("#templateIndustry").val(templateIndustryCodeTemp);
		});
	
	$.ajax({
		type:"post",
		url:WEBPATH+"/tArea/chickUserArea",
		dataType:"json",
		async:false,
		data:{},
		success:function(data){
			if(data.cityStatus =='1'){
				//省级用户
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/cityList",
					async:false,
					dataType:"json",
					success:function(data){
						$("#belongCity").append("<option value=''>请选择</option>"); 
						$("#belongCountry").append("<option value=''>请选择</option>"); 
						$.each(data,function(i,item){
							$("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
						});
					}
				});
			}else if(data.cityStatus =="2"){
				//市级用户
				$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/countyListByCode",
					dataType:"json",
					data:{parentCode:data.cityCode},
					success:function(data){
						$("#belongCountry").append("<option value=''>请选择</option>"); 
						$.each(data,function(i,item){
							$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
						});
					}
				});
			}else{
				//县级用户
				$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$("#belongCountry").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
			}
		}
	});
	$("#belongCity").change(function(){
		if ($(this).val() == ""){
			$("#belongCountry option").remove();
			$("#belongCountry").append("<option value=''>请选择</option>"); 
			return;
		}
		var parentCode = $(this).val();
		$("#belongCountry option").remove();
		$.ajax({
			type:"post",
			url:WEBPATH+"/tArea/countyListByCode",
			async:false,
			dataType:"json",
			data:{parentCode:parentCode},
			success:function(data){
				$("#belongCountry").append("<option value=''>请选择</option>"); 
				$.each(data,function(i,item){
					$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
				});
			}
		});
	});
</script>
 <!-- 存为自定义模板（Modal） -->
               <div class="modal-header">
                    <div style="float:right; margin-top:-5px;">
                    	<button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
					</div>
                    <h4 class="modal-title" id="myModalLabel">存为自定义模板</h4>
                </div>
                <form action="#" method="post" id= "templeteForm">
                <input id="templateAreaname" name="templateAreaname" type="hidden" ></input> 
				<input id="templateArea" name="templateArea" type="hidden" ></input>
				<input type="hidden" name ="templateObjectType" value ="${lawEnforceObject.typeCode}">
                <div class="modal-body">
                    <div class="smart-widget-body form-horizontal">
                        <div class="form-group">
                            <label class="col-lg-3 control-label">
                          	<span style="color: red;">*</span>名称</label>
                            <div class="col-lg-8">
                            <input type="text"  onkeydown="if(event.keyCode==13){return false;}" class="form-control" id="templateName" name ="templateName" placeholder="名称">
                            </div>
                        </div>
                        <div class="form-group">
                                          <label class="col-lg-3 control-label">适用地区</label>
                                            <div class="col-lg-2">
                                                <select 
                                                    class="form-control" id ='belongProvince' name ="belongProvince">
                                                    <option value="35000000">福建省</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-3">
                                                <select
                                                    class="form-control" id ="belongCity" name="belongCity">
                                                <!--     <option value="">请选择</option> -->
                                                </select>
                                            </div>
                                            <div class="col-lg-3">
                                                <select
                                                    class="form-control" id ="belongCountry" name ="belongCountry">
                                                </select>
                                            </div>
                                        </div>
                         <div class="form-group">
                            <label class="col-lg-3 control-label">适用对象类型</label>
                            <div class="col-lg-8" style="margin-top:7px;">
                            		<c:choose>
												<c:when test="${lawEnforceObject.typeCode=='1'}"> 
												   		企事业单位
													   </c:when>
												<c:when test="${lawEnforceObject.typeCode=='2'}">   
												       	个人
												</c:when>
												<c:when test="${lawEnforceObject.typeCode=='3'}">   
												       个体、三无、小三产
												</c:when>
												<c:when test="${lawEnforceObject.typeCode=='4'}">   
												       	自然保护区
												</c:when>
												<c:when test="${lawEnforceObject.typeCode=='6'}">
												       	水源地
												</c:when>
												<c:otherwise>
												      	 无主
											 </c:otherwise>
											</c:choose>
                            	
                            </div>
                        </div>
                        <c:if test = "${lawEnforceObject.typeCode=='1'}">
                         <div class="form-group">
                            <input type="hidden" value =" ${lawEnforceObject.industryTypeCode}" name ="templateIndustry" id ="templateIndustry">
                            <input type="hidden" name ="templateIndustryName" id ="templateIndustryName">
                           	<input type ="hidden" id="templateContributionName" name ="contributionName" value ="${ surveyRecord.contributionName}">
                            <label for="行业类型" class="col-lg-3 col-md-3 control-label">适用行业</label>
                            <div class="col-lg-8" style="margin-top:7px;" >
                         		<span id ="templateIndustryNameTemp">  ${lawEnforceObject.industryTypeName} </span>
                                <div class="checkbox inline-block" style="padding-right:10px; float:right;">
                                    <div class="custom-checkbox">
                                        <input type="checkbox" id="templateIndustryStatus" name ="templateIndustryStatus">
                                        <label for="templateIndustryStatus" class="checkbox-blue" ></label>
                                    </div>
                                    <div class="inline-block vertical-top">
                                        	所有行业
                                    </div>
                                </div>
                            </div>
                        </div></c:if>
                        <input type="hidden" id ="siteConditionList" name ="siteConditionList" >
                        <input type ="hidden" id ="lastSurveyModelerId" name ="lastSurveyModelerId">
                         <div class="form-group">
                            <label class="col-lg-3 control-label">是否设为常用</label>
                            <div class="col-lg-8">
                                <select class="form-control" id ="usuallyStatus" name ="usuallyStatus">
                                    <option value="">请选择</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                         </div>
                         <div class="form-group">
                            <label class="col-lg-3 control-label">是否设为默认加载模板</label>
                            <div class="col-lg-8">
                                <select class="form-control" name ="defaultStatus">
                                    <option value="">请选择</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                         </div>
                    </div>
                </div>
                </form>
                <div class="modal-footer">
                    <!--<button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>-->
                </div>
    <!-- ./存为自定义模板（Modal） -->

