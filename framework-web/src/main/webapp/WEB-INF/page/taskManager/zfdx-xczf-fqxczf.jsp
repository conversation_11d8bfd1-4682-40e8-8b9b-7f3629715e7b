<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>  
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="utf-8">
 <style>
.tangram-suggestion-main {
    z-index: 1060;
}
</style>
</head>
<script type="text/javascript">
	// $(document).ready(function(){
<%--		// var htmlCity = "<option value=''>——市级——</option>";--%>
<%--		// var htmlCounty = "<option value=''>——县级——</option>";--%>
		//执法对象所在行政区
		<%--var belongAreaId = '${task.belongAreaId}';--%>
		// console.log(belongAreaId)
<%--		//权属行政区id--%>
<%--		var powerAreaId = '${task.powerAreaId}';--%>
// 		console.log(powerAreaId)
<%--		//35000000 35010000  35010200--%>
<%--		// 获得后6位信息--%>
<%--		var belongCity = '',belongCounty='';--%>
<%--		var powerCity = '', powerCounty='';--%>
<%--		//执法对象所在行政区--%>
<%--		if($.trim(belongAreaId.substring(2)) =='000000'){// 省--%>
<%--			belongCity = '',belongCounty='';--%>
<%--		}else if($.trim(belongAreaId.substring(4)) =='0000'){// 市--%>
<%--			belongCity = belongAreaId ;--%>
<%--			belongCounty='';--%>
<%--		}else if ($.trim(belongAreaId.substring(6))=='00'){// 县--%>
<%--			belongCity = $.trim(belongAreaId.substr(0,4)+'0000');--%>
<%--			belongCounty = belongAreaId;--%>
<%--		}--%>
<%--		//权属行政区id--%>
<%--		if($.trim(powerAreaId.substring(2)) =='000000'){// 省--%>
<%--			powerCity = '', powerCounty='';--%>
<%--		}else if($.trim(powerAreaId.substring(4)) =='0000'){// 市--%>
<%--			powerCity = powerAreaId ;--%>
<%--			powerCounty='';--%>
<%--		}else if ($.trim(powerAreaId.substring(6))=='00'){// 县--%>
<%--			powerCity = $.trim(powerAreaId.substr(0,4)+'0000');--%>
<%--			powerCounty = belongAreaId;--%>
<%--		}--%>

<%--		$.ajax({--%>
<%--			type:"post",--%>
<%--			url:WEBPATH+"/tArea/cityList",--%>
<%--			dataType:"json",--%>
<%--			success:function(data){--%>
<%--				// $("#belong_city").html(htmlCity);--%>
<%--				// $("#power_city").html(htmlCity	);--%>
<%--				$.each(data,function(i,item){--%>
<%--					if(item.code==belongCity){--%>
<%--						$("#law_object_city").append(item.name);--%>
<%--					}--%>
<%--					if(item.code == powerCity){--%>
<%--						$("#power_city").append(item.name);--%>
<%--					}--%>
<%--				});--%>
<%--			}--%>
<%--		});--%>

<%--		if(belongCounty!=''){--%>
<%--			var parentCode = belongCity;--%>
<%--			$.ajax({--%>
<%--				type:"post",--%>
<%--				url:WEBPATH+"/tArea/countyListByCode",--%>
<%--				dataType:"json",--%>
<%--				data:{parentCode:parentCode},--%>
<%--				success:function(data){--%>
<%--					// $("#belong_county").html(htmlCounty);--%>
<%--					$.each(data,function(i,item){--%>
<%--						if(item.code==belongCounty){--%>
<%--							$("#law_object_county").append(item.name);--%>
<%--						}--%>
<%--					});--%>
<%--				}--%>
<%--			});--%>
<%--		}--%>
<%--		if(powerCounty!=''){--%>
<%--			var parentCode = powerCity;--%>
<%--			$.ajax({--%>
<%--				type:"post",--%>
<%--				url:WEBPATH+"/tArea/countyListByCode",--%>
<%--				dataType:"json",--%>
<%--				data:{parentCode:parentCode},--%>
<%--				success:function(data){--%>
<%--					// $("#power_county").html(htmlCounty);--%>
<%--					$.each(data,function(i,item){--%>
<%--						if(item.code == powerCounty){--%>
<%--							$("#power_county").append(item.name);--%>
<%--						}--%>
<%--					});--%>
<%--				}--%>
<%--			});--%>
<%--		}--%>
// 	});
</script>
<body class="overflow-hidden">
		<sec:authentication property="principal" var="authentication"/>	
		<div class="main-container">
			<div class="padding-md">
				<!--第三层任务办理row-->

				<!--第一层办理流转row-->
                <div class="row">
                    <!--按钮-->
                    <div class="col-lg-12">
						<div class="smart-widget widget-light-grey">
								<div class="smart-widget  widget-blue">
							<div class="smart-widget-header font-16">
								<i class="fa fa-arrow-right"></i>${taskTitle}
                                <span class="smart-widget-option" style="margin-top:-7px;">
                                    <span class="refresh-icon-animated"><i class="fa fa-circle-o-notch fa-spin"></i></span>
								</span>
							</div>
							<div class="smart-widget-inner table-responsive">
							<c:if test ="${task.taskFromType !=1  and task.taskFromType !=4 and task.taskFromType !=5 }">
							<div style="float:left;font-size:14px;color:red; padding:10px 20px;">注意：专项执法检查任务必须要完成专项检查表之后才能办结。</div>
							</c:if>
                                <div style="float:right;">
                                    <div class="padding-xs" style="float:left;">
                                        <button type="button" class="btn btn-default btn-block disabled" >上报数据</button>
                                    </div>
                                    <div class="padding-xs" style="float:left;">
                                        <button type="button" class="btn btn-default btn-block disabled" >流转跟踪</button>
                                    </div>
                                    <div class="padding-xs" style="float:left;">
                                        <button type="button" class="btn btn-default btn-block"  onclick="goBack('${preUrl}')">返回</button>
                                    </div>
                               </div>
                                            
                            </div>
                        </div>
                    </div>
                    <!--./按钮-->                                                        
                </div>
                <!--./第一层办理流转row-->

				<!--第二层流转操作row-->
				<div class="row">
					<!--按钮-->
					<div class="col-lg-12">
						<div class="smart-widget widget-light-grey">
							<div class="smart-widget-inner table-responsive">
								<div class="smart-widget-body">
											<!--现场检查表-->
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-grey m-bottom-md"
													>
													<div class="statistic-title">
														<i class="fa fa-file-text-o" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
														<h4>执法信息</h4>
													</div>
												</div>
											</div>
											<!--./现场检查表-->

											<!--现场检查表-->
                                            
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-sgrey m-bottom-md"
													>
													<div class="statistic-title">
														<i class="fa fa-file-text-o" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
													<c:choose>
															<c:when test="${isSpecial ==1}">
															<h4>专项检查表</h4>
															</c:when>
															<c:otherwise>
															<h4>现场检查表</h4>
															</c:otherwise>
														</c:choose>
													</div>
												</div>
                                            </div>
											<!--./现场检查表-->

											<!--勘察笔录-->
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-sgrey m-bottom-md"
													>
													<div class="statistic-title">
														<i class="fa fa-file-text-o" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
														<h4>勘察笔录</h4>
													</div>
												</div>
											</div>
											<!--./勘察笔录-->

											<!--询问笔录-->
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-sgrey m-bottom-md"
													>
													<div class="statistic-title">
														<i class="fa fa-file-text-o" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
														<h4>询问笔录</h4>
													</div>
												</div>
											</div>
											<!--./询问笔录-->

											<!--证据采集-->
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-sgrey m-bottom-md"
													>
													<div class="statistic-title">
														<i class="fa fa-file-text-o" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
														<h4>上传证据</h4>
													</div>
												</div>
											</div>
											<!--./证据采集-->

											<!--执法小结-->
											<div class="col-lg-2 col-sm-2 col-xs-4">
												<div class="statistic-box bg-sgrey m-bottom-md">
													<div class="statistic-title">
														<i class="fa fa-laptop" style="font-size: 40px;"></i>
													</div>
													<div class="statistic-title">
														<h4>执法小结</h4>
													</div>
												</div>
											</div>
											<!--./执法小结-->
								</div>
							</div>
						</div>
					</div>
					<!--./按钮-->
				</div>
				<!--./第二层流转操作row-->
				<!--第二层任务办理row-->
				<div class="row">
					<div class="col-lg-12">
						<div class="smart-widget widget-light-grey">
							<div class="smart-widget-header font-16">
								<i class="fa fa-arrow-right"></i> 
								现场执法基本信息
								<!-- 现场执法基本信息  -->
								<span
									class="smart-widget-option"> <span
									class="refresh-icon-animated"> <i
										class="fa fa-circle-o-notch fa-spin"></i>
								</span> 
								</span>
							</div>
							<div class="smart-widget-inner table-responsive">
								<div class="smart-widget-body form-horizontal">      
								     <form  action="#" method="post" id="taskAllocationForm" >
										<div id ="previewIds" style ="display: none;"></div>
										<input id="lawObjectId" name="lawObjectId"  value ="${task.lawObjectId }" type="hidden">
									<!-- 检查人的信息-->
										<input  name="token" value="${tokenReport }" type="hidden">
										<input id="checUserIds" name="checUserIds"  value="${task.checUserIds}" type="hidden">
									    <input id="userId"  value="${authentication.id}" type="hidden">
										<input id="taskFlowId" name="taskFlowId" value="${taskFlowId}" type="hidden">
									    <input id=monitorTypeCode value="${task.monitorTypeCode}" name="monitorTypeCode" type="hidden">
										<input id="specialActionIds" value="${task.specialActionIds}" name="specialActionIds" type="hidden">
										<input id="cardTypeCode" value="${task.cardTypeCode}" name="cardTypeCode" type="hidden">
<%--										 <input id="belongAreaId" value="${task.belongAreaId}" name="belongAreaId" type="hidden">--%>
									    <input id="lawObjectType" value="${task.lawObjectType}" name="lawObjectType" type="hidden">
									    <input id="taskId" value="${task.id}" name="taskId" type="hidden">
										<input id="sysFileUrl" value="${sysFileUrl}" name="sysFileUrl" type="hidden">
										<input id="sysFileType" value="${sysFileType}" name="sysFileType" type="hidden">
										<input id="sysFileIds" value="${sysFileIds}" name="sysFileIds" type="hidden">
										<input id="updateObjectState" name= "updateObjectState" type="hidden">
										 <input id="taskFromType" value="${task.taskFromType}" name="taskFromType" type="hidden">
										<input id ="taskFromTypeStatus" type="hidden" value ="${status }">
										 <input id ="belongAreaId" type="hidden" value ="${belongAreaId }">
								<div class="form-group">
								<label for="执法对象名称" class="col-lg-2 control-label"><span
									style="color: red;">*</span> 执法对象名称</label>
								<div class="col-lg-8">
										<div class="input-group">
										<input placeholder="请选择执法对象名称" type="text" id="lawObjectName" value ="${task.lawObjectName }"  readonly = "readonly" 
											name="lawObjectName" class="form-control" >
										<div class="input-group-btn">
											<button type="button" class="btn btn-info no-shadow"
												 tabindex="-1" data-toggle="modal"
												  data-remote="${webpath}/jcbl/xczf-xckfb-lawobjecttmodel"
												data-target="#zfdx">执法对象选择</button>
										</div>
									</div>
								</div>
							</div>
					<c:choose>
                                <c:when test="${ not  empty task }">
                                    <c:choose>
                                        <c:when test="${task.lawObjectType == 2}">
                                        <!-- 个人-->
                                        <div class="form-group" id ="hiddenCardType" >
											<label  for="证照名称" class="col-lg-2 control-label">证照名称</label>
                                        	<div class="col-lg-8">
                                         	<select class="form-control" name="cardTypeName" id ="cardTypeName"  disabled="disabled">
											<option value="">请选择</option>
											<c:forEach items="${cardTypeList1}" var="item">
												<option value="${item.name }#${item.code}"
												<c:if test="${item.code== task.cardTypeCode }">selected</c:if>
												>${item.name }</option>
											</c:forEach>
                                         </select> 
                                         </div></div>
                                        </c:when>
                                         <c:when test="${task.lawObjectType == 3 or  task.lawObjectType  ==5}">
                                        <!-- 无主和个体-->
                                        <div class="form-group" id ="hiddenCardType" >
											<label  for="证照名称" class="col-lg-2 control-label">证照名称</label>
                                        	<div class="col-lg-8">
                                         	<select class="form-control" name="cardTypeName" id ="cardTypeName"  disabled="disabled">
											<option value="">请选择</option>
											<c:forEach items="${cardTypeList}" var="item">
												<option value="${item.name }#${item.code}"
												<c:if test="${item.code== task.cardTypeCode }">selected</c:if>
												>${item.name }</option>
											</c:forEach>
										</select> 
										</div>
										</div>
                                        </c:when>
                                         <c:when test="${task.lawObjectType == 1}">
                                        <!-- 企业-->
                                        <div class="form-group" id ="hiddenCardType" >
											<label  for="证照名称" class="col-lg-2 control-label">证照名称</label>
                                        	<div class="col-lg-8">
                                        	 <select class="form-control"  id ="cardTypeName"  >
													<option value="">统一社会信用代码</option>
											 </select>
                                       	  </div></div>
                                        </c:when>
                                         <c:when test="${task.lawObjectType == 4}">
                                        <!-- 自然保护区-->
                                        <div class="form-group" id ="hiddenCardType" >
											<label  for="证照名称" class="col-lg-2 control-label">证照名称</label>
                                        	<div class="col-lg-8">
                                        	  <select class="form-control" id ="cardTypeName"  >
													<option value="">管理机构统一社会信用代码</option>
											 </select>
                                        	
                                       	  </div></div>
                                        </c:when>
<%--                                        <c:otherwise>--%>
<%--                                           <div class="form-group" id ="hiddenCardType" >--%>
<%--											<label  for="证照名称" class="col-lg-2 control-label">证照名称</label>--%>
<%--			                                <div class="col-lg-8">--%>
<%--			                                 <select class="form-control" name="cardTypeName" id ="cardTypeName"  >--%>
<%--													<option value="">请选择</option>--%>
<%--											 </select>--%>
<%--											  </div>--%>
<%--											 </div>--%>
<%--                                        </c:otherwise>--%>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                </c:otherwise>
                            </c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType == 6}">
								 <div class="form-group">
									 <label for="执法对象所在行政区" class="col-lg-2 control-label">执法对象所在行政区</label>
									 <div class="col-md-2 col-lg-2 col-sm-2">
										 <select class="form-control" id ="law_object_province" name ="law_object_province">
											 <option value="35000000">福建省</option>
										 </select>
									 </div>
									 <div class="col-md-3 col-lg-3 col-sm-3">
										 <select
												 class="form-control" id ="law_object_city" name ="law_object_city">
										 </select>
									 </div>
									 <div class="col-md-3 col-lg-3 col-sm-3">
										 <select
												 class="form-control" id ="law_object_county" name ="law_object_county">
										 </select>
									 </div>
								 </div>
								</c:when>
							</c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
								<div class="form-group">
									<label for="证照号" class="col-lg-2 control-label">证照号</label>
									<div class="col-lg-8">
										<input type="text" id="cardCode" name="cardCode"  readonly = "readonly"
											value="${task.cardCode }${lawObject.cardCode }" class="form-control"
											placeholder="证照号">
									</div>
								</div>
								</c:when>
							</c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
								<div class="form-group">
									<label for="联系人" class="col-lg-2 control-label">
									<c:if test="${task.lawObjectType!=5}">
										<span style="color: red;">*</span>
									</c:if>
									联系人</label>
									<div class="col-lg-8">
										<input type="text" id="linkman" name="linkman"
											value="${task.linkman }${lawObject.linkMan }" class="form-control"  readonly = "readonly"
											placeholder="联系人">
									</div>
								</div>
								</c:when>
							</c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
								<div class="form-group">
									<label for="联系方式" class="col-lg-2 control-label">
									<c:if test="${task.lawObjectType!=5}">
										<span style="color: red;">*</span>
									</c:if>
									联系方式</label>
									<div class="col-lg-8">
										<input type="text" id="legalPhone" name="legalPhone" value="${task.legalPhone }${lawObject.linkPhone }" class="form-control"  readonly = "readonly"
											placeholder="联系方式">
									</div>
								</div>
								</c:when>
							</c:choose>
								<div class="form-group">
									<label for="地址" class="col-lg-2 control-label"><span
										style="color: red;">*</span>地址</label>
									<div class="col-lg-8">
										<input name="address" id="address" value="${task.address}"  readonly = "readonly"
											type="text" class="form-control"  placeholder="地址">
									</div>
								</div>
						    <c:choose>
							    <c:when test="${task.lawObjectType == 6}">
									<div class="form-group">
										<label for="权属行政区" class="col-lg-2 control-label">权属行政区</label>
										<div class="col-md-2 col-lg-2 col-sm-2">
											<select class="form-control" id ="power_province" name ="power_province">
												<option value="35000000">福建省</option>
											 </select>
										</div>
										<div class="col-md-3 col-lg-3 col-sm-3">
											<select
													 class="form-control" id ="power_city" name ="power_city">
											 </select>
										</div>
										<div class="col-md-3 col-lg-3 col-sm-3">
											 <select
													 class="form-control" id ="power_county" name ="power_county">
											 </select>
										</div>
									 </div>
								 </c:when>
							</c:choose>
								<div class="form-group">
								<label for="地理坐标" class="col-md-2 control-label">地理坐标</label>
								<div class="col-lg-2">
												    <span
									style="color: red;">*</span>  经度<input class="form-control" id="mapJD"
														name="gisCoordinateX" placeholder="经度"
														value="${gisCoordinateX}">
								</div>
								<div class="col-lg-2">
												  <span
									style="color: red;">*</span>  纬度<input class="form-control" id="mapWD"
										name="gisCoordinateY" placeholder="纬度"
										value="${gisCoordinateY}">
								</div>
								<div class="col-lg-2">
								<!-- 	<button class="btn btn-info" type="button"
										data-toggle="modal" data-target="#myModal" id="createMap"
										onclick="createMapOnclick()" style="margin-top: 18px;">定位</button> -->
											<button class="btn btn-info" type="button" 
													 		 onclick="createMapClick('${task.address}','${gisCoordinateX}','${gisCoordinateY}')"
														style="margin-top: 18px;">定位</button> 
											<button type="button" class="btn btn-info" id="folder" style="margin-top: 18px;" data-container="body" tabindex="-1" data-toggle="popover" data-placement="bottom" data-html="true"
		 												data-content="请使用福建环境执法APP扫一扫定位<br>坐标。<button type='button' class='close' data-dismiss='modal' aria-hidden='true' style='margin-top:-20px;'>&times;</button>注意获取的是手机当前位置的<br>经纬度。
		 												<button type='button' class='btn btn-danger btn-xs' id='refreshQR' onclick='refreshQR()' style='float:right; margin-right:15px;'>刷新</button><br><div id='appQrCode' style=' width:230px; height:235px;padding-top:7px;'></div>">手机定位</button>
		 											<li id="getPositionSuccess" style="color:cornflowerblue; display:none;">坐标获取成功，请保存信息。</li>
								</div>
							</div>
							<c:choose>
								<c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="水源地编码" class="col-lg-2 control-label"><span
												 style="color: red;">*</span>水源地编码</label>
										 <div class="col-lg-8">
											 <input name="waterSourceCode" id="waterSourceCode" value=""  readonly = "readonly"
													type="text" class="form-control"  placeholder="水源地编码">
										 </div>
									 </div>
								</c:when>
							</c:choose>
							<c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="水源地级别" class="col-lg-2 control-label"><span style="color: red;">*</span>水源地级别 </label>
										 <div class="col-lg-8">
											 <select id="waterSourceLevel" class="form-control" name="waterSourceLevel">
<%--												 <option value="">请选择</option>--%>
<%--														 <option value="1"<c:if test="${task.waterSourceLevel=='1'}"> selected </c:if>>千吨万人</option>--%>
<%--														 <option value="2"<c:if test="${task.waterSourceLevel=='2'}"> selected </c:if>>千人以上</option>--%>
<%--														 <option value="3"<c:if test="${task.waterSourceLevel=='3'}"> selected </c:if>>20人(含)至千人</option>--%>
<%--														 <option value="4"<c:if test="${task.waterSourceLevel=='4'}"> selected </c:if>>20人以下</option>--%>
											 </select>
										 </div>
									 </div>
								 </c:when>
							</c:choose>
							<c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="水源地类别" class="col-lg-2 control-label"><span style="color: red;">*</span>水源地类别 </label>
										 <div class="col-lg-8">
											 <select id="waterSourceCategory" class="form-control" name="waterSourceCategory">
<%--												 <option value="">请选择</option>--%>
<%--													  <option value="1"<c:if test="${task.waterSourceCategory=='1'}"> selected </c:if>>湖库型</option>--%>
<%--													 <option value="2"<c:if test="${task.waterSourceCategory=='2'}"> selected </c:if>>河流型</option>--%>
<%--													 <option value="3"<c:if test="${task.waterSourceCategory=='3'}"> selected </c:if>>水库型</option>--%>
<%--													 <option value="4"<c:if test="${task.waterSourceCategory=='4'}"> selected </c:if>>地下水型</option>--%>
											 </select>
										 </div>
									 </div>
								 </c:when>
							</c:choose>
						    <c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="实际取水量（吨/日）" class="col-lg-2 control-label">实际取水量（吨/日）</label>
										 <div class="col-lg-8">
											 <input name="saterIntake" id="saterIntake" value="${task.saterIntake}"  readonly = "readonly"
													type="text" class="form-control"  placeholder="水源地编码">
										 </div>
									 </div>
								 </c:when>
							</c:choose>
						    <c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="实际供水人口（人）" class="col-lg-2 control-label">实际供水人口（人）</label>
										 <div class="col-lg-8">
											 <input name="waterSupply" id="waterSupply" value="${task.waterSupply}"  readonly = "readonly"
													type="text" class="form-control"  placeholder="实际供水人口（人）">
										 </div>
									 </div>
								 </c:when>
							</c:choose>
							<c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="责任单位 " class="col-lg-2 control-label">责任单位</label>
										 <div class="col-lg-8">
											 <input name="manageOrgName" id="manageOrgName" value="${task.manageOrgName}"  readonly = "readonly"
													type="text" class="form-control"  placeholder="责任单位">
										 </div>
									 </div>
								 </c:when>
							</c:choose>
						    <c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="责任人 " class="col-lg-2 control-label">责任人</label>
										 <div class="col-lg-8">
											 <input name="chargePerson" id="chargePerson" value="${task.chargePerson }"  readonly = "readonly"
													type="text" class="form-control"  placeholder="责任人">
										 </div>
									 </div>
								 </c:when>
							</c:choose>
						    <c:choose>
								 <c:when test="${task.lawObjectType == 6}">
									 <div class="form-group">
										 <label for="联系电话 " class="col-lg-2 control-label">联系电话</label>
										 <div class="col-lg-8">
											 <input type="text" id="legalPhone" name="legalPhone" value="${task.legalPhone }${lawObject.linkPhone }" class="form-control"  readonly = "readonly"
													placeholder="联系方式">
										 </div>
									 </div>
								 </c:when>
							</c:choose>
								 <div class="form-group">
									<label for="来源" class="col-lg-2 control-label">来源</label>
									<div class="col-lg-8">
										<c:choose>
											<c:when test="${task.taskFromType == 1 and status == 6}">
											<select class="form-control" id="taskFromName" name="taskFromName">
												<option value="监督性监测数据超标#6">监督性监测数据超标</option>
											</select>
											</c:when>
											<c:when test="${task.taskFromType !=1 or status ==4 or status==5}">
											<select class="form-control" id="taskFromName" name="taskFromName">
												<option value="专项检查#6">专项检查</option>
											</select>
											</c:when>
											<c:otherwise>
												<select class="form-control" id="taskFromName" name="taskFromName">
												<option value="">——请选择——</option>
														<c:forEach items="${taskSourceList}"  var="menu" varStatus="status" >
														<c:choose>
															<c:when test="${menu.askQuestion==1 and not empty menu.childrenList}">
																 <optgroup label="${menu.name}">
																	  <c:forEach items="${menu.childrenList}" var="secondChild" varStatus="status">
																		 <option value="${secondChild.name }#${secondChild.code}" <c:if test="${secondChild.code  ==task.taskFromCode }">selected</c:if>>${secondChild.name}</option>
																	  </c:forEach>
																</optgroup>
															</c:when>
															<c:otherwise>
																<c:if test="${menu.name =='测试信息' }">
																	<option style="color:red;"  value="${menu.name }#${menu.code}" <c:if test="${menu.code  ==task.taskFromCode }">selected</c:if>>${menu.name}</option>
																</c:if>
																<c:if test="${menu.name !='测试信息' }">
																	<c:if test="${menu.name !='固定污染源随机抽查' }">
																		<option value="${menu.name }#${menu.code}" <c:if test="${menu.code  ==task.taskFromCode }">selected</c:if>>${menu.name}</option>
																	</c:if>
																</c:if>
															</c:otherwise>
														</c:choose>
													</c:forEach>
												</select>
											</c:otherwise>
										</c:choose>
									</div>
								</div>

							<div class="form-group">
								<label for="监察类型" class="col-lg-2 control-label">监察类型</label>
								<div class="col-lg-8">
									<div class="input-group">
										<input type="text" id="monitorTypeName" name="monitorTypeName"  readonly = "readonly" 
											 class="form-control" placeholder="监察类型">
										<div class="input-group-btn">
											<button type="button" class="btn btn-info no-shadow"
												tabindex="-1" data-toggle="modal" data-target="#jclx">监察类型选择</button>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label for="检查人" class="col-lg-2 control-label"><span
									style="color: red;">*</span> 检查人</label>
								<div class="col-lg-8">
									<div class="input-group">
										<input type="text" id="checUserNames" name="checUserNames" readonly = "readonly" 
											 class="form-control"  value ="${task.checUserNames }">
										<div class="input-group-btn">
											<button type="button" class="btn btn-info no-shadow"
												id="CheckUserChooseBtn" tabindex="-1" data-toggle="modal"
												data-remote="${webpath}/jcbl/direct-chick-user-page"
												data-target="#jcr">检查人添加</button>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label for="执法证号" class="col-lg-2 control-label"><span
									style="color: red;">*</span> 执法证号</label>
								<div class="col-lg-8">	<input type="text"   readonly = "readonly"   id="lawEnforcIds" name="lawEnforcIds" 
								value="${task.lawEnforcIds }"
											class="form-control"  placeholder="执法证号">
								</div>
							</div>
							<div class="form-group">
								<label for="限办时间" class="col-lg-2 control-label">限办时间</label>
								<div class="col-lg-8"> 
									<input type="text"  id="limitTimeTemp" placeholder="限办时间"   readonly = "readonly" 
										name="limitTimeTemp" class="form-control"
										data-date-format="yyyy-mm-dd hh:ii" >
								</div>
							</div>
							<div class="form-group">
								<label for="关联专项行动" class="col-lg-2 col-sm-2 col-xs-5 control-label">关联专项行动</label>
								<div class="ol-lg-8 col-sm-8 col-xs-12">
										<div class="input-group">
										<input type="text"  id="specialActionNames"   name="specialActionNames" value ="${ task.specialActionNames }" readonly = "readonly" 
											class="form-control" placeholder="关联专项行动">
										<div class="input-group-btn">
											<button type="button" class="btn btn-info no-shadow"
												id="glzxxdBtn" tabindex="-1" data-toggle="modal"
												data-remote="${webpath}/jcbl/special-action-page"
												data-target="#glfrw">关联专项行动 </button>
										</div>
									</div>
									</div>
							</div>
							<div id="xslwwfxwDiv" style="display: none;">
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
								<div class="form-group">
									<label for="是否属于小散乱污企业" class="col-lg-2 control-label"><span style="color: red;">*</span>是否属于“小散乱污”企业 </label>
									<div class="col-lg-8">
										<select class="form-control" id="xslw" name="xslw">
												<!-- <option value="">请选择</option>	 -->
												<option value="1" <c:if test="${task.xslw eq '1'}">selected</c:if>>是</option>
												<option value="0" <c:if test="${empty task.xslw or task.xslw eq '0' }">selected</c:if>>否</option>
										</select>
									</div>
								</div>
								</c:when>
							</c:choose>

									<%-- <div class="form-group">
												<label for="违法行为涉及污染源类别" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label">违法行为涉及污染源类别 </label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
													<div class="input-group">
														<input type="hidden" id="littleCaseTypeCode" name="taskLlegalCode" value="${task.taskLlegalCode }">
														<input type="text" class="form-control"  id="littleCaseTypeName" name="taskLlegalName" readonly="readonly" placeholder="违法行为涉及污染源类别" value="${task.taskLlegalName }" >
														<div class="input-group-btn">
															<button type="button" data-remote="${webpath}/taskManager/illegal-case-type?code=littleCaseTypeCode&name=littleCaseTypeName&checkFormName=lawObjectSaveForm";
																class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#wfajlx">选择</button>
														</div>
													</div>
												</div>
									</div> --%>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
								 <div class="form-group">
									<label for="适用排污许可行业技术规范" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label"><span	style="color: red;">*</span>适用排污许可行业技术规范 </label>
									<div class="col-lg-8 col-sm-8 col-xs-12">
										<div class="input-group">
											<input type="hidden" id="sypwxkhyjsgfCode" name="sypwxkhyjsgfCode"  value ="${ task.sypwxkhyjsgfCode }">
											<input type="text" class="form-control"  id="sypwxkhyjsgfName" name="sypwxkhyjsgfName" value ="${ task.sypwxkhyjsgfName }" readonly="readonly" placeholder="适用排污许可行业技术规范"  >
											<div class="input-group-btn">
												<button type="button" data-remote="${webpath}/taskManager/wasteEmisson-page-model?id=sypwxkhyjsgfCode&name=sypwxkhyjsgfName&checkFormName=taskAllocationForm";
													class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#pwxkjsgf">选择</button>
											</div>
										</div>
									</div>
								</div>
								</c:when>
							</c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
                                <div class="form-group">
                                    <label for="inputtext3" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label">
                                        <span style="color: red;">*</span>固定污染源排污许可分类</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12">
                                        <select class="form-control" name="sewageClassify" id="sewageClassify" onchange="document.getElementById('sewageClassifyName').value=this.options[this.selectedIndex].text">
                                            <option value="">请选择</option>
                                            <c:forEach items="${SEWAGECLASSIFY}" var="sypwxkhyjsgf">
                                                <option value="${sypwxkhyjsgf.code}" <c:if test="${task.sewageClassify eq sypwxkhyjsgf.code}"> selected</c:if>>${sypwxkhyjsgf.name}</option>
                                            </c:forEach>
                                        </select>
                                        <input type="hidden" id="sewageClassifyName" name="sewageClassifyName" value="${task.sewageClassifyName}">
                                    </div>
                                </div>
								</c:when></c:choose>
							<c:choose>
								<c:when test="${task.lawObjectType != 6}">
                                <div class="form-group">
                                    <label for="inputtext3" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label">
                                        <span style="color: red;">*</span>管理类型</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12">
                                        <select id="selctval" class="form-control" name="managementType" onchange="manageSelect(this.options[this.selectedIndex].text)">
                                            <option value="">请选择</option>
                                            <option value="重点管理"<c:if test="${task.managementType=='重点管理'}"> selected </c:if>>重点管理</option>
                                            <option value="简化管理"<c:if test="${task.managementType=='简化管理'}"> selected </c:if>>简化管理</option>
                                            <option value="登记管理"<c:if test="${task.managementType=='登记管理'}"> selected </c:if>>登记管理</option>

                                        </select>
                                    </div>
                                </div>
								</c:when></c:choose>
                                <div id="manageShow">
								<c:choose>
									<c:when test="${task.lawObjectType != 6}">
									<div class="form-group">
										<label for="inputtext3" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label"><span style="color:red;">*</span>是否发证</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<select class="form-control" name="isCertification" id="isCertification">
												<option value="">请选择</option>
												<option value="已发证"<c:if test="${task.isCertification=='已发证'}"> selected </c:if>>已发证</option>
												<option value="未发证"<c:if test="${task.isCertification=='未发证'}"> selected </c:if>>未发证</option>
											</select>
										</div>
									</div>
									</c:when>
								</c:choose>
								<c:choose>
									<c:when test="${task.lawObjectType != 6}">
									<div class="form-group isCertificationShow" style="display: none;">
										<label for="inputtext3" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label"><span style="color:red;">*</span>许可证编号</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" placeholder="许可证编号" class="form-control"
												   id="licenseNumber" name="licenseNumber"
												   value="${task.licenseNumber}">
										</div>
									</div>
									</c:when>
								</c:choose>
								<c:choose>
									<c:when test="${task.lawObjectType != 6}">
									<div class="form-group isCertificationShow" style="display: none;">
										<label for="inputtext3" class="col-lg-2 col-sm-2 col-xs-5 col-md-2 control-label"><span style="color:red;">*</span>发证机构</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<select id="certifyingAuthority" class="form-control" name="certifyingAuthority">
												<option value="">请选择</option>
												<option value="0"<c:if test="${task.certifyingAuthority== '0'}"> selected </c:if>>市级环保部门</option>
												<option value="1"<c:if test="${task.certifyingAuthority== '1'}"> selected </c:if>>县级环保部门</option>
											</select>
										</div>
									</div>
									<div class="form-group isCertificationShow" style="display: none;">
										<label class="col-lg-2 control-label">发证时间</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" class="form-control" readonly="readonly" placeholder="发证时间" id="startDateStr" name="startDateStr" value="<fmt:formatDate value='${task.startDate }' pattern='yyyy-MM-dd'></fmt:formatDate>">
										</div>
									</div>
									<div class="form-group isCertificationShow" style="display: none;">
										<label class="col-lg-2 control-label">截止时间</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" class="form-control" readonly="readonly"  placeholder="截止时间" id="closeDateStr" name="closeDateStr" value="<fmt:formatDate value='${task.closeDate}' pattern='yyyy-MM-dd'></fmt:formatDate>">
										</div>
									</div>
                                </div></c:when></c:choose></div>

								<div class="form-group">
									<label class="col-lg-2 control-label"><span style="color:red;">*</span>现场执法时间</label>
									<div class="col-lg-5 no-padding">
										<div class="col-lg-6">
											<input type="text" class="form-control" readonly="readonly" placeholder="开始时间" id="lawEnforcementStartTime" name="lawEnforcementStartTime" value="<fmt:formatDate value='${task.lawEnforcementStartTime }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
										</div>
										<div class="col-lg-6">
											<input type="text" class="form-control" readonly="readonly" placeholder="结束时间" id="lawEnforcementEndTime" name="lawEnforcementEndTime" value="<fmt:formatDate value='${task.lawEnforcementEndTime }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
										</div>
									</div>
								</div>
							</form>
								</div>
								<div class="modal-footer">
									<button type="button" style="width:100px;" class="btn btn-info" id ="submitSaveTask" data-dismiss="modal">保存</button>
									</div>
								</div>
						</div>
                    </div>
                </div>					
            </div>
        </div>
	<!-- 执法对象选择（Modal） -->
		<div class="modal fade" id="zfdx" tabindex="-1"
					role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
					<div class="modal-dialog  modal-lg">
						<div class="modal-content"></div>
					</div>
		</div>
	<!-- 执法对象选择（Modal） -->
	<!-- 监察类型选择（Modal） -->
	<div class="modal fade" id="jclx" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<div style="float:right; margin-top:-5px;">
                    <button type="button"  id="jclxxzBtn" class="btn btn-info">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
					<h4 class="modal-title" id="myModalLabel">监察类型</h4>
				</div>
				<div class="modal-body padding-lg">
					<div class="smart-widget-body form-horizontal">
							<div class="form-group">
								<div class="col-lg-12">
									<c:forEach items="${monitorTypeList}" var="monitorTypeList">
										<div class="checkbox inline-block"
											style="padding-right: 30px;">
											<div class="custom-checkbox">
												<input type="checkbox" name="monitorTypeCodeList"
													value=" ${monitorTypeList.name}#${monitorTypeList.code}"
													id="monitorType+${monitorTypeList.code}"> <label
													for="monitorType+${monitorTypeList.code}"
													class="checkbox-blue"> </label>
											</div>
											<div class="inline-block vertical-top">
												${monitorTypeList.name}</div>
										</div>
									</c:forEach>
								</div>
							</div>
					</div>
				</div>
				<div class="modal-footer">
					<!--<button type="button"  id="jclxxzBtn" class="btn btn-info">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
				</div>
			</div>
		</div>
	</div>
	<!-- ./监察类型选择（Modal） -->
		<!-- ./检查人选择（Modal） -->
			<div class="modal fade" id="jcr" tabindex="-1" role="dialog" 
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
	<!-- ./检查人选择（Modal） -->
	<!-- ./坐标（Modal） -->
	<div class="modal fade" id="myModal" role="dialog" class="tangram-suggestion-main"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
            	 
			</div>
		</div>
	</div>
			<!-- 违法案件类型（Modal） -->
	<div class="modal fade" id="wfajlx" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content"></div>
		</div>
	</div>
	<div class="modal fade" id="pwxkjsgf" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" >

			</div>
		</div>
	</div>
		<!-- 专项行动选择（Modal） -->
			<div class="modal fade" id="glfrw" tabindex="-1"
					role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
					<div class="modal-dialog  modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
				
		<!-- ./坐标（Modal） -->
				<script type="text/javascript">
                    var flag = '${flag}';
					var belongAreaId =  $("#belongAreaId").val();
					console.log(belongAreaId)
				function setMapValue() {
					if ($("#new_map_txt").html() == "") {
						swal({title:"错误!",text:"你还没选择相应的坐标点。", type:"warning",allowOutsideClick :true}); 
						return false;
					}
					var mapJW = $("#new_map_txt").html().split(",");
					$("#mapJD").val(mapJW[0]);
					$("#mapWD").val(mapJW[1]);
					$('#taskAllocationForm').formValidation('revalidateField', 'gisCoordinateX');
					$('#taskAllocationForm').formValidation('revalidateField', 'gisCoordinateY');
					$('#myModal').modal('hide')
				}
				//返回上一步主菜单
				 $(function(){
			      //监听回退键
			      business.listenBackSpace();
					 $("#startDateStr").datetimepicker({
						 format : 'yyyy-mm-dd',
						 todayBtn : true,
						 /*clearBtn:true,*/
						 language: 'zh-CN',
						 autoclose : true
					 }).on("changeDate",function(ev){
						 $("#closeDateStr").datetimepicker('setStartDate',new Date($("#startDateStr").val()));
						 $('#taskAllocationForm').formValidation('revalidateField', 'startDateStr');

						 if ($("#closeDateStr").val() && $("#startDateStr").val() > $("#closeDateStr").val()) {
							 swal({title:"提示", text:"发证时间不能晚于截止时间!", type:"error",allowOutsideClick :true},function(){
								 $("#startDateStr").val("");
							 });
							 return;
						 }
					 });
					 $("#closeDateStr").datetimepicker({
						 format : 'yyyy-mm-dd',
						 todayBtn : true,
						 /*clearBtn:true,*/
						 language: 'cn',
						 autoclose : true
					 }).on("changeDate",function(ev){
						 $("#startDateStr").datetimepicker('setEndDate',new Date($("#closeDateStr").val()));
						 $('#taskAllocationForm').formValidation('revalidateField', 'closeDateStr');
						 if ($("#startDateStr").val() && $("#startDateStr").val() > $("#closeDateStr").val()) {
							 swal({title:"提示", text:"截止时间不能早于发证时间!", type:"error",allowOutsideClick :true},function(){
								 $("#closeDateStr").val("");
							 });
							 return;
						 }
					 });
			     });
				function createMapClick(address,gisCoordinateX,gisCoordinateY){
					 var urlBaidu = encodeURI(WEBPATH+'/taskManager/baidu-ditu?address='+address+'&gisCoordinateX='+gisCoordinateX+'&gisCoordinateY='+gisCoordinateY);
					  var options = {
								remote:urlBaidu
					  };
					 $('#myModal').modal(options);
				}
				$(function(){
					var lawObjectType = $("#lawObjectType").val();
					if(lawObjectType!=null && lawObjectType!='' && lawObjectType!='4'){
						$("#xslwwfxwDiv").show();
					}
				//初始化判断是否我有参数请求 基本信息可编辑
				var lawObjectId = $("#lawObjectId").val();
				if(lawObjectId !=null && lawObjectId != ""){
					 $("#cardTypeName").removeAttr("disabled");
			           $("#linkman").removeAttr("readonly");
			           $("#legalPhone").removeAttr("readonly");
			           $("#address").removeAttr("readonly");
			           $("#cardCode").removeAttr("readonly");
				}
						function indexOf(arr,val){
						    for(var i = 0; i < arr.length; i++){
						        if(arr[i] == val){return i;}
						    }
						    return -1;
						}

						function array_contain(array, obj){
						    for (var i = 0; i < array.length; i++){
						        if (array[i] == obj)//如果要求数据类型也一致，这里可使用恒等号===
						            return true;
						    }
						    return false;
						}
						   function isContains(str, substr) {
							    return str.indexOf(substr) >= 0;
							};
					       var EndTime = ''; var startTime = ''
					$("#lawEnforcementStartTime").datetimepicker({
							   format : 'yyyy-mm-dd hh:ii:ss',
									todayBtn : true,
									/*clearBtn:true,*/
									language: 'cn',
									autoclose : true
							    }).on("changeDate",function(ev){
							    	$("#lawEnforcementEndTime").datetimepicker('setStartDate',new Date($("#lawEnforcementStartTime").val()));
									$('#taskAllocationForm').formValidation('revalidateField', 'lawEnforcementStartTime');

									if($("#lawEnforcementStartTime").val().substr(0,10)>dateToString(new Date(),"1").substr(0,10)){
										swal({title:"提示", text:"开始时间不能晚于当前系统时间!", type:"error",allowOutsideClick :true},function(){
											$("#lawEnforcementStartTime").val(dateToString(new Date(),"1"));
										});
									}
							      });
						   $("#lawEnforcementEndTime").datetimepicker({
							   format : 'yyyy-mm-dd hh:ii:ss',
									todayBtn : true,
									/*clearBtn:true,*/
									language: 'cn',
									autoclose : true
						        }).on("changeDate",function(ev){
							    	$("#lawEnforcementStartTime").datetimepicker('setEndDate',new Date($("#lawEnforcementEndTime").val()));
							        $('#taskAllocationForm').formValidation('revalidateField', 'lawEnforcementEndTime');
							        // $("#lawEnforcementStartTime").val(dateToString(new Date(),"1"));
							        if ($("#lawEnforcementEndTime").val() < $("#lawEnforcementStartTime").val()) {
									   swal({title:"提示", text:"结束时间不能早于开始时间!", type:"error",allowOutsideClick :true});
										   // alert('结束时间不能小于开始时间！');
										   return;
									}
							        if($("#lawEnforcementEndTime").val().substr(0,10)>dateToString(new Date(),"1").substr(0,10)){
										swal({title:"提示", text:"结束时间不能晚于当前系统时间!", type:"error",allowOutsideClick :true},function(){
											$("#lawEnforcementEndTime").val(dateToString(new Date(),"1"));
										});
									}
						       });
							/**
							 *日期转字符串
						     */
						   function dateToString(now,type){
							    var year = now.getFullYear();  
							    var month =(now.getMonth() + 1).toString();  
							    var day = (now.getDate()).toString();  
							    var hour = (now.getHours()).toString();  
							    var minute = (now.getMinutes()).toString();  
							   	var second = (now.getSeconds()).toString();  
							    if (month.length == 1) {  
							        month = "0" + month;  
							    }  
							    if (day.length == 1) {  
							        day = "0" + day;  
							    }  
							    if (hour.length == 1) {  
							        hour = "0" + hour;  
							    }  
							    if (minute.length == 1) {  
							        minute = "0" + minute;  
							    }  
							    if (second.length == 1) {  
							        second = "0" + second;  
							    }
							     var dateTime = year + "-" + month + "-" + day +" "+hour+":"+minute+":"+second;  
							     return dateTime;  
							  }
							if($("#lawEnforcementStartTime").val()!=null && $("#lawEnforcementStartTime").val()!=''){
								
							}else{	
								$("#lawEnforcementStartTime").val(dateToString(new Date(),"1"));//获取系统当前时间
							}
							if($("#lawEnforcementEndTime").val()!=null && $("#lawEnforcementEndTime").val()!=''){
								
							}else{	
								$("#lawEnforcementEndTime").val(dateToString(new Date()),"0");//获取系统当前时间
							}
						 //限办日期 插件加载
						   $("#limitTimeTemp").datetimepicker({
								 format:'yyyy-mm-dd',
									todayBtn : true,
									clearBtn:true,
									language: 'cn',
									autoclose : true,
									minView : 'month',
									maxView : 'decade',
							        startDate:new Date()
							    }).on("changeDate",function(ev){
									   $('#taskAllocationForm').formValidation('revalidateField', 'limitTimeTemp');
							      });
							//保存 提交表单
							$('#submitSaveTask').click(function() {
								loding('submitSaveTask',"保存");
								 $("#taskAllocationForm").data('formValidation').validate();
								var validate = $("#taskAllocationForm").data('formValidation').isValid();
						        if(validate){
									var options = {
											url : WEBPATH + '/jcbl/saveLocaleLaw ',
											type : 'post',
											async:false,
											success : function(data) {
												if (data.meta.result == "success") {
													swal({
														title : "提示",
														text : "发起现场执法成功!",
														type : "success",},
														function(isConfirm){
															 var obj={taskFlowId:data.data.taskFlowId,taskId:data.data.taskId,lawObjectType:data.data.lawObjectType,parentUrl:data.data.status,nodeCode:data.data.nodeCode};
													         business.addMainContentParserHtml(WEBPATH+'/taskManager/start-handle-task?selectType=0&menuId='+'${menuId}',obj);
													});
													return false;
												}else if(data.meta.code == '007'){
									                swal({ title : data.meta.message, text : "", type : "info",allowOutsideClick :true });
									            }else {
													swal({title:"操作失败", text:"保存失败!", type:"error",allowOutsideClick :true});
												}
										},
										error :function(){
											swal({title:"操作失败", text:"保存失败!", type:"error",allowOutsideClick :true});
										}
									}
										swal({
										  title: "提示?",
										  text: "请确认是否将执法对象信息回写执法对象库!",
										  type: "warning",
										  showCancelButton: true,
										  confirmButtonColor: "#DD6B55",
										  confirmButtonText: "是",
										  cancelButtonText: "否,继续",
										  closeOnConfirm: false,
										  closeOnCancel: false
										},
										function(isConfirm){
										  if (isConfirm) {
											 //会写当事人信息
											// swal.close();
										//	 business.openwait();//开启遮罩
											  $("#updateObjectState").val("1");
											  $('#taskAllocationForm').ajaxSubmit(options);
										//     business.closewait();//关闭遮罩层
											  
										  }else{
											  //不会写当事人信息
									//		swal.close();
										//	business.openwait();//开启遮罩
											  $("#updateObjectState").val("0");
											  $('#taskAllocationForm').ajaxSubmit(options);
									//		business.closewait();//关闭遮罩层
										  }
										  })
								  //    $('#taskAllocationForm').ajaxSubmit(options);
										return  true;
						        }else {
							         //表单未填写
							        $("#taskAllocationForm").data('formValidation').validate();
							         return false;
						     } 
							});
							//表单非空验证
						    $("#taskAllocationForm").formValidation({
						        framework: 'bootstrap',
						        message: 'This value is not valid',
						        icon:{
							            valid: 'glyphicon glyphicon-ok',
							            invalid: 'glyphicon glyphicon-remove',
							            validating: 'glyphicon glyphicon-refresh'
						               },
						        fields: {
						        	 lawObjectName: {
						                message: '执法对象名称不能为空！',
						                validators: {
						                    notEmpty: {
						                        message: '执法对象名称不能为空！'
						                    }
						                
						            }
						        	},
						        	checUserNames:{
						                message: '检查人不能为空！',
						                validators: {
						                    notEmpty: {
						                        message: '检查人不能为空！'
						                    },
						                    callback: {
							                    message: "检查人至少选择在2个，最多选择20个！",
							                    callback: function(value, validator, $field) {
							                		if(value!=""){
							                				var arr = value.split(",");
							                				if(arr.length>=2 && arr.length<=20){
							                					return true;
							                				}else{
							                					return false;
							                				}
							                			} else{
															return false;
							                			}
							                    }
							                }
						                }
						        	},specialActionNames : {
						        		 validators: {
						        			 callback: {
						        		  message: "专项行动最多选择20个！",
						                  callback: function(value, validator, $field) {
						              		if(value!=""){
						              				var arr = value.split(",");
						              				if(arr.length<=20){
						              					return true;
						              				}else{
						              					return false;
						              				}
						              			} else{
														return true ;
						              			}
						                  }
						        		 }}
						        		 
						        	},
						        	taskDesc:{
						        		validators: {
							                stringLength: {
							                	max: 1000,
							                	message: '该项不能超过1000个字符.'
							                }
							            }
						        	},cardCode : {
										validators : {
											regexp: {
						                        regexp: /^((?![A-Z]+$)[0-9A-Z]{18}$|[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)/,
						                        message: '证照号格式不正确!'
						                    }
										}
									},
									linkman : {
										validators : {
									 
											stringLength : {
												max : 50,
												message : '联系人最大50个字符'
											},
										    callback: {
							                    message: "联系人不能为空！",
							                    callback: function(value, validator, $field) {
							                    	var lawType = '${task.lawObjectType}';
							                		if(value=="" && lawType!=5){
														return false;
							                		}else{
							                			return true;
							                		}
							                    }
							                }
										}
									},
									legalPhone : {
										   validators: {
							                    regexp: {
													regexp: /(^(\d{3,4}-)?\d{7,8})$|(^1[3|4|5|6|7|8|9]\d{9}$)/,
							                    	  message:'请输入正确的联系号码',
							                    },
											   notEmpty: {
												   message: '联系方式不能为空！'
											   }
							                 }
									},gisCoordinateY : {
										validators : {
											notEmpty: {
						                        message: '经度纬度不能为空！'
						                    },
											stringLength : {
												max : 50,
												message : '维度最大50个字符'
											}

										}
									},
									gisCoordinateX : {
										validators : {
											notEmpty: {
						                        message: '经度纬度不能为空！'
						                    },
											stringLength : { 
												max : 50,
												message : '经度 最大50个字符'
											}

										}
									},
									address : {
										validators : {
											notEmpty: {
						                        message: '地址不能为空！'
						                    },
											stringLength : {
												max : 500,
												message : '地址最大500个字符'
											}
										}
									},	specialActionNames : {
						        		 validators: {
						        			 callback: {
						        		  message: "专项行动最多选择20个！",
						                  callback: function(value, validator, $field) {
						              		if(value!=""){
						              				var arr = value.split(",");
						              				if(arr.length<=20){
						              					return true;
						              				}else{
						              					return false;
						              				}
						              			} else{
														return true ;
						            			      }
						        				 }
						        			 }	 
						    		  }
								},sypwxkhyjsgfName : {
									validators : {
										notEmpty: {
					                        message: '适用排污许可行业技术规范不能为空！'
					                    }

									}
								},sewageClassify: {
                                        message: '固定污染源排污许可分类不能为空！',
                                        validators: {
                                            notEmpty: {
                                                message: '固定污染源排污许可分类不能为空！'
                                            }
                                        }
                                    },
                                    managementType: {
                                        message: '管理类型不能为空！',
                                        validators: {
                                            notEmpty: {
                                                message: '管理类型不能为空！'
                                            }
                                        }
                                    },isCertification: {
                                        message: '是否发证不能为空！',
                                        validators: {
                                            notEmpty: {
                                                message: '是否发证不能为空！'
                                            }
                                        }
                                    },
                                    certifyingAuthority: {
                                        message: '发证机构不能为空！',
                                        validators: {
                                            notEmpty: {
                                                message: '发证机构不能为空！'
                                            }
                                        }
                                    },licenseNumber: {
                                        message: '许可证编号不能为空！',
                                        validators: {
                                            regexp:{
												regexp:/^(?![A-Z]+$)[0-9A-Z]{22}$/,
                                                message: '许可证编号只允许22位大写字母+数字类型',
                                            },
                                            notEmpty: {
                                                message: '许可证编号不能为空！'
                                            }
                                        }
                                    },lawEnforcementStartTime : {
										validators : {
											notEmpty: {
						                        message: '现场执法开始时间不能为空！'
						                    }

										}
									},
									lawEnforcementEndTime : {
										validators : {
											notEmpty: {
						                        message: '现场执法结束时间不能为空！'
						                    }

										}
									}
							}					        
						    });
						   //设置传入参数
						        function queryParams(params) {
						              var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
						                  pageNum: params.pageNumber,
						                  pageSize: params.pageSize,
						              };
						              return temp;
						          }
						        
						  	//检查人条件查询
						  	$("#checkPersonBtn").click(function() {
						  		$("#checkPersonStatus").val("1");
						  		$('#checkUserChooseTable').bootstrapTable('refresh');
						  	});
						  	
					//检查类型选择 确认按钮点击，模态框数据加载到父页面	  
				$("#jclxxzBtn").click(function(){
						var arr = new Array();
						$('input[name="monitorTypeCodeList"]:checked').each(function() {
							arr.push($(this).val());
						});
						var monitorTypeCode = new Array();
						var monitorTypeName = new Array();
						for (var i = 0; i < arr.length; i++) {
							var temp = arr[i].split("#");
							monitorTypeName.push(temp[0]);
							monitorTypeCode.push(temp[1]);
						}
						$("#monitorTypeName").val(monitorTypeName.join(','));
						$("#monitorTypeCode").val(monitorTypeCode.join(','));
						//隐藏模态框
						$('#jclx').modal('hide');
					});
	
				//证件类型改变，清空证件号码
				$("#cardTypeName").change(function(){
					$("#cardCode").val("");
				});
				})
				</script>
<script>
	$(function () { 
		$("#folder").popover('destroy').popover();
	});
	var flag;//定时发送请求任务
	var time;//时间戳
	var uuid = business.guid();//每个页面一个单独的uuid
	
	$("#folder").on('shown.bs.popover', function(){
		//开启遮罩，重新打开持续请求后台坐标数据
		business.openwait('1');//传入1，表示不要中间的转圈
		clearInterval(flag);
		//每隔1.5秒请求后台坐标数据
		flag = setInterval(function(){
			$.ajax({
                type: "POST",
                async:false,
                url: WEBPATH+'/taskManager/getPositionInfo',
                data:{uuid:uuid, time:time},
                error: function(request) {
                	//swal("错误!","获取位置信息失败！", "error");
                	//一次请求错误不做处理，等待1分钟后超时
                },
                success: function(data) {
                	if(data.meta.statusCode ==  '200'){
                		 var position = data.data;
                		 if(position != null && position != '' && position != 'undefined'){
	                		 var gisCoordinateX = position.split(',')[0];
	                		 var gisCoordinateY = position.split(',')[1];
	                		 $("[name='gisCoordinateX']").val(gisCoordinateX);
	                		 $("[name='gisCoordinateY']").val(gisCoordinateY);
	                		 clearInterval(flag);
	                		 time='';
	                		 $('#appQrCode').empty();
	                		 $('#folder').popover('hide'); 
	                		 document.getElementById('getPositionSuccess').style.display='block';
	                		 setTimeout(function(){
	                			 document.getElementById('getPositionSuccess').style.display='none';
	                		 },5000)
                		 }
                	}else{
                		//swal("错误!","获取位置信息失败！", "error");
                		//一次请求错误不做处理，等待1分钟后超时
                	}
                }
            });
		},1500);
	})
	
	$('#folder').on('show.bs.popover', function () {
		setTimeout(function(){
			//$('#folder').popover('hide');
			$('#appQrCode').empty();
			$('#appQrCode').html("正在生成二维码……");
			setSysTime();
			buildQR(time,uuid);
		},10);
	})
	$('#folder').on('hide.bs.popover', function () {
		//隐藏时关闭遮罩，关闭间隔请求，关闭超时提示，清空二维码
		business.closewait();
		clearInterval(flag);
		$('#appQrCode').empty();
	})
	
	//监听点击事件
    $('body').on('click', function(event) {  
		var target = $(event.target); 
		if (!target.hasClass('popover')  
				&& target.parent().attr('id') != 'appQrCode'
				&& target.parent('.popover-content').length === 0
				&& target.parent('.myPopover').length === 0
				&& target.parent('.popover-title').length === 0
				&& target.parent('.popover').length === 0 && target.attr("id") !== "folder") {  
			$('#folder').popover('hide');  
		}
		if(target.hasClass('close')){
			$('#folder').popover('hide');
		}
	}); 

	//刷新二维码
	function refreshQR(){
		setSysTime();
		buildQR(time,uuid);
	}
	
	//设置time为系统时间
	function setSysTime(){
		$.ajax({
            type: "POST",
            async:false,//同步获取时间戳
            url: WEBPATH+'/api/auth/getCurTime',
            error: function(request) {
            	swal({title:"提示!",text:"获取系统时间失败，二维码生成失败！", type:"warning",allowOutsideClick :true});
            	$('#folder').popover('hide');  
            },
            success: function(data) {
            	if(data.meta.httpStatusCode ==  '200'){
            		 time=data.data.curtime;
            	}else{
            		swal({title:"提示!",text:"获取系统时间失败，二维码生成失败！", type:"warning",allowOutsideClick :true});
            		$('#folder').popover('hide');  
            	}
            }
        });
	}
	
	//生成二维码
	function buildQR(time,uuid){
		$('#appQrCode').empty();
		var QRtext = {appLogo:'CHN-FJ',type:'010',time:time,uuid:uuid,url:'/api/taskManager/appGISInfo'};
		$('#appQrCode').qrcode({
			render : 'canvas',
			text : JSON.stringify(QRtext),
			height : 225,
			cache:false,
			width : 225,
			foreground: "#23b7e5"
		});
	}


	function goBack(preUrl) {
		if(preUrl != null && preUrl != '' && preUrl != 'undefined' && flag != '2'){
			business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId="+'${menuId}', $("#searchForm").serialize());
		} else if (flag == '2') {
            business.addMainContentHtml(WEBPATH + '/overlaw/overStandardSupervision?pageNumber=1&pageSize=10', null);
		} else {
			swal({
				title : "提示！",
				text : "返回信息错误，请刷新后重试。",
				type : "error",
				allowOutsideClick :true
			})
		}
	}
	
    </script>
    <%--新加字段 管理类型、发证--%>
    <script type="text/javascript">
        $(function(){
            var ismanageCode = '${task.managementType}';
            if(ismanageCode == '登记管理' || ismanageCode == ''){
                $("#manageShow").hide();
                $("#licenseNumber").val('');
                $("#certifyingAuthority").val('');
                $("#isCertification").val('');
            }else{
                $("#manageShow").show();
            }
            var isCertificationCode = '${task.isCertification}';
            if(isCertificationCode == '已发证'){
                $(".isCertificationShow").show();
            }else{
                $(".isCertificationShow").hide();
                $("#licenseNumber").val('');
                $("#certifyingAuthority").val('');
            }
            $("#isCertification").change(function(){
                if($(this).val() == "已发证"){
                    $(".isCertificationShow").show();
                }else{
                    $(".isCertificationShow").hide();
                    $("#licenseNumber").val('');
                    $("#certifyingAuthority").val('');
                }
            })
        })
        function manageSelect(value) {
            if(value == '登记管理' || value == '请选择'){
                $("#manageShow").hide();
                $("#licenseNumber").val('');
                $("#certifyingAuthority").val('');
                $("#isCertification").val('');
            }else{
                $("#manageShow").show();
            }
        }
    </script>
</html>
