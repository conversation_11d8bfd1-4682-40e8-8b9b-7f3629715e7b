<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<sec:authentication property="principal" var="authentication"/>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script
	src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js" type="text/javascript"></script>
	<%-- <link rel="stylesheet" href="${webpath }/static/css/bootstrap.min.css"> --%>
<title>Insert title here</title>
		<style type="text/css">
			.fixed-table-container {
				white-space : nowrap;
			}
		</style>
</head>
<body class="overflow-hidden">
	<jsp:include page="function.jsp"></jsp:include>
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
	<div  class="main-container">
				<div class="padding-md">
                	<div class="row">
						<div class="col-md-12">
							<div class="smart-widget padding-xs text-right">
                                <a href="#" class="btn btn-info" style="width:100px; margin-right:10px;" onclick="goback('${ajtzConfigUrl}','${ajtzConfigParams }')">返回</a>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-12">
							<div class="smart-widget">
								<div class="smart-widget-inner">
									<div class="smart-widget-body form-horizontal">
                                    	<div class="form-group">
                                            <label class="control-label col-md-2 col-sm-2">行政区划</label>
                                            <div class="col-md-1 col-sm-2">
                                                <select class="form-control"  disabled="true">
													<option value="35000000">福建省</option>
												</select>
                                            </div>
                                            <div class="col-md-2 col-sm-2">
                                                <select class="form-control" id="belongCity" name="belongCity">
                                       			</select>
                                            </div>
                                            <div class="col-md-2 col-sm-2">
                                                 <select class="form-control" id="belongCountry" name="belongCountry">
                                                 </select>
                                            </div>
                                           <div class="col-md-1 col-sm-2">
		                                       <div class="custom-checkbox" style="margin:7px auto;">
		                                       	<input type="hidden" id="isLevelCity" name="isLevelCity" value="0">
		                                           <input type="checkbox" id="checkLevel">
		                                           <label for="checkLevel" class="checkbox-blue"></label>
		                                       </div> 只选本级
                                   			</div>
                                        </div>
                                        <div class="form-group">
                                        	<label class="control-label col-md-2">案件名称</label>
                                            <div class="col-md-3">
                                               <input type="text" placeholder="案件名称" class="form-control"
										data-parsley-required="true" id="caseName" name="caseName">
                                            </div>
                                            <label class="control-label col-md-2">调查机构</label>
                                            <div class="col-md-3">
                                            	<input type="text" placeholder="调查机构" class="form-control" id="researchOrgName" name="researchOrgName" data-parsley-required="true">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                        	<label class="control-label col-md-2">综合案件编号</label>
                                            <div class="col-md-3">
                                                <input type="text" placeholder="综合案件编号" class="form-control"
											data-parsley-required="true" id="mainCaseNumber" name="mainCaseNumber">
                                            </div>
                                            <label class="control-label col-md-2">案件状态</label>
                                            <div class="col-md-3">
                                            	<select class="form-control" id="caseState" name="caseState">
													<option value="">——请选择——</option>
													<option value="0">进行中</option>
													<option value="1">已办结</option>
												</select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">当事人名称</label>
                                            <div class="col-md-3">
                                             <input type="text" placeholder="涉案当事人" class="form-control"
										data-parsley-required="true" id="lawObjName" name="lawObjName">
                                            </div>
                                            <label class="control-label col-md-2">案件创建时间</label>
                                            <div class="col-md-3" style="padding:0px;">
                                                <div class="col-md-6">
                                                <input type="text" placeholder="开始时间" class="form-control" id="createTimeStart" name="createTimeStart" data-parsley-required="true">
                                                </div>
                                                <div class="col-md-6">
                                                <input type="text" placeholder="结束时间" class="form-control" id="createTimeEnd" name="createTimeEnd" data-parsley-required="true">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                        	<label class="control-label col-md-2">案由</label>
                                            <div class="col-lg-3 col-sm-6 col-xs-6">
                                            	<select class="form-control" id="caseReason" name="caseReason">
                                                    <option value="">——请选择——</option>
                                                    <c:forEach items="${caseReason }" var="obj" varStatus="status">
                                                       <option value="${obj.code }">${obj.name }</option>
                                                    </c:forEach>
                                                </select>
                                                <!--<div class="input-group">
                                                <input type="yyzzzjh" class="form-control" id="yyzzzjh" placeholder="案由">

                                                    <div class="input-group-btn">
                                                        <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#anyou">选择</button>
                                                    </div>
                                                </div>-->
                                            </div>
                                            <label class="control-label col-md-2">处理主体</label>
                                            <div class="col-md-3 col-lg-3 col-sm-3">
		                                       <div class="input-group">
		                                        <input type="text"  disabled="disabled" class="form-control" name="handlingUnitName" id="handlingUnitName"  placeholder="处理主体" ></input>
		                                        <input type="hidden" id="handlingUnitId" name="handlingUnitId"  value="">
		                                           <div class="input-group-btn">
		                                               <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#handlingUnitModeler">选择</button>
		                                           </div>
		                                       </div>
		                                   </div>
                                        </div>
                                        <div class="form-group">
                                        	<label class="control-label col-md-2">关联专项行动</label>
                                           <%--  <div class="col-md-3">
                                                <select class="form-control" id="specialAction" name="specialAction">
                                                    <option value="">——请选择——</option>
                                                    <c:forEach items="${specialActions }" var="obj" varStatus="status">
                                                        <option value="${obj.id }">${obj.specialName }</option>
                                                    </c:forEach>
                                                </select>
                                            </div>   --%>
                                            <div class="col-md-3 col-lg-3 col-sm-3">
                                                <div class="input-group">
                                                <input type="text" disabled="disabled"  class="form-control" id="specialActionNames"  name="specialActionNames" placeholder="关联专项行动">
                                                <input type="hidden" id="specialActionIds" name="specialActionIds" >
                                                    <div class="input-group-btn">
                                                        <button type="button" id="glzxxdBtn" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#glfrw">选择</button>
                                                   </div>
                                                </div>
                                            </div>
                                            <label class="control-label col-md-2">违法类型</label>
                                            <div class="col-lg-3 col-sm-6 col-xs-6">
                                            	<select class="form-control" id="illegalType" name="illegalType">
                                                    <option value="">——请选择——</option>
                                                   <c:forEach items="${illegalTypes }" var="obj" varStatus="status">
                                                       <option value="${obj.id }">${obj.illegaName }</option>
                                                   </c:forEach>
                                                </select>
                                                <!--<div class="input-group">
                                                <input type="yyzzzjh" class="form-control" id="yyzzzjh" placeholder="违法类型">

                                                    <div class="input-group-btn">
                                                        <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#ajglwflx">选择</button>
                                                    </div>
                                                </div>-->
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">有无简易行政处罚</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isSmr" name="isSmr">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">有无一般行政处罚</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isCml" name="isCml">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">有无行政命令</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isAstn" name="isAstn">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">有无限产停产</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isLmt" name="isLmt">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">有无查封扣押</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isAtt" name="isAtt">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">有无移送行政拘留</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isDet" name="isDet">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">有无移送涉嫌犯罪</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isEvmt" name="isEvmt">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">有无按日连续处罚</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isDay" name="isDay">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">有</option>
                                                    <option value="0">无</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                           <label class="control-label col-md-2">是否对接到环保部</label>
											<div class="col-md-3">
												<select class="form-control" id="isSubmit" name="isSubmit">
													<option value="">——请选择——</option>
													<option value="1">是</option>
													<option value="0">否</option>
												</select>
											</div>
                                            <label class="control-label col-md-2">首次对接成功时间</label>
											<div class="col-md-3" style="padding:0px;">
												<div class="col-md-6">
													<input type="text" placeholder="开始时间" class="form-control" readonly="readonly"
														data-parsley-required="true" id="searchDateStart3" name="searchDateStart3">
												</div>
												<div class="col-md-6">
													<input type="text" placeholder="结束时间" class="form-control" readonly="readonly"
														data-parsley-required="true" id="searchDateEnd3" name="searchDateEnd3">
												</div>
											</div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">特殊监管行业</label>
                                            <div class="col-md-3">
                                                <select class="form-control" name="specialSuperviseIndustry">
                                                    <option value="">全部</option>
                                                    <option value="涉（煤）焦油">涉（煤）焦油</option>
                                                    <option value="两高">"两高"</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2 col-lg-2 col-sm-2">行业</label>
                                            <div class="col-md-3 col-lg-3 col-sm-3">
                                                <div class="input-group">
                                                    <input type="hidden" class="form-control" data-parsley-required="true" id='industryTypeCode' name="industryTypeCode">
                                                    <input type="text" placeholder="行业" readonly="readonly" class="form-control" data-parsley-required="true" id='industryTypeName' name="industryTypeName">
                                                    <div class="input-group-btn">
                                                        <button type="button" class="btn btn-info no-shadow"
                                                                tabindex="-1" data-toggle="modal"
                                                                data-remote="${webpath}/zfdx/enterprises-industry-type-page"
                                                                data-target="#Industrytype">选择</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-10 text-right">
                                            <a href="javascript:void(0)" class="btn btn-info" style="width:120px;" onclick="dajSearch()">查询</a>
                                            </div>
                                        </div>

									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
                        <div class="col-md-12">
                        	<div class="smart-widget-body form-horizontal">
                                <div class="form-group" >
                                    <div class="col-md-12 text-right">
                                       <button class="btn btn-info" onClick="downloadExcelTask()" type="submit"
								style="width: 100px; margin-right: 10px;">导出EXCEL</button>
                                    </div>
                                </div>
                            </div>
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 综合案件台账详细列表
                                    <span class="smart-widget-option">
                                        <span class="refresh-icon-animated">
                                            <i class="fa fa-circle-o-notch fa-spin"></i>
                                        </span>
                                        <a href="#" class="widget-toggle-hidden-option">
                                            <i class="fa fa-cog"></i>
                                        </a>
                                        <a href="#" class="widget-collapse-option" data-toggle="collapse">
                                            <i class="fa fa-chevron-up"></i>
                                        </a>
                                        <a href="#" class="widget-refresh-option">
                                            <i class="fa fa-refresh"></i>
                                        </a>

                                    </span>
                                </div>
                              <div class="smart-widget-inner table-responsive">
							  	<div id="div_container" style="text-align:center;">
                                    <div id="my_div" class="fakeContainer first_div" style="padding:1px;">
                                        <table id="dataTable">

                                        </table>
                                    </div>
                                </div>
                             </div>
                            </div>
                        </div>

					</div>

				</div>
			</div>
    <!-- 行业类型选择（Modal） -->
    <div class="modal fade" id="Industrytype" tabindex="-1" role="dialog"
         aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
            </div>
        </div>
    </div>
    <!-- ./行业类型选择（Modal） -->
			<!-- 实施单位modal -->
<div class="modal fade" id="handlingUnitModeler" tabindex="-1"
	role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-hidden="true">&times;</button>
				<h4 class="modal-title" id="">处理主体</h4>
			</div>
			<div class="modal-body">
				<div class="smart-widget-body form-horizontal">
					<div class="form-group">
						<div class="col-lg-6">
							<div>
								<ul id="handlingUnitTreeDept" class="ztree"></ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-info" data-dismiss="modal">确定</button>
				<button type="button" class="btn btn-default" data-dismiss="modal"
					onclick="clearHandlingUnit()">清空</button>
			</div>
		</div>
	</div>
</div>
	<!-- 专项行动选择（Modal） -->
	<div class="modal fade" id="glfrw" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
                        <div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                        <h4 class="modal-title" id="myModalLabel">专项选择</h4>
                    </div>
                    <div class="modal-body">
                        <div class="smart-widget-body form-horizontal">
                                 <div class="form-group">
                                    <label for="专项名称" class="col-md-2 control-label">专项名称</label>
                                    <div class="col-md-4">
                                    <input type="text" class="form-control" id="templateName" name="templateName" placeholder="专项名称" >
                                    </div>
                                    <label class="control-label col-md-1">专项级别</label>
									<div class="col-md-4">
										<select class="form-control" id="speciallevel" name="speciallevel">
											<option value="">——请选择——</option>
											<option value="0">部级</option>
											<option value="1">省级</option>
											<option value="2">市级</option>
											<option value="3">县级</option>
										</select>
									</div>
                                </div>
                                <div class="form-group">
                                	<label class="control-label col-md-2">是否指定检查表</label>
									<div class="col-md-4">
										<select class="form-control" id="isSpecial" name="isSpecial">
										    <option value="">——请选择——</option>
										    <option value="1">是</option>
                                            <option value="0">否</option>
										</select>
									</div>
									<label class="control-label col-md-1 col-sm-2">更新时间</label>
									<div class="col-md-4 col-sm-2" style="padding:0;">
										<div class="col-md-6 col-sm-6">
										<input type="text" placeholder="开始时间" class="form-control" readonly="readonly"
														data-parsley-required="true" id="updateTimeStart" name="updateTimeStart">
										</div>
										<div class="col-md-6 col-sm-6">
										<input type="text" placeholder="结束时间" class="form-control" readonly="readonly"
														data-parsley-required="true" id="updateTimeEnd" name="updateTimeEnd">
										</div>
									</div>

								</div>
							   	<div class="form-group">
							   		<label class="control-label col-md-2">是否本单位适用</label>
									<div class="col-md-4">
										<select class="form-control" id="isLevelCity2">
											<option value="0">——请选择——</option>
                                            <option value="1">是</option>
                                            <option value="2">否</option>
										</select>
									</div>
                                    <label class="control-label col-md-1 col-sm-2">适用地区</label>
									<div class="col-md-4 col-sm-2">
										<input type="text" placeholder="适用地区" class="form-control" data-parsley-required="true" id="applyAreaName" name="applyAreaName">
									</div>
								</div>
								<div class="form-group">
								   <div class="col-md-11 text-right">
                                        <button id ="specialTaskSearch" class="btn btn-info">查询</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                	<label class="control-label col-md-2 col-sm-2">已选择：</label>
                                	<div class="col-md-6" style="margin-top: 7px;" id="checkedSpecialAction">
									</div>
									<div class="col-md-3 text-right">
                                        <button type="button" onclick="clearSpecialAction()" class="btn btn-danger">清空</button>
										<button type="button" class="btn btn-info" onclick="confirmSpecialAction()" data-dismiss="modal">确定</button>
									</div>
								</div>
                       			<hr>
                        </div>
                          <!-- 附件预览modal -->
                        <div class="modal fade" id="fjyl" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
				            <div class="modal-dialog modal-lg">
				                <div class="modal-content">
				                </div>
				            </div>
				        </div>
                    	<table id="specialActionTable" class="table-no-bordered">

						</table>
			</div>
		</div>
	</div>
</body>
<!-- 专项行动 -->
<script type="text/javascript">

//更新开始时间
$("[name='updateTimeStart']").datetimepicker({
 format:'yyyy-mm-dd',
	 language: 'cn',
	 todayBtn: true,
 clearBtn: true,
 autoclose: true,
 minView:2,
 maxView:4
});
//更新结束时间
$("[name='updateTimeEnd']").datetimepicker({
 format:'yyyy-mm-dd',
	 language: 'cn',
	 todayBtn: true,
 clearBtn: true,
 autoclose: true,
 minView:2,
 maxView:4
});
var  caseSpecialBase = new Array();
var choosedSpecialActionName = new Array();
var choosedSpecialActionId = new Array();
function checkTime(i){
	 if (i<10)
	   {i="0" + i}
	   return i
}
function indexOf(arr,val){
	    for(var i = 0; i < arr.length; i++){
	        if(arr[i] == val){return i;}
	    }
	    return -1;
	}
//判断数组中是否包含某元素
function array_contain(array, obj){
   for (var i = 0; i < array.length; i++){
       if (array[i] == obj)//如果要求数据类型也一致，这里可使用恒等号===
           return true;
   }
   return false;
}

function isContains(str, substr) {
   return str.indexOf(substr) >= 0;
}
//多条件查询
$("#specialTaskSearch").click(function(){
	 $('#specialActionTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:10});
});
function clearSpecialAction(){
    $('#specialActionTable').bootstrapTable('uncheckAll');
    choosedSpecialActionName = new Array();//将存放选中的name数组清除
    choosedSpecialActionId = new Array();//将存放选中的id数组清除
    caseSpecialBase=[];
    $("#specialActionNames").val("");
    $("#specialActionIds").val("");
    $("#checkedSpecialAction").html("");
}
function confirmSpecialAction(){//专项行动模态窗确定按钮
	var specialAction= $("#specialActionTable").bootstrapTable('getSelections');
	if(specialAction.length>5){
		swal({
			title : "提示",
			text : "专项行动最多关联5条!",
			type : "info"
		})
	}else{
        $("#specialActionNames").val(choosedSpecialActionName.join(","));
        $("#specialActionIds").val(choosedSpecialActionId.join("-"));
	}
}

$("#glzxxdBtn").click(function() {
         LoadingDataListOrderRealItems();

         $('#specialActionTable').bootstrapTable('hideColumn', 'id');
     });
	function LoadingDataListOrderRealItems() {
		$('#specialActionTable').bootstrapTable({
			 method: 'post',
			 dataType: "json",
			 url:  WEBPATH+'/jcbl/specialActionList',
		       undefinedText : '-',
		       pagination : true, // 分页
		       striped : true, // 是否显示行间隔色
		       cache : false, // 是否使用缓存
		       pageSize: 10, // 设置默认分页为 20
		       pageNumber: 1,
		       clickToSelect:false,
		       checkboxHeader:false,
		       queryParamsType: "",
		       pageList: [10, 25, 50, 100, 200], // 自定义分页列表
		       singleSelect: false,
		       contentType: "application/x-www-form-urlencoded;charset=UTF-8",
		      // pageList : [ 5, 10, 20 ],
		      // showColumns : true, // 显示隐藏列
		       sidePagination: "server", //服务端请求
		       queryParams:function(params) {
		    	   var templateName = $("#templateName").val();
		    	   var speciallevel = $("#speciallevel").val();
		    	   var isSpecial = $("#isSpecial").val();
		    	   var updateTimeStart = $("#updateTimeStart").val();
		    	   var updateTimeEnd = $("#updateTimeEnd").val();
		    	   var applyAreaName = $("#applyAreaName").val();
		    	   var isLevelCity2 = $("#isLevelCity2").val();
		    	   var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的

		    			   pageNum: params.pageNumber,
			               pageSize: params.pageSize,
			               templateName:templateName,
			               speciallevel:speciallevel,
			               isSpecial:isSpecial,
			               updateTimeStart:updateTimeStart,
			               updateTimeEnd:updateTimeEnd,
			               applyAreaName:applyAreaName,
			               isLevelCity:isLevelCity2
			            };
			       return temp;
		       },//参数
		       uniqueId : "id", // 每一行的唯一标识
				 columns: [
			           {
			           checkbox: true,
			           title: "操作",
			           align: 'center',
			           formatter: function (value, row, index) {
			        	   if($.inArray(row.id, caseSpecialBase) >-1){
								return {
									checked : true
								}
							}
	                   	}
			           },
				       {
				    	   title: '序号',//标题  可不加
		                   align: 'center',
		                   formatter: function (value, row, index) {
		                       return index+1;
		                   }
				       },
			           {
			           field: "templateName",
			           title: "专项名称",
			           align: 'center',
			           },
			           {
			           field: "speciallevelName",
			           title: "专项级别",
			           align: 'center',
			           },
			           {
			           field: "createDepartment",
			           title: "创建单位",
			           align: 'center',
			           }
			           ,
			           {
			           field: "updateTime",
			           title: "更新时间",
			           align: 'center',
			           formatter : function(value){
							if(value==null || value==''){return '';}
			                var date = new Date(value);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                var h = checkTime(date.getHours());
			                var i = checkTime(date.getMinutes()); //分
			                return y + '-' +m + '-' + d+' '+h+':'+i;
			            }
			           }
			           ,
			           {
			           field: "applyAreaName",
			           title: "适用地区",
			           align: 'center',
			           },
			           {
			           field: "isSpecial",
			           title: "是否指定检查表",
			           align: 'center',
			           formatter: function (value, row, index) {
			        	   if(value=='0'){
			        		   return "否"
			        	   }else{
			        		   return "是"
			        	   }
			           	}
				       },
				       {
			           field: "",
			           title: "持续时间",
			           align: 'center',
			           formatter: function (value, row, index) {
			        	   if(row.durationStartTime==null){
			        		   row.durationStartTime='';
			        		   var durationStartTime='';
			        	   }
			        	   if(row.durationEndTime==null){
			        		   row.durationEndTime='';
			        		   var durationEndTime ='';
			        	   }
			        	   if(row.durationStartTime!=''){
			        		   var date = new Date(row.durationStartTime);
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                var durationStartTime =y + '-' +m + '-' + d;
			        	   }
			        	   if(row.durationEndTime!=''){
			        		   var date = new Date(row.durationEndTime);
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                var durationEndTime =y + '-' +m + '-' + d;
			        	   }
			        	   return durationStartTime+'-'+durationEndTime
			           }
			           },
				       {
			           field: "describe",
			           title: "描述",
			           align: 'center',
			           },
				       {
			           field: "",
			           title: "附件",
			           align: 'center',
			           formatter: function (value, row, index) {
			        	   var html="<a tabindex='-1' data-toggle='modal' data-remote='${webpath}/HomeSpecialInlet/attachmentPreviewer?actionId="+row.id+"' data-target='#fjyl'  style=\"cursor:pointer;\"><i class=\"fa \" style=\"color: #23b7e5;\">预览</i></a>&nbsp";
				        	  return html;
			           	}
			           }
			           ],
			           responseHandler : function(res) {
			               return {
			                   total : res.total,
			                   rows : res.records
			               };
			           },
			           onCheck: function(row, $element) {
			        	   if(!array_contain(choosedSpecialActionId,row.id)){
			        		   caseSpecialBase.push(row.id);
			        		   choosedSpecialActionId.push(row.id);
			        		   choosedSpecialActionName.push(row.templateName);
			        	   }
			        	   $("#checkedSpecialAction").html(choosedSpecialActionName.toString());
			           },//单击row事件
			        	 onUncheck: function(row, $element) {
			        		 if(array_contain(choosedSpecialActionId,row.id)){
			        			   var index = indexOf(choosedSpecialActionId,row.id);
			        			   choosedSpecialActionId.splice(index,1);
			        			   choosedSpecialActionName.splice(index,1);
					        }
					        $("#checkedSpecialAction").html(choosedSpecialActionName.toString());
				        	 },
				        	 onRefresh: function () {
				        	   },
				        	   onLoadSuccess:function(data,e){
				        		   var dataArray = data.rows;
				        		   for(var i=0;i<dataArray.length;i++){
				        			   if(dataArray[i][0]!=undefined){
				        				   if(!array_contain(choosedSpecialActionId,dataArray[i].id)){
					        				   choosedSpecialActionId.push(dataArray[i].id);
					        				   choosedSpecialActionName.push(dataArray[i].templateName);
					        				 }
				        			   }
				        		   }
				        		   $("#checkedSpecialAction").html(choosedSpecialActionName.toString());

				        	   },
			           formatLoadingMessage: function () {
			        	   return "数据加载中...";
			        	   },
			         formatNoMatches: function () { //没有匹配的结果
			        		   return '无符合条件的记录';
			        	   }
			})

	}
</script>
<script type="text/javascript">
$(document).ready(function(){
	//首次对接成功时间
	$("[name='searchDateStart3']").datetimepicker({
	     format:'yyyy-mm-dd',
	   	 language: 'cn',
	   	todayBtn: true,
        clearBtn: true,
	     autoclose: true,
	     minView:2,
        maxView:4
	}).on('changeDate',function(ev){
		$("[name='searchDateEnd3']").datetimepicker('setStartDate',new Date($("#searchDateStart3").val()));
	});

	$("[name='searchDateEnd3']").datetimepicker({
	     format:'yyyy-mm-dd',
	     language: 'cn',
	     todayBtn: true,
        clearBtn: true,
	     autoclose: true,
	     minView:2,
        maxView:4
	}).on('changeDate',function(ev){
		$("[name='searchDateStart3']").datetimepicker('setEndDate',new Date($("#searchDateEnd3").val()));
	});
	$('#dataTable').bootstrapTable('destroy').bootstrapTable({
		 method: 'post',
		 dataType: "json",
		 url:  WEBPATH+'/ajtz/mainCase-list',
	     undefinedText : '-',
	     pagination : true, // 分页
	     striped : true, // 是否显示行间隔色
	     cache : false, // 是否使用缓存
        fixedColumns: true,//固定列
        fixedNumber: 4,
        cache: false,
	     pageSize:10, // 设置默认分页为 10
	     pageNumber: 1,
	     queryParamsType: "",
	     locale:'zh-CN',
	     pageList: [5, 10, 20,30,50], // 自定义分页列表
	     singleSelect: false,
	     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	     //showColumns : true, // 显示隐藏列
	     sidePagination: "server", //服务端请求
	     queryParams:function (params) {
	    	 	var belongCity = $("[name='belongCity']").val();
	    	 	var belongCountry = $("[name='belongCountry']").val();
	    	 	var isLevelCity = $("[name='isLevelCity']").val();
	      		var researchOrgName = $("[name='researchOrgName']").val();
	      		var createTimeStart=$("[name='createTimeStart']").val();
	      		var createTimeEnd=$("[name='createTimeEnd']").val();
	      		var handlingUnitId=$("[name='handlingUnitId']").val();
	      		var lawObjName=$("[name='lawObjName']").val();
	      		var caseReason = $("[name='caseReason']").val();
	      		//var caseType = $("#caseType").val();
	      		var specialAction = $("#specialActionIds").val();
	      		var illegalType = $("#illegalType").val();
	      		var caseState=$("[name='caseState']").val();
	      		var caseName = $("[name='caseName']").val();
	      		var isSmr = $("#isSmr").val();
	      		var isCml = $("#isCml").val();
	      		var isAstn = $("#isAstn").val();
	      		var isLmt = $("#isLmt").val();
	      		var isAtt = $("#isAtt").val();
	      		var isDet = $("#isDet").val();
	      		var isEvmt = $("#isEvmt").val();
	      		var isDay = $("#isDay").val();
	      		var isSubmit = $("#isSubmit").val();
                var industryTypeCode = $("#industryTypeCode").val();//行业
	      		var searchDateStart3=$("[name='searchDateStart3']").val();
	      		var searchDateEnd3=$("[name='searchDateEnd3']").val();
	      		//特殊监管行业
             var specialSuperviseIndustry = $("[name='specialSuperviseIndustry']").val();

             var caseNumber=$("[name='mainCaseNumber']").val();
	      		//将综合案件编号输入的小写字母转换成大写字母
	      		var mainCaseNumber = caseNumber.toUpperCase();
	            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
           		pageNum: params.pageNumber,
                pageSize: params.pageSize,
              	belongCity:belongCity,
              	belongCountry:belongCountry,
              	isLevelCity:isLevelCity,
              	researchOrgName:researchOrgName,
              	createTimeStart:createTimeStart,
              	createTimeEnd:createTimeEnd,
              	caseReason:caseReason,
              	specialAction:specialAction,
				handlingUnitId:handlingUnitId,
				lawObjName:lawObjName,
				illegalType:illegalType,
				mainCaseNumber:mainCaseNumber,
				caseStatus:caseState,
				caseName:caseName,
                industryTypeCode:industryTypeCode,
				isSmr:isSmr,
				isCml:isCml,
				isAstn:isAstn,
				isLmt:isLmt,
				isAtt:isAtt,
				isDet:isDet,
				isEvmt:isEvmt,
				isSubmit:isSubmit,
				searchDateStart3:searchDateStart3,
				searchDateEnd3:searchDateEnd3,
				isDay:isDay,
                specialSuperviseIndustry:specialSuperviseIndustry
	            };
	            return temp;
	     },//参数
	     uniqueId : "id", // 每一行的唯一标识
		 columns: [
			  {
	        	   field: 'id',
		           title: "操作",
		           align: 'center',
		           formatter: function(value,row,index){
		        	  var html="";
		        	  html+="<a onclick=\"toCase('"+row.id+"')\" style=\"cursor:pointer;\"><i class=\"fa fa-search\" style=\"color: #23b7e5;\">详情</i></a>"
		        	  return html;
		           }
	           },
	           {
	        	   field: "",
		           title: "序号",
		           align: 'center',
		           formatter: function(value,row,index){
			        	  return index+1;
			       }
	           },
	           {
		           field: "caseNumber",
		           title: "综合案件编号",
		           align: 'center'
	           },
	           {
	        	   field: "lawObjectName",
		           title: "当事人名称",
		           align: 'center'
	           },
	           {
		           field: "caseName",
			       title: "案件名称",
			       align: 'center'
	           },
	           {
		           field: "punishSubject",
		           title: "处理主体",
		           align: 'center'
	           },
	           {
		           field: "researchOrgName",
		           title: "调查机构",
		           align: 'center'
	           },{
		           field: "caseReason",
		           title: "案由",
		           align: 'center'
	           },
	           {
	        	   field: "caseStatus",
		           title: "案件状态",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value=='1'){
			        		   return '已办结';
			        	   } else if (value=='0'){
			        		   return '进行中';
			        	   }
		           }
	           },
	           {
		           field: "specialActions",
		           title: "关联专项行动",
		           align: 'center'
	           },{
		           field: "illegalTypes",
		           title: "违法类型",
		           align: 'center'
	           },{
		           field: "createTime",
		           title: "案件创建时间",
		           align: 'center'
	           },
	           {
		           field: "smrProgramState",
		           title: "有无简易行政处罚",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0' || value=='3'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "cmlPunishState",
		           title: "有无一般行政处罚",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0' || value=='3'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "astnDecState",
		           title: "有无行政命令",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },

	           {
	        	   field: "attachmentState",
		           title: "有无查封扣押",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "lmtProductionState",
		           title: "有无限产停产",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "detentionState",
		           title: "有无移送行政拘留",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "evmtPollutionState",
		           title: "有无移送涉嫌犯罪",
		           align: 'center',
		           formatter: function(value, row, index){
		        	   if(value==null || value=='' || typeof(value)=='undefined'){
		        		   return '-';
		        	   } else if (value=='0'){
		        		   return '无';
		        	   } else {
		        		   return '有';
		        	   }
		           }
	           },
	           {
	        	   field: "pdcount",
		           title: "按日连续处罚次数",
		           align: 'center'
	           },{
			           field: "dockSusState",
			           title: "是否对接到环保部",
			           align: 'center',
			           formatter: function(value, row, index){
			        	   if(value=='1'){
			        		   return '是';
			        	   } else {
			        		   return '否';
			        	   }
			           }
		           },{
 			        	   field: "firstDockingDate",
				           title: "首次对接成功时间",
				           align: 'center'
 			       },{
 			        	   field: "easyAmount",
				           title: "简易行政处罚罚款金额（万元）",
				           align: 'center'
 			        },{
 			        	   field: "generalAmount",
				           title: "一般行政处罚金额（万元）",
				           align: 'center'
 			        },{
 			        	   field: "penaltyAmount",
				           title: "按日计罚金额（万元）",
				           align: 'center'
 			        }
		 ],
		 responseHandler : function(res) {
              return {
                  total : res.total,
                  rows : res.list
              };
        },
        onCheck: function(row, $element) {
        },//单击row事件
        onUncheck: function(row, $element) {
	     },
	     onUncheckAll: function(row, $element) {
	     },
	     onCheckAll:function(row, $element) {
	     },
	     onRefresh: function () {
	     },
        formatLoadingMessage: function () {
       	   return "玩命加载中...";
        },
        formatNoMatches: function () { //没有匹配的结果
       		   return '无符合条件的记录';
        }
	});

	// 是否选中本级
	$("#checkLevel").change(function() {
		if( $("#checkLevel").is(':checked')==true ){
			$("#isLevelCity").val('1');
		}else{
			$("#isLevelCity").val('0');
		}
	});

	//返回按钮之后参数和页码的回显
	var params = $("#groundBackParams").val();
	if(params != null && params != '' && params != 'undefined'){
		var jsonParam = $.parseJSON(params);

		for(var key in jsonParam){
			//绑定设定条件
			if(key=='belongCity'){
				$("[name="+key+"]").find("option[value='"+jsonParam[key]+"']").attr("selected","selected");
				//$("#"+key).val(jsonParam[key]);
				$("[name="+key+"]").trigger('change');
				continue;
			}
			$("[name="+key+"]").val(jsonParam[key]);
			if(key == 'isLevelCity'){
				if(jsonParam[key] == '1'){
					$("#checkLevel").attr('checked','checked');
					$("#checkLevel").trigger('change');
				}
			}
		}
		$('#dataTable').bootstrapTable('refresh',{pageNumber:parseInt(jsonParam['pageNum']),pageSize:parseInt(jsonParam['pageSize'])});
	}
});

function dajSearch(){
	$('#dataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:10});
}

function clearHandlingUnit(){
	$("#handlingUnitId").val("");
	$("#handlingUnitName").val("");
}

//点击详情
function toCase(caseId){
business.addMainContentParserHtml(WEBPATH + '/caseInfo/baseInfoCasePage', "caseId="+caseId+"&parentUrl=3&selectType=1");
}
//导出
function downloadExcelTask(){
	var belongCity = $("[name='belongCity']").val();
 	var belongCountry = $("[name='belongCountry']").val();
 	var isLevelCity = $("[name='isLevelCity']").val();
		var researchOrgName = $("[name='researchOrgName']").val();
		var createTimeStart=$("[name='createTimeStart']").val();
		var createTimeEnd=$("[name='createTimeEnd']").val();
		var handlingUnitId=$("[name='handlingUnitId']").val();
		var lawObjName=$("[name='lawObjName']").val();
		var caseReason = $("[name='caseReason']").val();
		//var caseType = $("#caseType").val();
		//var specialAction = $("#specialAction").val();
		var specialAction = $("#specialActionIds").val();
		var illegalType = $("#illegalType").val();
		var caseState=$("[name='caseState']").val();
		var caseName = $("[name='caseName']").val();
		var isSmr = $("#isSmr").val();
		var isCml = $("#isCml").val();
		var isAstn = $("#isAstn").val();
		var isLmt = $("#isLmt").val();
		var isAtt = $("#isAtt").val();
		var isDet = $("#isDet").val();
		var isEvmt = $("#isEvmt").val();
		var isDay = $("#isDay").val();
		var isSubmit = $("#isSubmit").val();
  		var searchDateStart3=$("[name='searchDateStart3']").val();
  		var searchDateEnd3=$("[name='searchDateEnd3']").val();
		var caseNumber=$("[name='mainCaseNumber']").val();
		//将综合案件编号输入的小写字母转换成大写字母
		var mainCaseNumber = caseNumber.toUpperCase();

var path = WEBPATH+'/ajtz/download-MainCase-execl?belongCity='+belongCity+'&belongCountry='+belongCountry+'&isLevelCity='+isLevelCity
		+'&researchOrgName='+researchOrgName+'&createTimeStart='+createTimeStart+'&createTimeEnd='
		+createTimeEnd+'&handlingUnitId='+handlingUnitId+'&lawObjName='+lawObjName+'&caseReason='+caseReason+'&specialAction='
		+specialAction+'&caseName='+caseName+'&isSmr='+isSmr
		+'&isCml='+isCml+'&isAstn='+isAstn+'&isLmt='+isLmt+'&isAtt='+isAtt+'&caseNumber='+caseNumber
		+'&isDet='+isDet+'&mainCaseNumber='+mainCaseNumber+'&caseStatus='+caseState+'&isEvmt='+isEvmt+'&isDay='+isDay
		+'&isSubmit='+isSubmit+'&searchDateStart3='
		+searchDateStart3+'&searchDateEnd3='+searchDateEnd3;
window.location.href=path;
}

//返回上一步主菜单
function goback(ajtzConfigUrl,ajtzConfigParams) {
if(ajtzConfigUrl != null && ajtzConfigUrl != '' && ajtzConfigUrl != 'undefined'){
	business.addMainContentParserHtml(WEBPATH+ajtzConfigUrl+"?back=1&menuId="+'${menuId}', $("#searchForm").serialize());
} else {
	swal({
		title : "提示！",
		text : "返回信息错误，请刷新后重试。",
		type : "error"
	})
}
}

//日期控件
$("[name='createTimeStart']").datetimepicker({
     format:'yyyy-mm-dd',
   	 language: 'cn',
   	 minView:2,
   	todayBtn: true,
   	 clearBtn : true,
   	 endDate:new Date(),
     //endDate:$("#askingEndDate").val(),
     autoclose: true
}).on('changeDate',function(ev){
	$("[name='createTimeEnd']").datetimepicker('setStartDate',new Date($("#createTimeStart").val()));
});

$("[name='createTimeEnd']").datetimepicker({
     format:'yyyy-mm-dd',
     language: 'cn',
     minView:2,
     todayBtn: true,
     clearBtn : true,
     endDate:new Date(),
   	 //startDate:$("#askingStartDate").val(),
     autoclose: true
}).on('changeDate',function(ev){
	$("[name='createTimeStart']").datetimepicker('setEndDate',new Date($("#createTimeEnd").val()));
});
</script>
