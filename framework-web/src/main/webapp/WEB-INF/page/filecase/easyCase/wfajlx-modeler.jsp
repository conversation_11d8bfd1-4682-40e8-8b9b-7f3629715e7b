<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
	<script type="text/javascript">
	  var  littleModelerBaseArr = new Array();
	  $(document).ready(function(){
 			//初始化时违法案件类型选中处理
			var littleModelerCodes = $("#"+'${littleModelerId}').val();
			var littleModelerNames = $("#"+'${littleModelerName}').val();
			if(littleModelerCodes != null && littleModelerCodes != '' && $.trim(littleModelerCodes).length > 0){
				var littleModelerCodesArry = littleModelerCodes.split(",");
				var littleModelerNamesArry = littleModelerNames.split(",");
				for( i = 0; i< littleModelerCodesArry.length ; i++){
					littleModelerBaseArr.push(littleModelerCodesArry[i]);
				}
				// 获取页面复选框集合 
				var littleModelerCheckObj = document.getElementsByName('littleModelerCheck');
				for(var i=0; i < littleModelerCheckObj.length ; i++ ){
					//不能用attrIdsStr.indexOf，因为如果有13，则会把1和3也选中。。。。  使用  $.inArray(rowCode, idsArr) >-1
					if($.inArray(littleModelerCheckObj[i].value, littleModelerBaseArr) >-1){
						littleModelerCheckObj[i].checked=true;
					}
				}
			}  
	  });
		//处理选中的违法类型
	 function littleModelerCheckSave() {
		 	//选中的违法类型
			var obj = document.getElementsByName('littleModelerCheck');
			var ids = "";
			var littleModelerNames = "";
			for (var i = 0; i < obj.length; i++) {
				if (obj[i].checked)
					ids += obj[i].value + ',';
				if (obj[i].checked)
					littleModelerNames += $.trim($("#littleModelerCheck" + obj[i].value).html()) + ',';
			}
			ids = ids.substring(0, ids.length - 1);//选中的所有的id
			littleModelerNames = littleModelerNames.substring(0, littleModelerNames.length - 1);//选中的所有的name
			
			var littleModelerCodes = $("#"+'${littleModelerId}').val(ids);
			var littleModelerNames = $("#"+'${littleModelerName}').val(littleModelerNames);
			//选择之后重新校验该字段
			var checkFormName =  '${littleModelerCheckForm}';
			if(checkFormName!='null' && checkFormName!='' && $.trim(checkFormName).length > 0){
				$('#'+checkFormName).formValidation('revalidateField', '${littleModelerName}');
			}
			//行政命令专属方法
			if(checkFormName=='xzml_form'){
				$('#'+checkFormName).formValidation('revalidateField', 'caseTypeName');
			}
			$("#wfajlx").modal('hide');   
		}
	 
	</script>

		<div class="modal-header">
				<div style="float:right; margin-top:-5px;">
                <button type="button" onclick="littleModelerCheckSave()" class="btn btn-info">确定</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
				<h4 class="modal-title" id="myModalLabel">违法案件类型</h4>
			</div>
			<div class="modal-body" style="height: 200px; padding-top: 20px;">
				<div class="smart-widget-body form-horizontal">
					<div class="form-group">
						<div class="col-lg-12 padding-lg">
							<c:forEach items="${littleModelerList}" var="item" varStatus="status">
								<div class="checkbox inline-block" style="width: 120px;">
									<div class="custom-checkbox">
										<input type="checkbox" id="checkboxlittle${status.index+1}" name="littleModelerCheck" value="${item.code }">
                                       	<label for="checkboxlittle${status.index+1}" class="checkbox-blue" checked></label>
									</div>
									<div class="inline-block vertical-top" id="littleModelerCheck${item.code }">${item.name }</div>
								</div>
							</c:forEach>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<!--<button type="button" onclick="littleModelerCheckSave()" class="btn btn-info">确定</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
			</div>
</body>
</html>