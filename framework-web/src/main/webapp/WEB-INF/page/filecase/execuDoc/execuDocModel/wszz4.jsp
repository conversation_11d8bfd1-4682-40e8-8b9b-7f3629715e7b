<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page language="java" import="java.util.*"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<jsp:include page="publicJs.jsp"/>
<div class="main-container">
	<div class="padding-md">
	<!--第二层任务办理row-->
		<div class="row">
			<!--任务办理-->
			<div class="col-md-12">                                    
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
					    <i class="fa fa-arrow-right"></i> 案件处理内部审批表（通用）
					</div>
					<div class="smart-widget-inner table-responsive">
	                   	<div class="col-md-1"></div>
	                   	<div class="col-md-9">
							<div style="float:left; padding:5px 10px; width:100%;">
								<%-- <button type="button" id="relateCaseBtn" class="btn btn-info" data-toggle="modal" data-target="#glaj"
									data-remote="${webpath }/execuDoc/relateCaseModal">关联案件</button> --%>
								<div id="caseInfoDisplay" style="display:none;">
									<input type="hidden" id="docType" value="wszz4">
									<input type="hidden" id="docId" name="docId" value="${docId }"/>
									<input type="hidden" id="pageNo" name="pageNo" value="${pageNo }"/>
									<input type="hidden" id="docNum" name="docNum" value="${docNum }"/>
									<input type="hidden" id="caseId" name="caseId" value="${caseInfo.id }"/>
									<input type="hidden" id="mongoId" name="mongoId" value="${docInfo.mongodbId }"/>
									<p style="font-size:16px; padding-top:10px; font-weight:bold; color:#23B7E5;">
										<span id="divCaseName">${caseInfo.caseName }</span></p>
									<p style="margin:-5px 0 0 20px;"><span style="font-weight:bold;">案件编号：</span>
										<span id="divCaseNumber">${caseInfo.caseNumber }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">当事人名称：</span>
										<span id="divLawObjectName">${caseInfo.lawObjectName }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">处罚主体：</span>
										<span id="divPunishSubject">${caseInfo.punishSubject }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">调查机构：</span>
										<span id="divResearchOrgName">${caseInfo.researchOrgName }</span></p>
										<!-- 20180921文书制作改造注掉 start -->
									<%-- <p style="margin:0 0 0 20px;">
										<div style="font-weight:bold; margin:10px 0 0 20px;width:120px;float:left;">案件类型选择：</div>
										<div style="margin:10px 0 0 20px;">
											<div id="model1" style="display: none;float:left; margin-left:10px;">
												<div class="custom-checkbox">
											        <input type="checkbox" id="checkbox1" name="moduleName" ${hisReadOnly } value="1"> <label
											            for="checkbox1" class="checkbox-blue" checked></label>
											    </div>简易行政处罚
										    </div>
										    <div id="model2" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox2" name="moduleName" ${hisReadOnly } value="2"> <label
											            for="checkbox2" class="checkbox-blue" checked></label>
											    </div>一般行政处罚
										    </div>
										    <div id="model3" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox3" name="moduleName" ${hisReadOnly } value="3"> <label
											            for="checkbox3" class="checkbox-blue" checked></label>
											    </div>行政命令
										    </div>
										    <div id="model4" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox4" name="moduleName" ${hisReadOnly } value="4"> <label
											            for="checkbox4" class="checkbox-blue" checked></label>
											    </div>查封扣押
										    </div>
										    <div id="model5" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox5" name="moduleName" ${hisReadOnly } value="5"> <label
											            for="checkbox5" class="checkbox-blue" checked></label>
											    </div>限产停产
										    </div>
										    <div id="model6" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox6" name="moduleName" ${hisReadOnly } value="6"> <label
											            for="checkbox6" class="checkbox-blue" checked></label>
											    </div>行政拘留
										    </div>
										    <div id="model7" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox7" name="moduleName" ${hisReadOnly } value="7"> <label
											            for="checkbox7" class="checkbox-blue" checked></label>
											    </div>环境污染犯罪
										    </div>
										    <div id="model8" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox8" name="moduleName" ${hisReadOnly } value="8"> <label
											            for="checkbox8" class="checkbox-blue" checked></label>
											    </div>申请法院强制执行
										    </div>
										    <div id="model9" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox9" name="moduleName" ${hisReadOnly } value="9"> <label
											            for="checkbox9" class="checkbox-blue" checked></label>
											    </div>其他移送
										    </div>
										    <div id="model10" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox10" name="moduleName" ${hisReadOnly } value="10"> <label
											            for="checkbox10" class="checkbox-blue" checked></label>
											    </div>按日计罚
										    </div>
										</div>
									</p> --%>
									<!-- 20180921文书制作改造注掉 end -->
									<div class="form-group" style="float:left;display:none;padding-top:10px;padding-left:20px;" 
										id="penaltyRela">
										<label class="control-label" style="width:120px; padding:5px 0;">按日计罚案件关联：</label>
										<div class="input-group" style="width:400px; float:right;">
											<input type="hidden" id="penaltyId">
											<input type="text" class="form-control" id="penaltyDeciNum" readonly="readonly">
										    <div class="input-group-btn">
										        <button type="button" class="btn btn-info no-shadow" tabindex="-1" 
										        data-toggle="modal" data-target="#arjfgl" data-remote="${webpath }/execuDoc/toPenaltyListPage"
										        	>选择</button>
										    </div>
										</div>
									</div>
								</div>
							</div>
							<div class="smart-widget-body form-horizontal text-center font-14">
							<form id="compInfo">
								<div class="col-md-12 text-center">
									<input type="hidden" name="userDept" id="userDept" 
									value="<c:if test="${empty params.userDept }">${userDept }</c:if><c:if test="${not empty params.userDept }">${params.userDept }</c:if>"/>
                                    <p style="font-size:16px; margin-top:20px;"><c:if test="${empty params.userDept }">${userDept }</c:if>
	                                    	<c:if test="${not empty params.userDept }">${params.userDept }</c:if></p>
                                    <p style="font-size:16px;">案件处理内部审批表（通用）</p>
                                    </div>
                                	<table width="100%" border="1" cellspacing="0" cellpadding="0" bordercolor="#999">
                                      <tr>
                                        <td>申请事项</td>
                                        <td colspan="4"><input type="text" class="form-control" ${hisReadOnly } value="${params.applyInfo }" id="applyInfo" name="applyInfo" placeholder="申请事项"></td>
                                      </tr>
                                      <tr>
                                        <td><span style="color:red;">*</span>案源</td>
                                        <td colspan="4"><textarea class="form-control" rows="6" ${hisReadOnly } id="caseSource" name="caseSource" placeholder="案源">${params.caseSource}</textarea></td>
                                      </tr>
                                      <tr>
                                        <td rowspan="5">当事人</td>
                                        <td>名称或姓名</td>
										<td colspan="3" id="lawObjectNameTd">${params.lawObjectName }
										</td>
										<input type="hidden" id="lawObjectName" name="lawObjectName" value="${params.lawObjectName }">
                                      </tr>
                                      <tr>
                                        <td>地址（住址）</td>
                                        <td><input type="text" class="form-control" value="${params.address }" ${hisReadOnly } id="address" name="address" placeholder="福州市晋安区连江路73号"></td>
                                        <td>邮政编码</td>
                                        <td><input type="text" class="form-control" value="${params.postalNumber }" ${hisReadOnly } id="postalNumber" name="postalNumber" placeholder="330110"></td>
                                      </tr>
                                      <tr>
                                        <td>营业执照注册号（公民身份号码）</td>
                                        <td><input type="text" class="form-control" value="${params.licenseNo }" ${hisReadOnly } id="licenseNo" name="licenseNo" placeholder="914404006174883094-x"></td>
                                        <td>组织机构代码</td>
                                        <td><input type="text" class="form-control" value="${params.orgCode }" ${hisReadOnly } id="orgCode" name="orgCode" placeholder="74883094"></td>
                                      </tr>
                                      <tr>
                                        <td>社会信用代码</td>
                                        <td colspan="3"><input type="text" class="form-control" ${hisReadOnly } value="${params.socialCreditCode }" id="socialCreditCode" name="socialCreditCode" placeholder="914404006174883094"></td>
                                      </tr>
                                      <tr>
                                        <td>法定代表人（负责人）</td>
                                        <td><input type="text" class="form-control" value="${params.legalPerson }" ${hisReadOnly } id="legalPerson" name="legalPerson" placeholder="张三"></td>
                                        <td>职务</td>
                                        <td><input type="text" class="form-control" value="${params.jobName }" ${hisReadOnly } id="jobName" name="jobName" placeholder="总经理"></td>
                                      </tr>
                                      <tr>
                                        <td><p>简要案情及</p><p>申请理由依据和内容</p></td>
                                        <td colspan="4"><table width="100%" border="0" cellspacing="0" cellpadding="0">
                                          <tr>
                                            <td><textarea rows="6" class="form-control" ${hisReadOnly } id="caseRec" name="caseRec" placeholder="简要案情及申请理由依据和内容">${params.caseRec}</textarea></td>
                                          </tr>
                                        </table></td>
                                      </tr>
                                      <tr>
                                        <td><p>承办人 </p>
                                        <p>意见</p></td>
                                        <td colspan="4"><table width="100%" border="0" cellspacing="0" cellpadding="0">
                                          <tr>
                                            <td style="height:150px;">&nbsp;</td>
                                          </tr>
                                          <tr>
                                            <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
                                              <tr>
                                                <td>&nbsp;</td>
                                                <td>签名：</td>
                                              </tr>
                                              <tr>
                                                <td>&nbsp;</td>
                                                <td style="width:150px;">年</td>
                                                <td style="width:100px;">月</td>
                                                <td style="width:100px;">日</td>
                                              </tr>
                                            </table></td>
                                          </tr>
                                        </table></td>
                                      </tr>
                                      <tr>
                                        <td><p>承办机构负责人</p>
                                        <p>意见</p></td>
                                        <td colspan="4">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                          <tr>
                                            <td style="height:150px;">&nbsp;</td>
                                          </tr>
                                          <tr>
                                            <td>
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tr>
                                                    <td>&nbsp;</td>
                                                    <td>签名：</td>
                                                  </tr>
                                                  <tr>
                                                    <td>&nbsp;</td>
                                                    <td style="width:150px;">年</td>
                                                    <td style="width:100px;">月</td>
                                                    <td style="width:100px;">日</td>
                                                  </tr>
                                                </table>
                                            </td>
                                          </tr>
                                        </table>
                                      </td>
                                      </tr>
                                      <tr>
                                        <td><p>环保部门负责人</p>
                                        <p>审批意见</p></td>
                                        <td colspan="4">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                          <tr>
                                            <td style="height:150px;">&nbsp;</td>
                                          </tr>
                                          <tr>
                                            <td>
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tr>
                                                    <td>&nbsp;</td>
                                                    <td>签名：</td>
                                                  </tr>
                                                  <tr>
                                                    <td>&nbsp;</td>
                                                    <td style="width:150px;">年</td>
                                                    <td style="width:100px;">月</td>
                                                    <td style="width:100px;">日</td>
                                                  </tr>
                                                </table>
                                            </td>
                                          </tr>
                                        </table>
                                        </td>
                                      </tr>
                                    </table> 
                                    </form>     
								</div>
							</div>     
							<div style="float:right; margin:5px 0 0 0;" class="col-md-2">
								<button href="#" class="btn btn-info" data-toggle="modal" data-target="#wszd" data-remote="${webpath }/execuDoc/wszdModal?id=${docNum}">文书指导</button>&nbsp;
								<button class="btn btn-info" onClick="goBack()">返回</button>
							</div>
						</div>
						<div class="modal-footer">
							<c:if test="${not empty docId }">
								<c:if test="${empty hisReadOnly }">
									<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
									<button type="button" class="btn btn-info" data-toggle="modal" data-target="#scsmj"
									data-remote="${webpath }/execuDoc/uploadPicModal">上传扫描件</button>
								</c:if>
								<button type="button" class="btn btn-info" onclick="showPicModal()" tabindex="-1"
									>查看扫描件</button>
								<button type="button" class="btn btn-info" style="width:80px;" data-toggle="modal" 
								data-target="#inputImgModeler" data-remote="${webpath}/execuDoc/printModal?id=${docInfo.pdfUrl}">打印</button>
							</c:if>
							<c:if test="${empty docId }">
								<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
							</c:if>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 关联案件（Modal） -->
    <!-- <div class="modal fade" id="glaj" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                
            </div>
        </div>
    </div> -->
	<!-- 按日计罚案件关联（Modal） -->
	<div class="modal fade" id="arjfgl" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 上传扫描件（Modal） -->
	<div class="modal fade" id="scsmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 文书指导 -->
	<div class="modal fade" id="wszd" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	        </div>
	    </div>
	</div>

<!-- 附件预览  -->
<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-hidden="true">&times;</button>
				<h4 class="modal-title" id="myModalLabel">附件预览</h4>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			</div>

		</div>
	</div>
</div>

<script type="text/javascript"> 
$(document).ready(function(){
	//绑定textarea值和设置可回车
	$("[name='caseSource']").focus(function(){
		business.listenTextAreaComeEnter();
	})
	$("[name='caseSource']").blur(function(){
		business.listenTextAreaGoEnter();
	})
	
	$("[name='caseRec']").focus(function(){
		business.listenTextAreaComeEnter();
	})
	$("[name='caseRec']").blur(function(){
		business.listenTextAreaGoEnter();
	})
})

//保存校验
function wszzValidate(){
	var applyInfo = $("#applyInfo").val();
	var caseSource = $("#caseSource").val();
	var address = $("#address").val();
	var postalNumber = $("#postalNumber").val();
	var licenseNo = $("#licenseNo").val();
	var orgCode = $("#orgCode").val();
	var socialCreditCode = $("#socialCreditCode").val();
	var legalPerson = $("#legalPerson").val();
	var jobName = $("#jobName").val();
	var caseRec = $("#caseRec").val();
	if(applyInfo.length > 200){
		swal('提示','申请事项长度不能大于200！','info');
		return false;
	}
	if(caseSource.length > 200){
		swal('提示','案源长度不能大于200！','info');
		return false;
	}
	if(address.length > 200){
		swal('提示','当事人地址长度不能大于200！','info');
		return false;
	}
	if(postalNumber.length > 10){
		swal('提示','当事人邮编长度不能大于10！','info');
		return false;
	}
	if(licenseNo.length > 30){
		swal('提示','营业执照注册号（公民身份号码）长度不能大于30！','info');
		return false;
	}
	if(orgCode.length > 30){
		swal('提示','组织机构代码长度不能大于30！','info');
		return false;
	}
	if(socialCreditCode.length > 30){
		swal('提示','社会信用代码长度不能大于30！','info');
		return false;
	}
	if(legalPerson.length > 30){
		swal('提示','法定代表人（负责人）长度不能大于30！','info');
		return false;
	}
	if(jobName.length > 30){
		swal('提示','法定代表人（负责人）职务长度不能大于30！','info');
		return false;
	}
	if(caseRec.length > 2000){
		swal('提示','简要案情及申请理由依据和内容长度不能大于2000！','info');
		return false;
	}
	return true;
}
</script>		