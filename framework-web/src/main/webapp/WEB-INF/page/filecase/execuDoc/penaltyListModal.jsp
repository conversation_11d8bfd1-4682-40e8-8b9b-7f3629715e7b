<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<script type="text/javascript">
	$('#arjfgl').on('hide.bs.modal', function () {
		$(this).removeData("bs.modal");  
	})
</script>
<div class="modal-header">
    <div style="float:right; margin-top:-5px;">
    <button type="button" class="btn btn-info" data-dismiss="modal" onclick="confirmPenalInfo()">确定</button>
    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
    </div>
    <h4 class="modal-title" id="myModalLabel">按日计罚案件关联</h4>
</div>
<div class="modal-body">
    <p style="color:red;">如果没有所需关联的案件，先请案件填报人员新建该案件。</p>
    <table class="table table-striped table-no-bordered no-margin" id="dataPelTable">
	</table>
</div>
<script type="text/javascript">
$(document).ready(function(){
	LoadingPelDataList();
})
	
function confirmPenalInfo(){
	var selectPel = $('#dataPelTable').bootstrapTable('getSelections')[0];
	//showCaseInfo(selectCase);
	$("#penaltyDeciNum").val(selectPel.decisionNumber);
	$("#penaltyId").val(selectPel.id);
	
	$.ajax({
		method: 'post',
		async: false, //使用同步的方式,true为异步方式
		url: WEBPATH + '/execuDoc/getDesicionNum',
		data: {
			caseId: selectPel.id,
			caseType:'2-2'//2按日连续处罚决定书
		},
		dataType: 'json',
		success: function(data) {
			if(data.meta.code == 200) {
				if(data.data != null) {
					//alert(data.data);
					$("#name1").val(data.data);
				}
			}
		}
	})
}
	
function LoadingPelDataList() {
	var caseId = $("#caseId").val();
	$('#dataPelTable').bootstrapTable({
		method: 'post',
		dataType: "json", 
		url:  WEBPATH+'/execuDoc/getPenaltyList',
	    undefinedText : '-',  
	    pagination : true, // 分页  
	    striped : true, // 是否显示行间隔色  
	    cache : false, // 是否使用缓存  
	    pageSize: 5, // 设置默认分页为 20
	    pageNumber: 1,
	    clickToSelect:true,
	    queryParamsType: "",
	    pageList: [5,10, 25, 50, 100, 200], // 自定义分页列表
	    singleSelect: false,
	    contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	   // showColumns : true, // 显示隐藏列  
	    sidePagination: "server", //服务端请求
	    queryParams:  function (params) {
	        var temp = {
	    		 pageNum: params.pageNumber,
	             pageSize: params.pageSize,
	             caseId:caseId
	        };
	        return temp;
	   },//参数  
	    uniqueId : "id", // 每一行的唯一标识  
		 columns: [
	           {
	        	    field : "penaltyCaseNumber",
					title : "按日计罚案件编号",
					align : 'center'
	           },
	           {
					field : "decisionNumber",
					title : "决定书文号",
					align : 'center'
			   },
	           {
					field : "lawObjectName",
					title : "当事人名称",
					align : 'center'
	           },
	           {
	        	   	field : "punishSubject",
					title : "处罚单位",
					align : 'center'
	           },
	           {
	        	   	field : "decisionIssueDate",
					title : "决定下达日期",
					align : 'center',
					formatter: function (value, row, index) {
						var time = row.decisionIssueDate;
						if(time==null){
							return "";
						}else {
							var date = new Date(time);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                return y + '-' +m + '-' + d;
						}
	                }
	           },
	           {    
	        	  radio: true,
	        	  title: "操作",
	        	  align: 'center',
	           }
	       ],responseHandler : function(res) {  
               return {  
                   total : res.total,  
                   rows : res.list  
               };  
	       },
	       onCheck: function(row, $element) {
	       },//单击row事件
	       onDblClickCell:function(field,value,row, $element){
	       },//双击事件
		   onRefresh: function () {
		   },
	       onLoadSuccess:function(data){ 
	    	   var penaltyId = $("#penaltyId").val();
	    	   if(penaltyId != null && penaltyId != '' && typeof(penaltyId) != 'undefined'){
					var rows = data.rows;
					for(var j=0;j<rows.length;j++){
						if(rows[j].id==penaltyId){
							$('#dataPelTable').bootstrapTable('check', j);
						}
					}
	    	   }
	       },
	       formatLoadingMessage: function () {
	       		return "玩命加载中...";
	       },
	       formatNoMatches: function () { //没有匹配的结果
	       		return '无符合条件的记录';
	       }
	})
}
</script>