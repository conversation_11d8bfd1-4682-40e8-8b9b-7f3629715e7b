<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page language="java" import="java.util.*"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style type="text/css">
	.fixed-table-Container tbody td , .fixed-table-container, .bootstrap-table .table, .table>tbody>tr>th, .table>tfoot>tr>th,  .table>thead>tr>th{
		border:0px;
	}
	.table>tbody>tr>td,  .table>tfoot>tr>td,.table>thead>tr>td,{
		border-bottom:1px;
	}
</style>
<div class="main-container">
		<div class="padding-md">
              <input id="groundBackParams" name="groundBackParams" type="hidden" value='${docCaseParamSession }' />
           <div class="smart-widget">
			<div class="smart-widget-inner">
				<jsp:include page="../execuDocBusiness.jsp"></jsp:include>
				<div class="smart-widget-body">
					<div class="tab-content">
						<div class="tab-pane fade in active" id="style1Tab1">
                              	<div class="smart-widget-body form-horizontal">
                                      <div class="form-group">
                                          <label class="control-label col-lg-2">综合案件编号</label>
                                          <div class="col-lg-3">
                                              <input type="text" id="caseNumber" name="caseNumber" placeholder="综合案件编号" class="form-control" data-parsley-required="true">
                                          </div>
                                          <label class="control-label col-lg-2">当事人名称</label>
                                          <div class="col-lg-3">
                                              <input type="text" id="lawObjectName" name="lawObjectName" placeholder="当事人名称" class="form-control" data-parsley-required="true">
                                          </div>
                                      </div>
                                      <div class="form-group">
                                          <label class="control-label col-lg-2">案件名称</label>
                                          <div class="col-lg-3">
                                              <input type="text" id="caseName" name="caseName" placeholder="案件名称" class="form-control" data-parsley-required="true">
                                          </div>
                                          <label class="control-label col-lg-2">文书类型</label>
                                          <div class="col-lg-3">
                                              <select class="form-control" id="configDocumentId" name="configDocumentId">
                                                  <option value="">——请选择——</option>
                                                  <c:forEach var="doc" items="${documentNames }" varStatus="obj">
                                                       <option value="${doc.id }">${doc.documentName }</option>
                                                  </c:forEach>
                                              </select>
                                          </div>
                                      </div>
                                      <div class="form-group">
                                          <label class="control-label col-lg-2">文书创建时间</label>
                                          <div class="col-lg-3 no-padding">
                                          	<div class="col-lg-6">
                                              <input type="text" placeholder="开始时间" id="createDateStart" name="createDateStart" class="form-control" data-parsley-required="true" readonly="readonly">
                                              </div>
                                              <div class="col-lg-6">
                                              <input type="text" placeholder="结束时间" id="createDateEnd" name="createDateEnd" class="form-control" data-parsley-required="true" readonly="readonly">
                                              </div>
                                          </div>
                                          <label class="control-label col-lg-2"></label>
                                          <div class="col-lg-3 text-right">
                                              <button class="btn btn-info" type="submit" style="width:120px;" id="searchBtn">查询</button>
                                          </div>
                                      </div>
                                  </div>
                                  <table class="table table-striped table-no-bordered table-hover no-margin" id="dataTable">
                                     
                                  </table>                          		
						</div>
					</div>
				</div>
			</div>
		</div>          
                  
	</div>
</div>
		
<script type="text/javascript">
//使用Bootstrap
$(document).ready(function() {
	business.listenEnter("searchBtn");
	
	var pageNumber=1;
	var pageSize=10;
	//返回按钮之后参数和页码的回显
	var params = $("#groundBackParams").val();
	
	if(params != null && params != '' && params != 'undefined'){
		if(params.indexOf("=") >= 0){
			params = params.replaceAll("{", "{\"");
			params = params.replaceAll("=", "\":\"");
			params = params.replaceAll(", ", "\",\"");
			params = params.replaceAll("}", "\"}");
		}
		var paramsJson = $.parseJSON(params);
		
		pageNumber = parseInt(paramsJson['pageNum']);
		pageSize = parseInt(paramsJson['pageSize']);
			
		for(var key in paramsJson){
			$("[name='"+key+"']").val(paramsJson[key]);
		}
	}
	
	loadDataTable();
	//$('#dataTable').bootstrapTable('hideColumn', 'id');
	$('#dataTable').bootstrapTable('refresh',{pageNumber:pageNumber,pageSize:pageSize});
	
	
	//绑定搜索按钮
    $('#searchBtn').click(function() {
		//$('#dataTable').bootstrapTable('refresh');
		$('#dataTable').bootstrapTable('refreshOptions', {
			pageNumber : 1,
			pageSize : 10
		});
	}); 
});

function loadDataTable(){
	$('#dataTable').bootstrapTable({
		method : 'post',
		dataType : "json",
		url : WEBPATH+ '/execuDoc/getMyDocList',
		undefinedText : '-',
		pagination : true, // 分页  
		striped : true, // 是否显示行间隔色  
		cache : false, // 是否使用缓存  
		pageSize : 10, // 设置默认分页为 10
		pageNumber : 1,
		queryParamsType : "",
		locale : 'zh-CN',
		pageList : [ 5, 10, 20, 30, 50 ], // 自定义分页列表

		singleSelect : false,
		contentType : "application/x-www-form-urlencoded;charset=UTF-8",
		// showColumns : true, // 显示隐藏列  
		sidePagination : "server", //服务端请求
		queryParams : function(params) {
			var caseNumber = $("#caseNumber").val();
			var lawObjectName = $("#lawObjectName").val();
			var caseName = $("#caseName").val();
			var configDocumentId = $("#configDocumentId").val();
			var createDateStart = $("#createDateStart").val();
			var createDateEnd = $("#createDateEnd").val();
			var temp = {
				caseNumber:caseNumber,
				lawObjectName:lawObjectName,
				caseName:caseName,
				configDocumentId:configDocumentId,
				createDateStart:createDateStart,
				createDateEnd:createDateEnd,
				pageNum : params.pageNumber,
				pageSize : params.pageSize
			};
			return temp;
		},//参数
		uniqueId : "id", // 每一行的唯一标识  
		columns : [

				{
					field : "Number",
					title : "序号",
					align : 'center',
					formatter : function(
							value, row,
							index) {
						return index + 1;
					}
				},
				{
					field : "caseNumber",
					title : "综合案件编号",
					align : 'center'
				},
				{
					field : "lawObjectName",
					title : "当事人名称",
					align : 'center'
				},
				{
					field : "caseName",
					title : "案件名称",
					align : 'center',
					formatter: function(value,row,index){
						var htm = "";
						if(value!=null && value!='' && value!='undefined'){
							return '<a href="#" style="color:#23b7e5;" onclick="toCase(\''+ row.caseId+ '\')">'+value+'</a>';
						}else{
							return "-";
						}
					}
				},
				{
					field : "punishSubject",
					title : "处罚主体",
					align : 'center'
				},
				{
					field : "documentName",
					title : "文书类型",
					align : 'center'
				},
				{
					field : "createDate",
					title : "创建时间",
					align : 'center',
					formatter: function(value,row,index){
						var time = value;
						if(time==null){
							return "-";
						}else {
							var date = new Date(time);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                return y + '-' +m + '-' + d;
						}
					}
				},
				{
					field : "makeType",
					title : "文书制作方式",
					align : 'center',
					formatter : function(value,row,index) {
						
						if(value=='1'){
							return "表单制作";
						}else if(value=='2'){
							return "扫描件上传";
						}else{
							return "-";
						}
					}
				},
				{
					field : 'id',
					title : "操作",
					align : 'center',
					formatter : function(value, row,index) {
						var a = '<a href="#" onclick=showDetails("'+ row.id+'","'+row.configDocumentId+'","' +row.pageNo+'","'+row.makeType+'","'+row.caseId+'")><i class="fa fa-pencil-square-o" style="color:#23b7e5;">详情</i></a>';
						var b = '<a href="#" onclick=editDoc("'+ row.id+'","'+row.configDocumentId+'","' +row.pageNo+'","'+row.makeType+'","'+row.caseId+'")><i class="fa fa-pencil-square-o" style="color:#23b7e5;">编辑</i></a>';
						var c = '<a href="#" onclick="deleteDoc(\''+ row.id+ '\')"><i class="fa fa-times" style="color:red;">删除</i></a>';
						if(row.caseStatus==1){
							//已办结，没有编辑按钮
							return a + c;
						}else{
							return b +c;
						}
						
					}
				} ],
		responseHandler : function(res) {
			return {
				total : res.total,
				rows : res.list
			};
		},
		onCheck : function(row,
				$element) {

		},//单击row事件
		onUncheck : function(row,
				$element) {

		},
		onUncheckAll : function(row,
				$element) {

		},
		onCheckAll : function(row,
				$element) {

		},
		onRefresh : function() {

		},
		formatLoadingMessage : function() {
			return "玩命加载中...";
		},
		formatNoMatches : function() { //没有匹配的结果
			return '无符合条件的记录';
		}
	});
}

$("#createDateStart").datetimepicker({
	language:'cn',
	format:'yyyy-mm-dd',
	todayBtn: true,
    clearBtn:true,
	autoclose: true,
	minView:2,
	maxView:4
});

$("#createDateEnd").datetimepicker({
	language:'cn',
	format:'yyyy-mm-dd',
	todayBtn: true,
    clearBtn:true,
	autoclose: true,
	minView:2,
	maxView:4
});

//跳转到案件首页
function toCase(caseId){
	$.ajax({
		method:'POST',
		url:WEBPATH+'/caseInfo/checkStates',
		data:{id:caseId},
		success:function(data){
			if(data.isExit==200){
				//跳转到案件首页
				business.addMainContentParserHtml(WEBPATH+"/caseInfo/baseInfoCasePage?caseId="+caseId+"&selectType=1&parentUrl=0",null);
			}else if(data.isExit==404){
				swal("提示", "案件已不存在，无法查看！", "warning");
			}else{
				swal("提示", "系统错误，请重试！", "warning");
			}
		}
	});
}

function showDetails(docId,docNum,modelNum,makeType,caseId){
	//只读页面
	$("#caseId").val(caseId);
	business.addMainContentParserHtml(WEBPATH+"/execuDoc/toExecuDocPage?editType=0&docNum="+docNum+"&docId="+docId+"&modelNum="+modelNum+"&makeType="+makeType,$("#execuDocParams").serialize());
}

function editDoc(docId,docNum,modelNum,makeType,caseId){
	//编辑页面
	$("#caseId").val(caseId);
	business.addMainContentParserHtml(WEBPATH+"/execuDoc/toExecuDocPage?editType=1&docNum="+docNum+"&docId="+docId+"&modelNum="+modelNum+"&makeType="+makeType,$("#execuDocParams").serialize());
}

function deleteDoc(id){
	swal({
		title : "您确定要删除这条信息吗",
		text : "删除后将无法恢复，请谨慎操作！",
		type : "warning",
		showCancelButton : true,
		confirmButtonColor : "#DD6B55",
		confirmButtonText : "是的，我要删除！",
		cancelButtonText : "让我再考虑一下",
		closeOnConfirm : false,
		closeOnCancel : false
	},
	function(isConfirm) {
		if (isConfirm) {
			$.ajax({
				method:'POST',
				url:WEBPATH+'/execuDoc/deleteDocById',
				data:{id:id},
				success:function(data){
					
					console.info(data);
					if(data.meta.code==200){
						/* swal("提示", data.meta.message, "info"); */
						swal({
							title:"提示",
							text:data.meta.message,
							type:"info",
							allowOutsideClick :true
						});
						business.addMainContentParserHtml(WEBPATH + '/execuDoc/toMyDocPage', $("#execuDocParams").serialize());
					}else{
						/* swal("提示", data.meta.message, "warning"); */
						swal({
							title:"提示",
							text:data.meta.message,
							type:"warning",
							allowOutsideClick :true
						});
					}
				}
			});
	}else {
		swal({
			title : "已取消",
			text : "您取消了删除操作！",
			type : "error",
			allowOutsideClick :true
		});
	}
	
});
}
</script>		