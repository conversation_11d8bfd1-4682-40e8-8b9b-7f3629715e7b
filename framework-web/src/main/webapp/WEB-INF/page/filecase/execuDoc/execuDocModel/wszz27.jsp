<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page language="java" import="java.util.*"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<jsp:include page="publicJs.jsp"/>
<div class="main-container">
	<div class="padding-md">
	<!--第二层任务办理row-->
		<div class="row">
			<!--任务办理-->
			<div class="col-md-12">                                    
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
					    <i class="fa fa-arrow-right"></i> 责令停产整治决定书
					</div>
					<div class="smart-widget-inner table-responsive">
	                   	<div class="col-md-1"></div>
	                   	<div class="col-md-9">
							<div style="float:left; padding:5px 10px; width:100%;">
								<%-- <button type="button" id="relateCaseBtn" class="btn btn-info" data-toggle="modal" data-target="#glaj"
									data-remote="${webpath }/execuDoc/relateCaseModal">关联案件</button> --%>
								<div id="caseInfoDisplay" style="display:none;">
									<input type="hidden" id="docType" value="wszz27">
									<input type="hidden" id="docId" name="docId" value="${docId }"/>
									<input type="hidden" id="pageNo" name="pageNo" value="${pageNo }"/>
									<input type="hidden" id="docNum" name="docNum" value="${docNum }"/>
									<input type="hidden" id="caseId" name="caseId" value="${caseInfo.id }"/>
									<input type="hidden" id="mongoId" name="mongoId" value="${docInfo.mongodbId }"/>
									<p style="font-size:16px; padding-top:10px; font-weight:bold; color:#23B7E5;">
										<span id="divCaseName">${caseInfo.caseName }</span></p>
									<p style="margin:-5px 0 0 20px;"><span style="font-weight:bold;">案件编号：</span>
										<span id="divCaseNumber">${caseInfo.caseNumber }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">当事人名称：</span>
										<span id="divLawObjectName">${caseInfo.lawObjectName }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">处罚主体：</span>
										<span id="divPunishSubject">${caseInfo.punishSubject }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">调查机构：</span>
										<span id="divResearchOrgName">${caseInfo.researchOrgName }</span></p>
										<!-- 20180921文书制作改造注掉 start -->
									<%-- <p style="margin:0 0 0 20px;">
										<div style="font-weight:bold; margin:10px 0 0 20px;width:120px;float:left;">案件类型选择：</div>
										<div style="margin:10px 0 0 20px;">
											<div id="model1" style="display: none;float:left; margin-left:10px;">
												<div class="custom-checkbox">
											        <input type="checkbox" id="checkbox1" name="moduleName" ${hisReadOnly } value="1"> <label
											            for="checkbox1" class="checkbox-blue" checked></label>
											    </div>简易行政处罚
										    </div>
										    <div id="model2" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox2" name="moduleName" ${hisReadOnly } value="2"> <label
											            for="checkbox2" class="checkbox-blue" checked></label>
											    </div>一般行政处罚
										    </div>
										    <div id="model3" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox3" name="moduleName" ${hisReadOnly } value="3"> <label
											            for="checkbox3" class="checkbox-blue" checked></label>
											    </div>行政命令
										    </div>
										    <div id="model4" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox4" name="moduleName" ${hisReadOnly } value="4"> <label
											            for="checkbox4" class="checkbox-blue" checked></label>
											    </div>查封扣押
										    </div>
										    <div id="model5" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox5" name="moduleName" ${hisReadOnly } value="5"> <label
											            for="checkbox5" class="checkbox-blue" checked></label>
											    </div>限产停产
										    </div>
										    <div id="model6" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox6" name="moduleName" ${hisReadOnly } value="6"> <label
											            for="checkbox6" class="checkbox-blue" checked></label>
											    </div>行政拘留
										    </div>
										    <div id="model7" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox7" name="moduleName" ${hisReadOnly } value="7"> <label
											            for="checkbox7" class="checkbox-blue" checked></label>
											    </div>环境污染犯罪
										    </div>
										    <div id="model8" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox8" name="moduleName" ${hisReadOnly } value="8"> <label
											            for="checkbox8" class="checkbox-blue" checked></label>
											    </div>申请法院强制执行
										    </div>
										    <div id="model9" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox9" name="moduleName" ${hisReadOnly } value="9"> <label
											            for="checkbox9" class="checkbox-blue" checked></label>
											    </div>其他移送
										    </div>
										    <div id="model10" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox10" name="moduleName" ${hisReadOnly } value="10"> <label
											            for="checkbox10" class="checkbox-blue" checked></label>
											    </div>按日计罚
										    </div>
										</div>
									</p> --%>
									<!-- 20180921文书制作改造注掉 end -->
									<div class="form-group" style="float:left;display:none;padding-top:10px;padding-left:20px;" 
										id="penaltyRela">
										<label class="control-label" style="width:120px; padding:5px 0;">按日计罚案件关联：</label>
										<div class="input-group" style="width:400px; float:right;">
											<input type="hidden" id="penaltyId">
											<input type="text" class="form-control" id="penaltyDeciNum" readonly="readonly">
										    <div class="input-group-btn">
										        <button type="button" class="btn btn-info no-shadow" tabindex="-1" 
										        data-toggle="modal" data-target="#arjfgl" data-remote="${webpath }/execuDoc/toPenaltyListPage"
										        	>选择</button>
										    </div>
										</div>
									</div>
								</div>
							</div>
							<div class="smart-widget-body form-horizontal text-center font-14">
							<form id="compInfo">
								<div class="col-md-12 text-center">
                                    <p style="font-size:16px; margin-top:20px;"><c:if test="${empty params.userDept }">${userDept }</c:if>
	                                    	<c:if test="${not empty params.userDept }">${params.userDept }</c:if></p>
                                    <p style="font-size:16px;">责令停产整治决定书</p>
                                   <%--  <p style="font-size:14px; float:right;"><input type="zfzh" value="${params.approvalNum1 }" id="approvalNum1" name="approvalNum1" ${hisReadOnly } class="from_gaozhi" style="width:150px;outline:none;">环责停字〔
                                    	<input type="zfzh" value="${params.approvalNum2 }" id="approvalNum2" name="approvalNum2" ${hisReadOnly } class="from_gaozhi" style="width:100px;outline:none;">〕号</p> --%>
                                    	<p style="font-size:14px; float:right;">决定书文号：<input type="zfzh" value="${params.approvalNum1 }" id="approvalNum1" name="approvalNum1" ${hisReadOnly } class="from_gaozhi" style="width:150px;outline:none;">
                                    	</p>
                                    </div>
                                	<table width="100%" border="0" cellspacing="0" cellpadding="0" style="text-align:left; line-height:2em;">
                                      <tr>
                                        <td><input type="zfzh" value="${params.lawObjectName }" id="lawObjectName" name="lawObjectName" class="from_gaozhi"  ${hisReadOnly }
                                        	style="width:380px;outline:none;" placeholder="当事人名称或者姓名，与营业执照、居民身份证一致">：</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">
                                        	营业执照注册号（公民身份号码）：<input type="zfzh" value="${params.licenseNo }" ${hisReadOnly } id="licenseNo" name="licenseNo"class="from_gaozhi" style="width:200px;outline:none;">
                                        	组织机构代码证：<input id="orgCode" name="orgCode" value="${params.orgCode }" ${hisReadOnly } type="zfzh" class="from_gaozhi" style="width:200px;outline:none;">
                                        	社会信用代码：<input id="socialCreditCode" name="socialCreditCode" ${hisReadOnly } value="${params.socialCreditCode }" type="zfzh" class="from_gaozhi" style="width:200px;outline:none;">
                                        	地址：<input id="address" name="address" value="${params.address }" ${hisReadOnly } type="zfzh" class="from_gaozhi" style="width:200px;outline:none;">
                                        	法定代表人（负责人）：<input id="legalPerson" name="legalPerson" ${hisReadOnly } value="${params.legalPerson }" type="zfzh" class="from_gaozhi" style="width:200px;outline:none;"></td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;"><input type="zfzh" id="bureaus1" name="bureaus1"  value="${params.bureaus1 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>于
                                        		<input type="zfzh" ${hisReadOnly }  id="docDate1" name="docDate1" readonly="readonly" class="from_gaozhi" style="width:120px;text-align: center;" value="${params.docDate1 }"  placeholder="xxxx年xx月xx日"/>
                                        		对你（单位）进行了调查，发现你（单位)实施了以下环境违法行为：</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;"><input type="zfzh" value="${params.illegalFact }" id="illegalFact" name="illegalFact" class="from_gaozhi"  ${hisReadOnly }
                                        	style="outline:none;width:100%;" placeholder="（陈述违法事实，如违法行为发生的时间、地点、情节、动机、危害后果等内容）"></td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">以上事实，有<input type="zfzh" value="${params.illegalBasis }" id="illegalBasis" name="illegalBasis"  ${hisReadOnly }
                                        	class="from_gaozhi" style="width:500px;outline:none;" placeholder="（列举证据形式，阐述证据所要证明的内容）">等证据为凭。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">依据《中华人民共和国环境保护法》第六十条的规定，本机关决定责令你（单位）停产整治。改正方式包括：
                                        <input id="correctFunc" name="correctFunc" value="${params.correctFunc }" ${hisReadOnly } type="zfzh" class="from_gaozhi" style="width:500px;outline:none;">的规定。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">你（单位）应当在收到本决定书后立即整改，并在15个工作日内将整改方案报<input type="zfzh" id="bureaus2" name="bureaus2"  value="${params.bureaus2 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>备案并向社会公开。</td>
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">你（单位）完成整改任务后，应当在15个工作日内将整改任务完成情况和整改信息社会公开情况，报<input type="zfzh" id="bureaus3" name="bureaus3"  value="${params.bureaus3 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>备案，并提交监测报告以及整改期间生产用电量、用水量、主要产品产量与整改前的对比情况等材料。停产整治决定自报<input type="zfzh" id="bureaus4" name="bureaus4"  value="${params.bureaus4 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>备案之日起解除。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;"><input type="zfzh" id="bureaus5" name="bureaus5"  value="${params.bureaus5 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>将依法对你（单位）履行停产整治措施的情况实施后督察，并依法作出处理或处罚。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">如你（单位）对本决定不服，可以在收到本决定书之日起60日内向
                                        	<input type="zfzh" value="${params.someGov }" id="someGov" name="someGov" ${hisReadOnly } placeholder="上级环保部门" class="from_gaozhi" style="width:200px;outline:none;">
                                        	或者
                                        	<input type="zfzh" value="${params.someDept }" id="someDept" name="someDept" ${hisReadOnly } placeholder="本级人民政府" class="from_gaozhi" style="width:200px;outline:none;">
                                        	申请行政复议，也可以在收到本决定书之日起6个月内向
                                        	<input type="zfzh" value="${params.someCourt }" id="someCourt" name="someCourt" ${hisReadOnly } placeholder="法院" class="from_gaozhi" style="width:200px;outline:none;">
                                        	提起行政诉讼。</td>
                                      </tr>
                                      <tr>
                                      	<td class="text-right"><input type="zfzh" value="<c:if test="${empty params.userDept }">${userDept }</c:if><c:if test="${not empty params.userDept }">${params.userDept }</c:if>" 
	                                    		id="userDept" name="userDept" ${hisReadOnly } placeholder="×××环境保护厅（局）" class="from_gaozhi" style="width:200px;outline:none;">
	                                    	</td>
                                      </tr>
                                      <tr>
                                        <td class="text-right" style="word-spacing:2em;">
                                        	<input type="zfzh" ${hisReadOnly }  id="docDate" name="docDate" readonly="readonly" class="from_gaozhi" style="width:120px;text-align: center;" value="${params.docDate }"  placeholder="xxxx年xx月xx日"/>
                                        </td>
                                      </tr>
                                    </table>     
                                    </form>
								</div>
							</div>     
							<div style="float:right; margin:5px 0 0 0;" class="col-md-2">
								<button href="#" class="btn btn-info" data-toggle="modal" data-target="#wszd" data-remote="${webpath }/execuDoc/wszdModal?id=${docNum}">文书指导</button>&nbsp;
								<button class="btn btn-info" onClick="goBack()">返回</button>
							</div>
						</div>
						<div class="modal-footer">
							<c:if test="${not empty docId }">
								<c:if test="${empty hisReadOnly }">
									<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
									<button type="button" class="btn btn-info" data-toggle="modal" data-target="#scsmj"
									data-remote="${webpath }/execuDoc/uploadPicModal">上传扫描件</button>
								</c:if>
								<button type="button" class="btn btn-info" onclick="showPicModal()" tabindex="-1"
									>查看扫描件</button>
								<button type="button" class="btn btn-info" style="width:80px;" data-toggle="modal" 
								data-target="#inputImgModeler" data-remote="${webpath}/execuDoc/printModal?id=${docInfo.pdfUrl}">打印</button>
							</c:if>
							<c:if test="${empty docId }">
								<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
							</c:if>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 关联案件（Modal） -->
    <!-- <div class="modal fade" id="glaj" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                
            </div>
        </div>
    </div> -->
	<!-- 按日计罚案件关联（Modal） -->
	<div class="modal fade" id="arjfgl" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 上传扫描件（Modal） -->
	<div class="modal fade" id="scsmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 文书指导 -->
	<div class="modal fade" id="wszd" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	        </div>
	    </div>
	</div>

<!-- 附件预览  -->
<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-hidden="true">&times;</button>
				<h4 class="modal-title" id="myModalLabel">附件预览</h4>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			</div>

		</div>
	</div>
</div>

<script type="text/javascript"> 

$(document).ready(function(){
	var day1 = new Date();
    day1.setTime(day1.getTime());
    var d = day1.getDate();
    if (d < 10){d = "0"+d;}
    var s1 = day1.getFullYear()+"年" + (day1.getMonth()+1) + "月" + d + "日";
    if($("#docDate").val() == null || $("#docDate").val() == ''){
	    $("#docDate").val(s1);
    }
    $("#docDate").datetimepicker({
		language:'cn',
	    format:'yyyy-mm-dd',
	    autoclose: true,
	    minView:'month',
	    maxView:'decade',
	    todayBtn:true,
	    clearBtn:true
	 }).on('hide',function(ev){
		 var date = $("#docDate").val();
		 if(date != ''){
			 $("#docDate").val(date.split('-')[0] + '年' + date.split('-')[1] + '月' + date.split('-')[2] + '日');
		 }
	 }).on('show',function(ev){
		 var date = $("#docDate").val();
		 if(date != ''){
			 $("#docDate").val(date.substring(0,4) + '-' + date.substring(5,(date.indexOf('月'))) 
					 + '-' + date.substring((date.indexOf('月')+1),date.indexOf('日')));
			 $("#docDate").datetimepicker('update');
		 }
	 });
    
    $("#docDate1").datetimepicker({
		language:'cn',
	    format:'yyyy-mm-dd',
	    autoclose: true,
	    minView:'month',
	    maxView:'decade',
	    todayBtn:true,
	    clearBtn:true
	 }).on('hide',function(ev){
		 var date = $("#docDate1").val();
		 if(date != ''){
			 $("#docDate1").val(date.split('-')[0] + '年' + date.split('-')[1] + '月' + date.split('-')[2] + '日');
		 }
	 }).on('show',function(ev){
		 var date = $("#docDate1").val();
		 if(date != ''){
			 $("#docDate1").val(date.substring(0,4) + '-' + date.substring(5,(date.indexOf('月'))) 
					 + '-' + date.substring((date.indexOf('月')+1),date.indexOf('日')));
			 $("#docDate1").datetimepicker('update');
		 }
	 });
})



/**
 * 必填项和长度校验
 */
function wszzValidate(){
	var approvalNum1 = $("#approvalNum1").val();
	if(typeof(approvalNum1) == 'undefined' ||approvalNum1==null ||approvalNum1==""){
	 	//swal({title:'提示',text:'环责停号不能为空',type:'info'});
	 	swal({title:'提示',text:'决定书文号不能为空',type:'info'});
	 	return false;
	}
	if(approvalNum1.length>60){
		//swal({title:'提示',text:'环责停号最长60字符',type:'info'});
		swal({title:'提示',text:'决定书文号长度不能大于60！',type:'info'});
		return false;
	}
	/*var approvalNum2 =  $("#approvalNum1").val();
	if(typeof(approvalNum2) == 'undefined' ||approvalNum2==null ||approvalNum2==""){
	 	swal({title:'提示',text:'环责停号不能为空',type:'info'});
	 	return false;
	}
	if(approvalNum2.length>10){
		swal({title:'提示',text:'环责停号最长10字符',type:'info'});
		return false;
	}*/
	
	var lawObjectName = $("#lawObjectName").val();
	if(typeof(lawObjectName) == 'undefined' ||lawObjectName==null ||lawObjectName==""){
	 	swal({title:'提示',text:'企业名称不能为空',type:'info'});
	 	return false;
	}
	if(lawObjectName.length>100){
		swal({title:'提示',text:'企业名称最长100字符',type:'info'});
		return false;
	}
	var licenseNo  =  $("#licenseNo").val();
	if(licenseNo.length>30){
		swal({title:'提示',text:'营业执照注册号最长30字符',type:'info'});
		return false;
	}
	var orgCode = $("#orgCode").val();
	if(orgCode.length>30){
		swal({title:'提示',text:'组织机构代码证最长30字符',type:'info'});
		return false;
	}
	var socialCreditCode = $("#socialCreditCode").val();
	if(socialCreditCode.length>30){
		swal({title:'提示',text:'社会信用代码最长30字符',type:'info'});
		return false;
	}
	var address = $("#address").val();
	if(address.length>200){
		swal({title:'提示',text:' 地址最长200字符',type:'info'});
		return false;
	}
	var legalPerson = $("#legalPerson").val();
	if(legalPerson.length>100){
		swal({title:'提示',text:' 法定代表人100字符',type:'info'});
		return false;
	}
 
	var illegalFact = $("#illegalFact").val();
	if(illegalFact.length>200){
		swal({title:'提示',text:'陈述违法事实最长200字符',type:'info'});
		return false;
	}
	var illegalBasis = $("#illegalBasis").val();
	if(illegalBasis.length>100){
		swal({title:'提示',text:'列举证据形式最长100字符',type:'info'});
		return false;
	}
	var correctFunc = $("#correctFunc").val();
	if(correctFunc.length>100){
		swal({title:'提示',text:'改正方式最长100字符',type:'info'});
		return false;
	}
	var someGov = $("#someGov").val();
	if(someGov.length>20){
		swal({title:'提示',text:' 申请复议单位名称最长20字符',type:'info'});
		return false;
	}
	
	var someDept = $("#someDept").val();
	if(someDept.length>20){
		swal({title:'提示',text:' 申请复议单位名称最长20字符',type:'info'});
		return false;
	}
	
	var someCourt = $("#someCourt").val();
	if(someCourt.length>20){
		swal({title:'提示',text:' 申请复议单位名称最长20字符',type:'info'});
		return false;
	}
	
	return true;
}

//正向引用加载文书必填项
function getStopDocInfo(){
	
}
</script>		