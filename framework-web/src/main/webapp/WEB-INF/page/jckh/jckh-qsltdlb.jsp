<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta charset="utf-8">
    <title>福建省环境监察执法平台</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<script src="${webpath }/static/js/tableExport.min.js"  type="text/javascript"></script>
	<style type="text/css">
		.fixed-table-container thead th .th-inner {
			white-space : normal;
		}
		table.table-expandable > tbody > tr div.table-expandable-arrow.up {
		    background-position:0px 0px;
		}
	</style>
</head>
	<script type="text/javascript">
		$(function(){
			//监听回退键
			business.listenBackSpace();    
		});
	</script>
<body>
<div class="main-container">
	<div class="padding-md">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
        <div class="row">
            <div class="col-lg-12 text-right form-inline no-margin">
            	<div class="smart-widget">
					<div class="smart-widget-inner">
						<div class="smart-widget-body">
							<div class="col-lg-12 col-md-12">
			                    <%--<div style="float:left; font-size:14px; color:red; padding:10px 0 0 0;" id="friendMess">当前统计数据起始自XX年XX月XX日0:00，截止到XX年XX月XX日24:00。</div>--%>
			                </div>
				            <div class="form-group" id="citySumDiv"  type="hidden" > 
				               <div class="custom-checkbox" style="margin:7px auto;">
				                	<input type="hidden" id="citySum" name="citySum" value="0">
				                    <input type="checkbox"  id="checkLevel" > 
				                    <label  for="checkLevel" class="checkbox-blue" ></label>
				               </div> 设区市汇总   
				            </div>
							<div class="form-group">
								<select class="form-control"  id ='belongProvince' name ="belongProvince">
									<option value="35000000">福建省</option>
								</select>
							</div>
							<div class="form-group">
								<select class="form-control"  id ="belongCity" name="belongCity">
			                              
								</select>
							</div>
							<div class="form-group">
								<select class="form-control"  id ="belongCountry" name ="belongCountry">
			                              
								</select>
							</div>
							<div class="form-group">
								<select class="form-control" id="selectDate" name="selectDate">
									<option value="thisYear">本年度</option>
									<option value="thisMonth" selected >本月</option>
									<option value="lastWeek">最近7天</option>
									<option value="lastTwoWeek">最近2周</option>
									<option value="lastMonth">最近30天</option>
									<option value="beforeMonth">上个月</option>
									<option value="lastThreeMonth">最近3个月</option>
									<option value="lastYear">最近12个月</option>
									<option value="yesterday">昨天</option>
									<option value="customDate">自定义时间</option>
								</select>
							</div>
							<div class="form-group">
								<div class="input-group">
									<!-- <input type="text" value="2017.01.01-2017.04.12" class="datepicker-input form-control"> -->
									<input type="hidden" readonly="readonly" value="" id="beginDate" name="beginDate"
									placeholder="起始时间" class="datepicker-input form-control" data-parsley-required="true">
								</div>
								<div class="input-group">
									<input type="hidden" readonly="readonly" value="" id="endDate" name="endDate"
									placeholder="结束时间" class="datepicker-input form-control" data-parsley-required="true">
									<!-- <span class="input-group-addon"><i class="fa fa-calendar"></i></span> -->
								</div>
							</div>
							<div class="form-group">
								<div class="padding-sm" style="float: left;">
									<button type="button" class="btn btn-info btn-block" onclick="selectCount()">查询统计</button>
								</div>
							</div>
			                <!-- <div class="form-group">
			                	<button onclick ="exportTable()" type="button" class="btn btn-info no-shadow">导出EXCEL</button>
			                </div> -->
                		</div>
                	</div>
                </div>
            </div>
        </div>           
                 <div class="row">                         
                     <div class="col-lg-12">  
                     	 <div class="smart-widget-body form-horizontal">
		                   <div class="form-group">
		                   		<div class="col-lg-12 text-right">
		                   			<button onclick ="exportTable()" type="button" class="btn btn-info no-shadow" style="margin-right: 10px;">导出EXCEL</button>
			                	</div>
			                </div>
		                </div>                                  
                         <div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
						<i class="fa fa-commenting"></i> 清水蓝天大练兵统计调度统计
					</div>
					<div class="smart-widget-inner">
						<div class="smart-widget-body no-padding">
							<div class="padding-md table-responsive">
								<table id="qsltdlbTable" class =" table table-bordered">
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var title = '考核人员基数';
	var titleDay = null;
	var titleTask = null;
	$(function(){


		var htmlCity = "<option value=''>请选择</option>"; 
		var htmlCounty = "<option value=''>请选择</option>"; 
		$.ajax({
	        type:"post",
	        url:WEBPATH+"/tArea/chickUserArea",
	        dataType:"json",
	        data:{},
	        async:false,
	        success:function(list){
	        	if(list.cityStatus =='1'){
					//省级用户
					$.ajax({
						type:"post",
						url:WEBPATH+"/tArea/cityList",
						dataType:"json",
						async:false,
						success:function(data){
							$("#belongCity").append("<option value=''>请选择</option>");
							$("#belongCountry").append("<option value=''>请选择</option>"); 
							$.each(data,function(i,item){
								$("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
							});
						}
					});
	        	}else if(list.cityStatus =="2"){
	        		//市级用户
	        		$("#belongCity").append("<option selected value="+list.cityCode+">"+list.cityName+"</option>");
	        	    $.ajax({
	                    type:"post",
	                    url:WEBPATH+"/tArea/countyListByCode",
	                    dataType:"json",
	                    async:false,
	                    data:{parentCode:list.cityCode},
	                    success:function(data){
							$("#belongCountry").append("<option value=''>请选择</option>"); 
							$.each(data,function(i,item){
								$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
							});
	                    }
	                });
	        	}else{
	        		//县级用户
	        		$("#belongCity").append("<option selected value="+list.cityCode+">"+list.cityName+"</option>");
					$("#belongCountry").append("<option selected value="+list.countyCode+"  >"+list.countyName+"</option>");
	        	}
	        }
	    });
		
		$("#belongCity").change(function(){
			if ($(this).val() == ""){
				$("#belongCountry option").remove();
				$("#belongCountry").html(htmlCounty);
				return;
			}
			var parentCode = $(this).val();
			$("#belongCountry option").remove();
			$.ajax({
				type:"post",
				url:WEBPATH+"/tArea/countyListByCode",
				dataType:"json",
				async:false,
				data:{parentCode:parentCode},
				success:function(data){
					$("#belongCountry").html(htmlCounty);
					$.each(data,function(i,item){
						$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
					});
				}
			});
		});
		
		//自定义时间段
		$("#beginDate").datetimepicker({
			language:'cn',
		    format:'yyyy-mm-dd',
		    todayBtn: true,
		    autoclose: true,
		    minView:'year',
		    maxView:'decade',
		    endDate:new Date()
		 });
		$("#beginDate").change(function(){
			var beginDate = $("#beginDate").val();
			//var now = new Date();
			$("#endDate").datetimepicker('remove');
			$("#endDate").val(business.getCurrentDate());
			$("#endDate").datetimepicker({
				language:'cn',
				format:'yyyy-mm-dd',
				todayBtn: true,
				autoclose: true,
				minView:'year',
				maxView:'decade',
				startDate:new Date(beginDate),
				endDate:new Date()
			})
		 });
		
		$("#selectDate").change(function(){
			var selectDate = $("#selectDate").val();
			if(selectDate == 'customDate'){
				$("#beginDate").attr("type","text");
				$("#endDate").attr("type","text");
			} else {
				$("#beginDate").val("");
				$("#endDate").val("");
				$("#beginDate").attr("type","hidden");
				$("#endDate").attr("type","hidden");
			}
		});
		//返回按钮之后参数和页码的回显
		var params = $("#groundBackParams").val();
		//console.log("返回参数："+params);
		if(params != null && params != '' && params != 'undefined'){
			var jsonParam = $.parseJSON(params);
			for(var key in jsonParam){
				//绑定设定条件
				if(key=='belongCity'){
					$("#"+key).find("option[value='"+jsonParam[key]+"']").attr("selected","selected");
					//$("#"+key).val(jsonParam[key]);
					$("#"+key).trigger('change');
					continue;
				}
				$("#"+key).val(jsonParam[key]);
			}
		}
		
		// 如果我可以更改市的下拉框选中，则证明我是省级人员
		var userBelongAreaId = '${sysUsers.belongAreaId}'; 
		if(userBelongAreaId=="35000000"){
			$("#citySumDiv").show();
			$("#citySum").val("1");
			 // 是否选中 设区市汇总
		 	$("#checkLevel").change(function() { 
				if( $("#checkLevel").is(':checked')==true ){
					$("#citySum").val('1');
					 $("#belongProvince").prop("disabled", true);
					 $("#belongCity").prop("disabled", true);
					 $("#belongCountry").prop("disabled", true);
				}else{
					$("#citySum").val('0');
					 $("#belongProvince").prop("disabled", false);
					 $("#belongCity").prop("disabled", false);
					 $("#belongCountry").prop("disabled", false);
				}
			}); 
			 
		 	var userCitySum = $("#citySum").val(); 
			if(userCitySum=="1"){
				 $("#belongProvince").prop("disabled", true);
				 $("#belongCity").prop("disabled", true);
				 $("#belongCountry").prop("disabled", true);
		 
				 $("#checkLevel").attr('checked','checked');
				 $("#checkLevel").trigger('change');
			} 
		}else{
			$("#citySumDiv").hide();
		}
		
		getStaDate();


	});
	
	function selectCount(){
		if($("#selectDate").val()=='customDate' &&
				($("#beginDate").val()==null||$("#beginDate").val()==''
					||$("#endDate").val()==null||$("#endDate").val()=='')){
			swal("提示","自定义时间段不能为空，请重新选择。","info");
			return false;
		}
		getStaDate();
		//$("#qsltdlbTable").bootstrapTable('refresh');
	};
	
	function loadingDetailTable(){
		$("#qsltdlbTable").bootstrapTable({
			method: 'post',
			dataType: "json", 
			url:  WEBPATH+'/jckh/getSpecialBlueData',
			undefinedText : '-',  
			pagination : false, // 分页  
			striped : true, // 是否显示行间隔色  
			cache : false, // 是否使用缓存  
			async:true,
			//queryParamsType: "",
			singleSelect: false,
			contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			sidePagination: "server", //服务端请求
			//showFooter:true,//显示列脚
			queryParams:function (params) {
	            var temp = {
	            	belongProvince:$("#belongProvince").val(),
	      			belongCity:$("#belongCity").val(),
		        	belongCountry:$("#belongCountry").val(),
		        	beginDate:$("#beginDate").val(),
		        	endDate:$("#endDate").val(),
		        	selectDate:$("#selectDate").val(),
		        	citySum:$("#citySum").val()
	            };
	            return temp;
	     	},//参数
	     	uniqueId : "belongCode", // 每一行的唯一标识
			columns: [[
				{
					field: "name",
					title: "行政区",
					align: "center",
					rowspan:2,

				},{
					field: "city",
					title: "<span data-toggle='tooltip' data-placement='bottom'  title='包含行政区本级所有部门所有人员完成的执法信息。'>本级行政区</span>",
					align: 'center',
					rowspan:2,
					formatter : function(value, row,index) {
						var citySum = $("#citySum").val();
						if(citySum!=0){
							value='-'
						}else {
							value = value;
						}
						return value;
					}
				},{
					field: "awayNum",
					title: "出动人次",
					align: 'center',
					rowspan:2,
				},{
					field: "checkNum",
					title: "检查家次",
					align: 'center',
					rowspan:2,
				},
				{
					field: "quesNum",
					title: "发现问题家次",
					align: "center",
					rowspan:2,
				},{
					field: "ncodeNum",
					title: "拟行政处罚数",
					align: 'center',
					rowspan:2,

				},{
					field: "nfiveNum",
					title: "拟办五类案件数",
					align: 'center',
					rowspan:2,
				},
				{
					field: "",
					title: "拟配套和涉及案件",
					align: 'center',
					rowspan:1,
					colspan:5

				},
				{
					field: "nclampNum",
					title: "拟取缔关闭数",
					align: "center",
					rowspan:2,
				},
				{
					field: "",
					title: "拟污染类型",
					align: 'center',
					rowspan:1,
					colspan:3

				},

			],[
				{
					field: "npdayNum",
					title: "拟按日计罚数",
					align: "center",
					rowspan:1,
					colspan:1
				},{
					field: "nphaltNum",
					title: "拟停产限产数",
					align: 'center',
					rowspan:1,
					colspan:1
				},{
					field: "npcloseNum",
					title: "拟查封扣押数",
					align: 'center',
					rowspan:1,
					colspan:1
				},{
					field: "npcodeNum",
					title: "拟行政拘留数",
					align: "center",
					rowspan:1,
					colspan:1
				},{
					field: "npcimeNum",
					title: "拟移送涉嫌犯罪查处数",
					align: "center",
					rowspan:1,
					colspan:1
				},{
					field: "waterNum",
					title: "拟涉水污染企业",
					align: "center",
					rowspan:1,
					colspan:1
				},{
					field: "airNum",
					title: "涉气污染企业",
					align: "center",
					rowspan:1,
					colspan:1
				},{
					field: "soidNum",
					title: "涉固废污染企业",
					align: "center",
					rowspan:1,
					colspan:1
				}
		]
			],
			responseHandler : function(res) {
				return {  
				    //total : res.size,  
				    rows : res
            	};  
			},
			onCheck: function(row, $element) {
			},//单击row事件
			onUncheck: function(row, $element) {
			},
			rowStyle:function(row, index){
				var style = {};             
				/* if(parseFloat(row.lawUserDayCount)<4 || parseFloat(row.lawTimesUserCount)<4){
					style={css:{'color':'red'}};//“人均执法天数”小于4天的，整行字体标为红色
				} */
				if(row.onJobRandomUserCount == 0){
					style={classes:'hideRow'};//如果在编在岗且纳入双随机人数为0，隐藏一行
				}
				if(row.belongCode != null && row.belongCode.length != 8) {
					style={classes:'childRow'}
				}
				return style;
			},
			formatLoadingMessage: function () {
				return "数据加载中...";
			},
			formatNoMatches: function () { //没有匹配的结果
				return '无符合条件的记录';
			},
			onLoadSuccess:function(data){
				$("#qsltdlbTable").bootstrapTable('hideColumn','belongCode');
				$(".hideRow").hide();//如果在编在岗且纳入双随机人数为0，隐藏一行
			}
		});

	}
	
	//跳转到详细每个人的数据页面
	function showDetailDataByArea(belongCode) {
		business.addMainContentParserHtml(WEBPATH + '/jckh/toPersonPage?belongCode='+belongCode, null);
	}
	
	//改变小三角样式并且伸缩插入行
	function showChildDept(belongCode, index){
		//判断当前小三角的状态
		debugger;
		business.openwait();
		var background = document.getElementById("xsj"+belongCode).style.background;
		//chrome下是上面这个，IE下是下面这个
		if(background == 'url("/static/img/arrows1.png") 0px -16px no-repeat scroll transparent' ||
				background == 'url("/static/img/arrows1.png") no-repeat 0px -16px'){
			//打开小三角，插入数据
			//获取所有的已打开的行，删除
			var childRows = $(".childRow");
			
			//更改小三角状态
			background = 'url("/static/img/arrows1.png") 0px 0px no-repeat scroll transparent';
			
			//插入行数据
			var belongCodeStr = $("[data-index='"+index+"']").attr('data-uniqueid');
			var tmp = {beginDate:$("#beginDate").val(),
	        	endDate:$("#endDate").val(),
	        	selectDate:$("#selectDate").val(),
	        	belongCode: belongCodeStr};
			$.ajax({
				type:'post',
				url:WEBPATH+"/jckh/getDataByBelongCode",
	            dataType:"json",
	            data:tmp,
	            async:false,
	            success:function(data){
					if(data.meta.code == '200'){
						var list = data.data;
						for(var i = 0; i < list.length; i++){
							var row = $.parseJSON(JSON.stringify(list[i]));
							$("#qsltdlbTable").bootstrapTable('insertRow', {index:Number(index)+1+i,row:row});
						}
					}
	            }
			});
			for(var i = 0; i < childRows.length; i++) {
				var belongCodeUnic = $(childRows[i]).attr('data-uniqueid');
				if(belongCodeUnic != null && belongCodeUnic.length != 8){
					$("#qsltdlbTable").bootstrapTable('removeByUniqueId', belongCodeUnic);
				}
			}
			$(".childRow").css("color","#23b7e5");
			$(".hideRow").hide();
		} else {
			background = 'url("/static/img/arrows1.png") 0px -16px no-repeat scroll transparent';
			var childRows = $(".childRow");
			for(var i = 0; i < childRows.length; i++) {
				var belongCodeUnic = $(childRows[i]).attr('data-uniqueid');
				if(belongCodeUnic != null && belongCodeUnic.length != 8){
					$("#qsltdlbTable").bootstrapTable('removeByUniqueId', belongCodeUnic);
				}
			}
			$(".hideRow").hide();
		}
		//用index判断然后修改，不然在这个过程中因为删除过一部分，所以导致belongCode会乱
		//document.getElementById("xsj"+belongCode).style.background = background;
		$("[data-index='"+index+"'] i").css('background',background);
		business.closewait();
	}

	function getStaDate(){
		var endDateStr;
		var tmp = {beginDate:$("#beginDate").val(),
	        	endDate:$("#endDate").val(),
	        	selectDate:$("#selectDate").val()};
		$.ajax({
			type:'post',
			url:WEBPATH+"/jckh/getStaDate",
            dataType:"json",
            data:tmp,
            async:false,
            success:function(data){
				if(data.meta.code == '200'){
					// $("#friendMess").html('当前统计数据起始自'+data.data.beginDate+' 00:00，截止到'+data.data.endDate+' 24:00。')
					endDateStr = data.data.endDate;
				}
            }
		});
		//在编在岗且纳入双随机人数这个字段名字随着所选日期动态更改
		var endDate = new Date(Date.parse(endDateStr.replace(/-/g,  "/")));
		
		if(endDate < new Date(Date.parse("2018/02/01"))){
			title = "<span data-toggle='tooltip' data-placement='bottom'  title='截止统计时间段终点，该行政区本级在编在岗且纳入双随机人数总和。'>在编在岗且纳入双随机人数</span>";
			//$("#qsltdlbTable").bootstrapTable('resetView');
			titleDay = "<span data-toggle='tooltip' data-placement='bottom'  title='执法任务办结数/在编在岗且纳入双随机人数'>人均执法天数</span>";
			titleTask = "<span data-toggle='tooltip' data-placement='bottom'  title='执法检查人天总数/在编在岗且纳入双随机人数'>人均执法记录数</span>";
			$("#qsltdlbTable").bootstrapTable('destroy');
			loadingDetailTable();
		} else {
			title = "<span data-toggle='tooltip' data-placement='bottom'  title='截止统计时间段终点，该行政区本级考核人员基数总和。'>考核人员基数</span>";
			titleDay = "<span data-toggle='tooltip' data-placement='bottom'  title='执法任务办结数/考核人员基数'>人均执法天数</span>";
			 titleTask = "<span data-toggle='tooltip' data-placement='bottom'  title='执法检查人天总数/考核人员基数'>人均执法记录数</span>";
			$("#qsltdlbTable").bootstrapTable('destroy');
			loadingDetailTable();
		}
	}
	
	/* //获取汇总数据放在最后一行
	function getCount(data){
		var obj={deptUserCount:0,
				deptLawUserCount:0,
				checkObjectCount:0,
				allLawTimes:0,
				//lawTaskCount:0,
				//lawPassCount:0, 
				lawObjCount:0,
				lawObjUpdateCount:0,
				onlineUserCount:0
		}
		$.each(data.rows,function(i,item){
			obj.deptUserCount += item.deptUserCount;
			obj.deptLawUserCount += item.deptLawUserCount;
			obj.checkObjectCount += item.checkObjectCount;
			obj.allLawTimes += item.allLawTimes;
			//obj.lawTaskCount += item.lawTaskCount;
			//obj.lawPassCount += item.lawPassCount;
			if(i==0){
				obj.lawObjCount = item.lawObjCount;
			}
			obj.lawObjUpdateCount += item.lawObjUpdateCount;
			obj.onlineUserCount += item.onlineUserCount;
		})
		return obj;
	} */
	
	//导出页面表单
	function exportTable(){
		$('#qsltdlbTable').tableExport({
			type:'excel',
			escape:'false',
			fileName:fileName(),
			ignoreColumn: []
		});
	}
		
	function fileName(){
	 	var userCitySum = $("#citySum").val(); 
		if(userCitySum=="1"){
			return '福建省清水蓝天大练兵统计调度统计情况';
		}
		
		if($('#belongCountry').val()!=null && $('#belongCountry').val() != ''){
			return '福建省'+$('#belongCity option:selected').text()+$('#belongCountry option:selected').text()+"清水蓝天大练兵统计调度统计情况";
		} else if ($('#belongCity').val()!=null && $('#belongCity').val()!=''){
			return '福建省'+$('#belongCity option:selected').text()+"清水蓝天大练兵统计调度统计情况";
		} else {
			return '福建省清水蓝天大练兵统计调度统计情况';
		} 
	}
</script>
</body>
</html>