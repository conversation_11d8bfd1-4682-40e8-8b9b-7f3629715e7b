<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta charset="utf-8">
    <title>福建省环境监察执法平台</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<script src="${webpath }/static/js/tableExport.js"  type="text/javascript"></script>
	<style type="text/css">
		.fixed-table-container thead th .th-inner {
			white-space : normal;
		}
	</style>
</head>
	<script type="text/javascript">
		$(function(){
			//监听回退键
			business.listenBackSpace();
		});
	</script>
<body>
<div class="main-container">
	<div class="padding-md">

        <div class="row">
            <div class="col-lg-12 text-right form-inline no-margin">
            	<input type="hidden" id="belongCode" name="belongCode" value="${belongCode }">
				<div style="float:left; font-size:14px; color:red; padding:10px 0 0 0;" id="friendMess">当前统计数据起始自XX年XX月XX日0:00，截止到XX年XX月XX日24:00。</div>
				<div class="form-group">
					<select class="form-control" id="selectDate" name="selectDate">
						<option value="thisMonth">本月</option>
						<option value="lastWeek">最近7天</option>
						<option value="lastTwoWeek">最近2周</option>
						<option value="lastMonth">最近30天</option>
						<option value="beforeMonth">上个月</option>
						<option value="lastThreeMonth">最近3个月</option>
						<option value="lastYear">最近12个月</option>
						<option value="yesterday">昨天</option>
						<option value="customDate">自定义时间</option>
					</select>
				</div>
				<div class="form-group">
					<div class="input-group">
						<input type="hidden" readonly="readonly" value="" id="beginDate" name="beginDate"
						placeholder="起始时间" class="datepicker-input form-control" data-parsley-required="true">
					</div>
					<div class="input-group">
						<input type="hidden" readonly="readonly" value="" id="endDate" name="endDate"
						placeholder="结束时间" class="datepicker-input form-control" data-parsley-required="true">
					</div>
				</div>
				<div class="form-group">
					<div class="padding-sm" style="float: left;">
						<button type="button" class="btn btn-info btn-block" onclick="selectCount()">查询统计</button>
					</div>
				</div>
                <div class="form-group">
                	<button onclick ="exportTable()" type="button" class="btn btn-info no-shadow">导出EXCEL</button>
                </div>
                <div class="form-group">
                	<button onclick ="goBack()" type="button" class="btn btn-info no-shadow">返回</button>
                </div>
            </div>
        </div>
                 <div class="row">
                     <div class="col-lg-12">
                         <div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
						<i class="fa fa-commenting"></i> 执法情况
					</div>
					<div class="smart-widget-inner">
						<div class="smart-widget-body no-padding">
							<div class="padding-md table-responsive">
								<table id="zfqkDetailTable" class ="table-no-bordered">
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

	$(function(){
		//自定义时间段
		$("#beginDate").datetimepicker({
			language:'cn',
		    format:'yyyy-mm-dd',
		    todayBtn: true,
		    autoclose: true,
		    minView:'year',
		    maxView:'decade',
		    endDate:new Date()
		 });
		$("#beginDate").change(function(){
			var beginDate = $("#beginDate").val();
			//var now = new Date();
			$("#endDate").datetimepicker('remove');
			$("#endDate").val(business.getCurrentDate());
			$("#endDate").datetimepicker({
				language:'cn',
				format:'yyyy-mm-dd',
				todayBtn: true,
				autoclose: true,
				minView:'year',
				maxView:'decade',
				startDate:new Date(beginDate),
				endDate:new Date()
			})
		 });

		$("#selectDate").change(function(){
			var selectDate = $("#selectDate").val();
			if(selectDate == 'customDate'){
				$("#beginDate").attr("type","text");
				$("#endDate").attr("type","text");
			} else {
				$("#beginDate").val("");
				$("#endDate").val("");
				$("#beginDate").attr("type","hidden");
				$("#endDate").attr("type","hidden");
			}
		});

		getStaDate();
		loadingDetailTable();
	});

	function selectCount(){
		if($("#selectDate").val()=='customDate' &&
				($("#beginDate").val()==null||$("#beginDate").val()==''
					||$("#endDate").val()==null||$("#endDate").val()=='')){
			swal("提示","自定义时间段不能为空，请重新选择。","info");
			return false;
		}
		getStaDate();
		$("#zfqkDetailTable").bootstrapTable('refresh');
	};

	function loadingDetailTable(){
		$("#zfqkDetailTable").bootstrapTable({
			method: 'post',
			dataType: "json",
			url:  WEBPATH+'/jckh/getPersonData',
			undefinedText : '-',
			pagination : false, // 分页
			striped : true, // 是否显示行间隔色
			cache : false, // 是否使用缓存
			//async:false,
			//queryParamsType: "",
			singleSelect: false,
			contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			sidePagination: "server", //服务端请求
			//showFooter:true,//显示列脚
			queryParams:function (params) {
	            var temp = {
		        	belongCode:$("#belongCode").val(),
		        	beginDate:$("#beginDate").val(),
		        	endDate:$("#endDate").val(),
		        	selectDate:$("#selectDate").val()
	            };
	            return temp;
	     	},//参数
	     	uniqueId : "userId", // 每一行的唯一标识
			columns: [
				{
					field : "Number",
					title : "序号",
					align : 'center',
					formatter : function(value, row,index) {
						return index + 1;
					}
				},{
					field: "loginName",
					title: "姓名",
					align: "center"
				},{
					field: "cardId",
					title: "身份证号",
					align: 'center'
				},{
				    field: "belongDepartmentName",
				    title: "所属部门",
				    align: 'center'
				},{
				    field: "phone",
				    title: "手机号码",
				    align: 'center'
				},{
				    field: "jobName",
				    title: "职务",
				    align: 'center'
				},{
				    field: "lawTimes",
				    title: "执法任务办结数",
				    align: 'center'
				},{
				    field: "lawDays",
				    title: "执法办理天数",
				    align: 'center'
				}
			],
			responseHandler : function(res) {
				return {
				    //total : res.size,
				    rows : res
            	};
			},
			onCheck: function(row, $element) {
			},//单击row事件
			onUncheck: function(row, $element) {
			},
			formatLoadingMessage: function () {
				return "数据加载中...";
			},
			formatNoMatches: function () { //没有匹配的结果
				return '无符合条件的记录';
			},
			onLoadSuccess:function(data){
			}
		});

	}

	function getStaDate(){
		var tmp = {beginDate:$("#beginDate").val(),
	        	endDate:$("#endDate").val(),
	        	selectDate:$("#selectDate").val()};
		$.ajax({
			type:'post',
			url:WEBPATH+"/jckh/getStaDate",
            dataType:"json",
            data:tmp,
            async:false,
            success:function(data){
				if(data.meta.code == '200'){
					$("#friendMess").html('当前统计数据起始自'+data.data.beginDate+' 00:00，截止到'+data.data.endDate+' 24:00。')
				}
            }
		});
	}

	function goBack() {
		var preUrl = '${preUrl}';
		if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
			business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", null);
		} else {
			swal({
				title : "提示！",
				text : "返回信息错误，请刷新后重试。",
				type : "error"
			})
		}
	}

	//导出页面表单
	function exportTable(){
		$('#zfqkDetailTable').tableExport({
			type:'excel',
			escape:'false',
			fileName:fileName()
		});
	}

	function fileName(){
		var fileName = 'tableExport';
		$.ajax({
			type:'post',
			url:WEBPATH+"/jckh/getAreaNameByCode",
            dataType:"json",
            data:{belongCode:$("#belongCode").val()},
            async:false,
            success:function(data){
				if(data.meta.code == '200'){
					fileName = data.data+'执法调度统计详细情况';
				}
            }
		});
		return fileName;
	}
</script>
</body>
</html>
