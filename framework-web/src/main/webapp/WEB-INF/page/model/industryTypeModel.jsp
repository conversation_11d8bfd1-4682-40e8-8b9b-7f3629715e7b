<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
	<div class="modal-header">
					<div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info"
						onclick="industryconfirm()" data-dismiss="modal">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
					<h4 class="modal-title" id="myModalLabel">行业类型</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body form-horizontal">
				<form  id= "searchForm" role="form"  >
						<div class="form-group">
							<label for="行业名称" class="col-lg-2 col-sm-2 col-xs-5 control-label">行业名称（说明）</label>
							<div class="col-lg-8 col-sm-8 col-xs-12">
								<input type="text" class="form-control" value="" name='name'
									id='name' placeholder="行业名称（说明）">
							</div>
						</div>
						<div class="form-group">
								<label for="小类代码" class="col-lg-2 control-label">小类代码</label>
								<div class="col-lg-8">
									<input type="text" class="form-control" value="" name='smallCode'
										id='smallCode' placeholder="请输入小类代码">
								</div>
							</div>
							
						<div class="form-group">
							<label for="门类" class="col-lg-2 col-sm-2 col-xs-5 control-label">门类</label>
							<div class="col-lg-8 col-sm-8 col-xs-12">
								<select class="form-control" name="firstCalss" id="firstCalss">
									<option value="">请选择</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label for="大类" class="col-lg-2 col-sm-2 col-xs-5 control-label">大类</label>
							<div class="col-lg-8 col-sm-8 col-xs-12">
								<select class="form-control" name="secondClass" id="secondClass">
									<option value="">请选择</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label for="中类" class="col-lg-2 col-sm-2 col-xs-5 control-label">中类</label>
							<div class="col-lg-8 col-sm-8 col-xs-12">
								<select class="form-control" name="thirdClass" id="thirdClass">
									<option value="">请选择</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label for="小类" class="col-lg-2 col-sm-2 col-xs-5 control-label">小类</label>
							<div class="col-lg-8 col-sm-8 col-xs-12">
								<select class="form-control" name="fourthClass" id="fourthClass">
									<option value="">请选择</option>
								</select>
							</div>
						</div>
						<div class="form-group">.
						<label  class="col-lg-9 col-sm-9 col-xs-9 control-label"></label>
							<div class="col-lg-2 col-sm-2 col-xs-2">
								<button type="button" class="btn btn-info" id="checkIndustryBtn"
								>查 询</button>
							</div>
						</div>
			</form>
					</div>
					<table class="table table-striped table-bordered no-margin">
						<thead>
							<tr>
								<th class="text-center" style="width: 5%;">门类</th>
								<th class="text-center" style="width: 6%;">大类</th>
								<th class="text-center" style="width: 6%;">中类</th>
								<th class="text-center" style="width: 6%;">小类</th>
								<th class="text-center" style="width: 30%;">类别名称</th>
								<th class="text-center" style="width: 40%;">说明</th>
								<th class="text-center" style="width: 7%;">操作</th>
							</tr>
						</thead>
						<tbody id="CheckUserChooseTr">
						</tbody>
							<tr><td colspan="7"><span id ="reminder"></span></td></tr>
					</table>
				</div>
		<!--第三层翻页-->
                <div id ="pageTotal" style="float: left;color:#0099cc; margin:10px 0 0 10px;"></div>
				<div style="margin: 0; float: right; ">
					<div style="float: left; margin:-15px 10px 0 0;">
						<div class="page" id ="pageDiv">
							<ul class="pagination" id="pageCon">
							</ul>
                        </div>
					</div>
                    <div style="float: right; width:100px;padding:5px;">
                        <select class="form-control" id="pageSizeId">
                            <option value="5">5条</option>
                            <option value="10" selected>10条</option>
                            <option value="15" >15条</option>
                            <option value="20" >20条</option>
                            <option value="30" >30条</option>
                        </select>
                    </div>
				</div>
				<!--./第三层翻页-->
				<!--./确定 关闭按钮-->
				<div class="modal-footer" style="margin-top: 40px;">
					
				</div>
	<!-- ./检查人选择（Modal） -->
	<script type="text/javascript">
		$("#secondClass").change(function() {
			var secondClass = $("#secondClass").val();
			$("#fourthClass option").remove();
			$("#fourthClass").html("<option value=''>请选择 </option>");
			$("#thirdClass").html("<option value=''>请选择 </option>");
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/getIindustryTypeById',
				data : {
					id : secondClass,
					industryLevel : 3
				},
				success : function(msg) {
					$("#thirdClass").html(
							"<option value=''>请选择 </option>");
					if (msg.length != 0) {
						$.each(msg, function(i, item) {
							$("#thirdClass").append("<option value="+item.thirdClass+">"+ item.name + "</option>");
						});
					}
				}
			});
		});
			//  小类加载
			$("#thirdClass").change(
				function() {
					var thirdClass = $("#thirdClass").val();
					$("#fourthClass option").remove();
					$.ajax({
						type : "POST",
						url : WEBPATH + '/zfdx/getIindustryTypeById',
						data : {
							id : thirdClass,
							industryLevel : 4
						},
						success : function(msg) {
							$("#fourthClass").html(
									"<option value=''>请选择 </option>");
							if (msg.length != 0) {
								$.each(msg, function(i, item) {
									$("#fourthClass").append("<option value="+item.fourthClass+"  >"+ item.name + "</option>");
								});
							}
						}
					});
				});
			
			//da 类加载
			$("#firstCalss").change(
				function() {
					var firstCalss = $("#firstCalss").val();
					$("#thirdClass option").remove();
					$("#fourthClass option").remove();
					$("#fourthClass").html("<option value=''>请选择 </option>");
					$("#secondClass").html("<option value=''>请选择 </option>");
					$("#thirdClass").html("<option value=''>请选择 </option>");
					$.ajax({
						type : "POST",
						url : WEBPATH + '/zfdx/getIindustryType',
						data : {
							firstCalss : firstCalss,
							industryLevel : 2
						},
						success : function(msg) {
							// $("#firstCalss").html(html);
							if (msg.length != 0) {
								$.each(msg, function(i, item) {
									$("#secondClass").append("<option value="+item.secondClass+"  >"+ item.name + "</option>");
								});
							}
						}
					});
				});
			var industryTypeCode = $("#industryTypeCode").val();
			$('#pageSizeId').change(function() {
				$("#pageDiv").html("<ul class='pagination' id='pageCon'> </ul>")
				var pageSizeId = $("#pageSizeId").val();
				$.ajax({
					type : "POST",
					url : WEBPATH+ '/zfdx/industryTypecheckList?pageSize='+ pageSizeId,
					data : $('#searchForm').serialize(),
					success : function(msg) {
						$("#CheckUserChooseTr").children().remove();
						$("#reminder").html("");
						if (msg.total != 0) {
							var temp = msg.list;
							for (var i = 0; i < temp.length; i++) {
								if (temp[i].fourthClass != "" && temp[i].fourthClass != null) {
									if(industryTypeCode !=null || industryTypeCode !=''){
										if(temp[i].fourthClass == industryTypeCode){
											$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
													+ temp[i].firstCalss
													+ "</td><td class='text-center'>"
													+ temp[i].secondClass
													+ "</td><td class='text-center'>"
													+ temp[i].thirdClass
													+ "</td><td class='text-center'>"
													+ temp[i].fourthClass
													+ "</td><td class='text-center'>"
													+ temp[i].name
													+ "</td><td class='text-center'>"
													+ temp[i].industryDesc
													+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
										}else{
											$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
													+ temp[i].firstCalss
													+ "</td><td class='text-center'>"
													+ temp[i].secondClass
													+ "</td><td class='text-center'>"
													+ temp[i].thirdClass
													+ "</td><td class='text-center'>"
													+ temp[i].fourthClass
													+ "</td><td class='text-center'>"
													+ temp[i].name
													+ "</td><td class='text-center'>"
													+ temp[i].industryDesc
													+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
		
										}
									}
								} else {
									$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'> </td></tr>")
			
								}
							}	var curentPage = msg.pageNum;
							var totalPage = msg.pages;
							if (totalPage > 0) {
								var optionsPage = {
									bootstrapMajorVersion : 3,
									currentPage : curentPage,
									totalPages : totalPage,
									numberOfPages : 5,
									itemTexts : function(type, page,current) {
										switch (type) {
										case "first":
											return "首页";
										case "prev":
											return "&laquo;";
										case "next":
											return "&raquo;";
										case "last":
											return "尾页";
										case "page":
											return page;
										}
									},
									onPageClicked : function(event,originalEvent,type, page) {
										var pageSizeId = $("#pageSizeId").val();
										 $.ajax({
											type : "POST",
											url : WEBPATH
													+ '/zfdx/industryTypecheckList?pageNum='
													+ page
													+ '&pageSize='
													+ pageSizeId,
											data : $('#searchForm').serialize(),
											success : function(msg) {
												$("#reminder").html("");
												$("#CheckUserChooseTr").children().remove();
												if (msg.total != 0) {
													var temp = msg.list;
													for (var i = 0; i < temp.length; i++) {
														if (temp[i].fourthClass != "" && temp[i].fourthClass != null) {
															if(industryTypeCode !=null || industryTypeCode !=''){
																if(temp[i].fourthClass == industryTypeCode){
																	$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																			+ temp[i].firstCalss
																			+ "</td><td class='text-center'>"
																			+ temp[i].secondClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].thirdClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].fourthClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].name
																			+ "</td><td class='text-center'>"
																			+ temp[i].industryDesc
																			+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
																}else{
																	$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																			+ temp[i].firstCalss
																			+ "</td><td class='text-center'>"
																			+ temp[i].secondClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].thirdClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].fourthClass
																			+ "</td><td class='text-center'>"
																			+ temp[i].name
																			+ "</td><td class='text-center'>"
																			+ temp[i].industryDesc
																			+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
								
																}
															}
														} else {
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																							+ temp[i].firstCalss
																							+ "</td><td class='text-center'>"
																							+ temp[i].secondClass
																							+ "</td><td class='text-center'>"
																							+ temp[i].thirdClass
																							+ "</td><td class='text-center'>"
																							+ temp[i].fourthClass
																							+ "</td><td class='text-center'>"
																							+ temp[i].name
																							+ "</td><td class='text-center'>"
																							+ temp[i].industryDesc
																							+ "</td><td class='text-center'> </td></tr>")
			
														}
														
													}
												} else {
													$("#reminder").html("无符合条件的记录")
												}
												curentPage = msg.pageNum;
												totalPage = msg.pages;
											}
										}); 
									}
								}
								$('#pageCon').bootstrapPaginator(optionsPage);
								$("#pageTotal").html("共"+msg.total+"条记录");
							}
						} else {
							$("#pageTotal").html("共"+msg.total+"条记录");
							$("#reminder").html("无符合条件的记录");
							 $('#pageCon').remove();
						}
					}
				});
			}); 
			//行业类型选择
			$(document).ready(function() {
			//监听enter
			/* $("#IndustryTypeChoose").click(function(){ */
			//查询门类信息
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/getIindustryType',
				data : "industryLevel=1",
				success : function(msg) {
					if (msg.length != 0) {
						$.each(msg, function(i, item) {
							$("#firstCalss").append("<option value="+item.firstCalss+"  >" + item.name + "</option>");
						});
					}
				}
			});
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/industryTypecheckList',
				data : $('#searchForm').serialize(),
				success : function(msg) {
					$("#reminder").html("");
					$("#CheckUserChooseTr").children().remove();
					if (msg.total != 0) {
						var temp = msg.list;
						for (var i = 0; i < temp.length; i++) {
							if (temp[i].fourthClass != "" && temp[i].fourthClass != null) {
								if(industryTypeCode !=null || industryTypeCode !=''){
									if(temp[i].fourthClass == industryTypeCode){
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
									}else{
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
	
									}
								}
							} else {
								$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																+ temp[i].firstCalss
																+ "</td><td class='text-center'>"
																+ temp[i].secondClass
																+ "</td><td class='text-center'>"
																+ temp[i].thirdClass
																+ "</td><td class='text-center'>"
																+ temp[i].fourthClass
																+ "</td><td class='text-center'>"
																+ temp[i].name
																+ "</td><td class='text-center'>"
																+ temp[i].industryDesc
																+ "</td><td class='text-center'> </td></tr>")
			
							}
						}
						var curentPage = msg.pageNum;
						var totalPage = msg.pages;
						if (totalPage > 0) {
							var optionsPage = {
								bootstrapMajorVersion : 3,
								currentPage : curentPage,
								totalPages : totalPage,
								numberOfPages : 5,
								itemTexts : function(type, page,current) {
									switch (type) {
									case "first":
										return "首页";
									case "prev":
										return "&laquo;";
									case "next":
										return "&raquo;";
									case "last":
										return "尾页";
									case "page":
										return page;
									}
								},
								onPageClicked : function(event,originalEvent,type, page) {
									var pageSizeId = $("#pageSizeId").val();
									 $.ajax({
										type : "POST",
										url : WEBPATH
												+ '/zfdx/industryTypecheckList?pageNum='
												+ page
												+ '&pageSize='
												+ pageSizeId,
										data : $('#searchForm').serialize(),
										success : function(msg) {
											$("#reminder").html("");
											$("#CheckUserChooseTr").children().remove();
											if (msg.total != 0) {
												var temp = msg.list;
												for (var i = 0; i < temp.length; i++) {
													if (temp[i].fourthClass != "" && temp[i].fourthClass != null) {
														if(industryTypeCode !=null || industryTypeCode !=''){
															if(temp[i].fourthClass == industryTypeCode){
																$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																		+ temp[i].firstCalss
																		+ "</td><td class='text-center'>"
																		+ temp[i].secondClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].thirdClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].fourthClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].name
																		+ "</td><td class='text-center'>"
																		+ temp[i].industryDesc
																		+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
															}else{
																$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																		+ temp[i].firstCalss
																		+ "</td><td class='text-center'>"
																		+ temp[i].secondClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].thirdClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].fourthClass
																		+ "</td><td class='text-center'>"
																		+ temp[i].name
																		+ "</td><td class='text-center'>"
																		+ temp[i].industryDesc
																		+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
							
														}
													}
													} else {
														$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																						+ temp[i].firstCalss
																						+ "</td><td class='text-center'>"
																						+ temp[i].secondClass
																						+ "</td><td class='text-center'>"
																						+ temp[i].thirdClass
																						+ "</td><td class='text-center'>"
																						+ temp[i].fourthClass
																						+ "</td><td class='text-center'>"
																						+ temp[i].name
																						+ "</td><td class='text-center'>"
																						+ temp[i].industryDesc
																						+ "</td><td class='text-center'> </td></tr>")
			
													}
													
												}
											} else {
												$("#reminder").html("无符合条件的记录")
											}
											curentPage = msg.pageNum;
											totalPage = msg.pages;
										}
									}); 
								}
							}
							$('#pageCon').bootstrapPaginator(optionsPage);
							$("#pageTotal").html("共"+msg.total+"条记录");
						}
					} else {
						$("#pageTotal").html("共"+msg.total+"条记录");
						$("#reminder").html("无符合条件的记录");
						 $('#pageCon').remove();
					}
			
				}
			});
			})
			//行业类型查询
			$("#checkIndustryBtn").click(function() {
			var pageSizeId = $("#pageSizeId").val();
			$("#pageDiv").html("<ul class='pagination' id='pageCon'> </ul>")
			$.ajax({
				type : "POST",
				url : WEBPATH + '/zfdx/industryTypecheckList?pageSize='+ pageSizeId,
				data : $('#searchForm').serialize(),
				success : function(msg) {
					 $("#CheckUserChooseTr").children().remove();
					 $("#reminder").html("");
					if (msg.total != 0) {
						var temp = msg.list;
						for (var i = 0; i < temp.length; i++) {
							if (temp[i].fourthClass != ""&& temp[i].fourthClass != null) {
								if(industryTypeCode !=null || industryTypeCode !=''){
									if(temp[i].fourthClass == industryTypeCode){
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
									}else{
										$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
												+ temp[i].firstCalss
												+ "</td><td class='text-center'>"
												+ temp[i].secondClass
												+ "</td><td class='text-center'>"
												+ temp[i].thirdClass
												+ "</td><td class='text-center'>"
												+ temp[i].fourthClass
												+ "</td><td class='text-center'>"
												+ temp[i].name
												+ "</td><td class='text-center'>"
												+ temp[i].industryDesc
												+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
	
									}
								}
							} else {
								$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																+ temp[i].firstCalss
																+ "</td><td class='text-center'>"
																+ temp[i].secondClass
																+ "</td><td class='text-center'>"
																+ temp[i].thirdClass
																+ "</td><td class='text-center'>"
																+ temp[i].fourthClass
																+ "</td><td class='text-center'>"
																+ temp[i].name
																+ "</td><td class='text-center'>"
																+ temp[i].industryDesc
																+ "</td><td class='text-center'> </td></tr>")
			
							}
						}
						$("#pageTotal").html("共"+msg.total+"条记录");
					} else {
						$("#pageTotal").html("共"+msg.total+"条记录");
						$("#reminder").html("无符合条件的记录")
						$('#pageCon').remove();
					} 
					var curentPage = msg.pageNum;
					var totalPage = msg.pages;
					if (totalPage > 0) {
						var optionsPage = {
							bootstrapMajorVersion : 3,
							currentPage : curentPage,
							totalPages : totalPage,
							numberOfPages : 5,
							itemTexts : function(type, page,current) {
								switch (type) {
								case "first":
									return "首页";
								case "prev":
									return "&laquo;";
								case "next":
									return "&raquo;";
								case "last":
									return "尾页";
								case "page":
									return page;
								}
							},
							onPageClicked : function(event,originalEvent,type, page) {
								var pageSizeId = $("#pageSizeId").val();
								//alert('ssss')
								//business.addMainContentParserHtml(WEBPATH+'/zfdx/industryTypecheckList?pageNum='+page+'&pageSize='+pageSizeId,$("#searchForm").serialize());
								 $.ajax({
									type : "POST",
									url : WEBPATH+ '/zfdx/industryTypecheckList?pageNum='+ page+ '&pageSize='+ pageSizeId,
									data : $('#searchForm').serialize(),
									success : function(msg) {
										$("#reminder").html("");
										$("#CheckUserChooseTr").children().remove();
										if (msg.total != 0) {
											var temp = msg.list;
											for (var i = 0; i < temp.length; i++) {
												if (temp[i].fourthClass != "" && temp[i].fourthClass != null) {
													if(industryTypeCode !=null || industryTypeCode !=''){
														if(temp[i].fourthClass == industryTypeCode){
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'><input type='radio' checked='checked'   name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
														}else{
															$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																	+ temp[i].firstCalss
																	+ "</td><td class='text-center'>"
																	+ temp[i].secondClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].thirdClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].fourthClass
																	+ "</td><td class='text-center'>"
																	+ temp[i].name
																	+ "</td><td class='text-center'>"
																	+ temp[i].industryDesc
																	+ "</td><td class='text-center'><input type='radio' name='chosseIndustry'  value='"+temp[i].fourthClass+"#"+temp[i].name+"' > </td></tr>")
						
														}
													}
												} else {
													$("#CheckUserChooseTr").append("<tr><td class='text-center'>"
																					+ temp[i].firstCalss
																					+ "</td><td class='text-center'>"
																					+ temp[i].secondClass
																					+ "</td><td class='text-center'>"
																					+ temp[i].thirdClass
																					+ "</td><td class='text-center'>"
																					+ temp[i].fourthClass
																					+ "</td><td class='text-center'>"
																					+ temp[i].name
																					+ "</td><td class='text-center'>"
																					+ temp[i].industryDesc
																					+ "</td><td class='text-center'> </td></tr>")
												}
											}
										} else {
											$("#reminder").html("无符合条件的记录")
											 $('#pageCon').remove();
										}
										curentPage = msg.pageNum;
										totalPage = msg.pages;
									}
								}); 
							}
						}
					}
						$('#pageCon').bootstrapPaginator(optionsPage);
				}
			});
			})				
				function industryconfirm(){
					var temp = $("input[type='radio']:checked").val();
					if(temp != null && temp != "" && temp != undefined){
						var arr = temp.split("#");
						$("#industryTypeName").val(arr[1]);
						$("#industryTypeCode").val(arr[0]);
						$('#toChangeFormId').formValidation('revalidateField', 'industryTypeName'); 
					}
			}

	$(document).ready(function(){
		//监听enter查询，内包含监听回退键
	    business.listenEnter("checkIndustryBtn");
	});
</script>
</body>

</html>