<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<% String fastdfs_addr = PropertiesHandlerUtil.getValue(	"fastdfs.nginx.ip", "fastdfs");%>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<script type="text/javascript">
     var FASTDFS_ADDR = '${FASTDFS_ADDR}';
</script>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="fileModalLabel">附件预览</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-body">
		<c:if test="${fileInfoModel.fileType==0}">
			<div class="pricing-value">
				<div id="B${fileInfoModel.id}"></div>
			</div>
		</c:if>
		<c:if test="${fileInfoModel.fileType==1}">
			<div class="pricing-value">
				<span class="value"><img style="height:100%;" src="${FASTDFS_ADDR}/${fileInfoModel.fileUrl}" /></span>
			</div>
				</c:if>
	</div>
</div>
<div class="modal-footer"> 
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
</div>
<script type="text/javascript">
$(document).ready(function(){
		$('#fileModel').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		var obj=$.parseJSON('${fileInfo}');
		if(obj != null) {
			var select="#B"+obj.id;
			if(isIEWhether){
				if(PDFObject.supportsPDFs){
					PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select);
				} else {
					 swal({ 
					        title: "提示",  
					        text: "您的浏览器不支持pdf预览功能，请安装pdf阅读器后重试！",  
					        type: "warning", 
					        showCancelButton: true, 
					        closeOnConfirm: true, 
					        confirmButtonText: "下载并安装", 
					        confirmButtonColor: "#ec6c62" 
					    }, function() { 
					        window.location.href=WEBPATH+"/sysUser/downloadPdfReader"
					    }); 
				}
			}else{
				var options = {
						/* pdfOpenParams: {
							navpanes: 0,
							toolbar: 0,
							statusbar: 0,
							page: 1
						}, */
						forcePDFJS: false,
						PDFJS_URL: WEBPATH+"/static/pdfjs/web/viewer.html"
				};
				
				PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select, options);
			}
		}
})

</script>