<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta charset="utf-8">
<title>福建环境监察全过程业务智能办理系统</title>
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>

<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<script type="text/javascript">
$(function(){
    //监听回退键
    business.listenBackSpace();    
   });
</script>
		<script>
			$(function()	{
				$('.chart').easyPieChart({
					easing: 'easeOutBounce',
					size: '140',
					lineWidth: '7',
					barColor: '#7266ba',
					onStep: function(from, to, percent) {
						$(this.el).find('.percent').text(Math.round(percent));
					}
				});

				$('.sortable-list').sortable();

				$('.todo-checkbox').click(function()	{
					
					var _activeCheckbox = $(this).find('input[type="checkbox"]');

					if(_activeCheckbox.is(':checked'))	{
						$(this).parent().addClass('selected');					
					}
					else	{
						$(this).parent().removeClass('selected');
					}
				
				});

				//Delete Widget Confirmation
				$('#deleteWidgetConfirm').popup({
					vertical: 'top',
					pagecontainer: '.container',
					transition: 'all 0.3s'
				});
			});
		</script>
        
        <script type="text/javascript">
	
		$('#ajaxPage').modal('zhifaduixiang').css({
			width: 'auto',
			'margin-left': function () {
				return -($(this).width() / 2);
			}
		});
		</script>

<body class="overflow-hidden">
	<div class="main-container">
				<div class="padding-md">
                
             	<!--第二层任务办理row-->
                    <div class="row">
                        <!--任务办理-->
                        <div class="col-lg-12">                                    
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-comment" style="font-size:16px;"></i> ${empty specialAction.id ? "创建" : "编辑" }专项行动
                                    <span class="smart-widget-option" style="margin-top:-3px;">
                                        <span class="refresh-icon-animated">
                                            <i class="fa fa-circle-o-notch fa-spin"></i>
                                        </span>
                                        <div id="callback">
                                        	<a href="#" onclick="goBack('${preUrl}')"><i class="fa fa-arrow-right"></i> 返回</a>
                                        	<!-- <a href="#" onclick="callbackbtn()"><i class="fa fa-arrow-right"></i> 返回</a> -->
                                        <!--<button class="btn btn-default btn-sm" id="callbackbtn" onclick="callbackbtn()">返回</button>-->
                                        </div>
                                    </span>
                                </div>
                                
                                
                              <div class="smart-widget-inner table-responsive">
                                  <div class="smart-widget-body form-horizontal">
                                  		<form id="addUpdateForm">
                                  
                                        <div id ="previewIds" style ="display: none;"></div>
                                  		<div class="form-group">
                                            <label for="专项行动名称" class="col-lg-2 control-label"><span style="color:red;">*</span>专项行动名称</label>
                                            <div class="col-lg-8">
                                                <input name="specialName"  value="${specialAction.specialName}" class="form-control" id="yyzzzjh" placeholder="专项行动名称">
                                            	<input  type="hidden" id="specialActionId" name="id" value="${specialAction.id}" class="form-control"/>
                                            </div>
										</div>
                                        <!-- <div class="form-group">
                                            <label for="行动级别" class="col-lg-2 control-label"><span style="color:red;">*</span> 行动级别</label>
											<div class="col-lg-8">
												
                                                <select class="form-control" name="specialLevel" id="selectID">
                                                </select>
                                            </div>
										</div> -->
                                        <div class="form-group">
                                            <label for="描述 不能超过500个字符" class="col-lg-2 control-label"> 描述</label>
                                            <div class="col-lg-8">
                                                <textarea name="specialDesc" class="form-control" placeholder="描述" rows="8" id="specialDesc">${specialAction.specialDesc}</textarea>
                                            </div>
										</div>
                                   <!-- 隐藏的已上传文件信息 开始-->
                                 <%--   <div>
									<input id="actionId" value="${specialAction.id}"
											name="actionId" type="hidden">
									</div>
									<div>
										<input id="sysFileUrl" value="${sysFileUrl}" name="sysFileUrl"
											type="hidden">
									</div>
									<div>
										<input id="sysFileType" value="${sysFileType}"
											name="sysFileType" type="hidden">
									</div>
									<input id="sysFileIds" value="${sysFileIds}" name="sysFileIds"
										type="hidden"> --%>
										
									<!--隐藏的已上传文件信息结束  -->
									<!-- 附件回显开始 -->
                                        <div class="form-group">
                                            <label for="" class="col-lg-2 control-label">${not empty specialAction.actionFileList ? "已上传附件" : "" } </label>
                                            <div class="col-lg-8 col-ms-8 col-sm-8">
                                            	<c:if test="${not empty specialAction.actionFileList}">
												<c:forEach items="${specialAction.actionFileList}" var="obj"
													varStatus="status">
													<c:if test="${status.count eq 1 || (status.count-1) % 6 eq 0}">
													</c:if>
													<c:if test="${obj.fileType==0}">
														<div class="col-lg-3 col-md-3 col-sm-3" id="${obj.id}">
															<div class="pricing-widget clean-pricing"
																style="cursor: pointer;">
																<div class="pricing-value">
																	<span class="value"><img style="height: 150px;"
																		data-toggle="modal"
																		data-remote="${webpath}/SpecialAction/showModal?id=${obj.id}"
																		data-target="#fileShowModel"
																		src="${webpath}/static/img/pdf-thumb.jpg" /></span>
																		
																</div>
																<ul class="text-center"
																	style="margin-top: -10px;">
																	<li>文件类型：其它</li>
																	
																	<li class="padding-sm">
																		<button type="button" class="btn btn-info btn-sm" onclick="downFile('${obj.fileName}','${obj.fileUrl }')" data-dismiss="modal">下载</button>
                                                                        <button type="button" class="btn btn-info btn-sm" onclick="deleteFile('${obj.id}')">删除</button>
																	</li>
																</ul>
															</div>
														</div>
													</c:if>
													<c:if test="${obj.fileType==1}">
														<div class="col-lg-3 col-md-3 col-sm-3" id="${obj.id}">
															<div class="pricing-widget clean-pricing"
																data-toggle="modal" data-target="#inputImgModeler"
																style="cursor: pointer;">
																<div class="pricing-value">
																	<span class="value"><img style="height: 150px;"
																		data-toggle="modal"
																		data-remote="${webpath}/SpecialAction/showModal?id=${obj.id}"
																		data-target="#fileShowModel"
																		src="${FASTDFS_ADDR}/${obj.fileUrl}" /></span>
																</div>
																<ul class="text-center"
																	style="margin-top: -10px;">

																	<li>文件类型：图片</li>
																	
																	<li class="padding-sm">
																		<button type="button" class="btn btn-info btn-sm"
																			onclick="downFile('${obj.fileName}','${obj.fileUrl }')" data-dismiss="modal">下载</button>
																		<button type="button" class="btn btn-info btn-sm"
																			onclick="deleteFile('${obj.id}')">删除</button>
																	</li>
																</ul>
															</div>
														</div>
													</c:if>
													<c:if test="${status.count % 6 eq 0 || status.count eq 6}">
                                                    </c:if>
										</c:forEach>
										</c:if>
                                            </div>
										</div>



									<!-- 附件回显结束 -->  
										
                                        <div class="form-group">
                                            <label for="任务要求附件" class="col-lg-2 control-label">任务要求附件</label>
                                            <div class="col-lg-8">
                                                <input id="file-es" name="file-es[]" type="file" multiple>
                                            
                                            </div>
										</div>
                                        </form> 
                                        <div class="form-group">
                                            <div class="col-lg-offset-2 col-lg-10">
                                            	<button type="button" class="btn btn-info" style="width:120px;" id="goZxxd" > 发 起 </button>
                                            </div>
                                       </div>
                                </div>
                            </div>
                        </div>
                        <!--./待办任务-->                                             
                                                       
					</div>
                    <!--./第二层任务办理row-->
				<div class="modal fade" id="fileShowModel" tabindex="-1"
					role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
					<div class="modal-dialog  modal-lg">
						<div class="modal-content"></div>
						<!-- /.modal-content -->
					</div>
					<!-- /.modal-dialog -->
				</div>

			</div>
			</div>
       
    <script type="text/javascript">
    $(function(){
    	$("[name='specialDesc']").focus(function(){
			business.listenTextAreaComeEnter();
		})
		$("[name='specialDesc']").blur(function(){
			business.listenTextAreaGoEnter();
		})
    })
    var arraySend=new Array();
    $("#file-es").fileinput({
		theme: "explorer",
		language : 'zh',
		uploadUrl : WEBPATH + '/actionFileManager/uploadActionFile', // you must set a valid URL here else you will get an error
		allowedFileExtensions : [ 'jpg', 'png', 'bmp','pdf'],
		maxFileSize : 1024*10,
		maxFilePreviewSize:1024*10,
		maxFileCount: 6,
		uploadAsync:true,
	  	initialCaption: "附件格式支持png，jpg，bmp，pdf，每个文件不大于10M，数量为6",
		slugCallback : function(filename) {
			return filename.replace('(', '_').replace(']', '_');
		}
	}).on('filebatchuploadsuccess', function(event, data, previewId, index) {
		
		$("#previewIds").val(previewIds);
		
		arraySend=arraySend.concat(data.response.data);
	}).on('fileuploaded', function(event, data, previewId, index) {
		$("#previewIds").val(previewIds);
		arraySend=arraySend.concat(data.response.data);
	}).on('filebatchuploadcomplete', function(event, files, extra) {
		var files = $('#input-id').fileinput('getFileStack'); //
		
	}).on('filesuccessremove', function(event, data, previewId, index) {
		//console.log(data);
		
		
		
	});
	
		$('#goZxxd').on('click',function() {
			
			var specialName = $("#yyzzzjh").val();
			//var specialLevel = $("#selectID").val();
			var specialDesc = $("#specialDesc").val();
			var sid = $("#specialActionId").val();
			var filees = $("#file-es").val();
		    var surl = encodeURI(WEBPATH+"/SpecialAction/insertOrUpdate?id="+sid+"&specialDesc="+specialDesc+"&specialName="+specialName);
			
			//校验参数
			 $("#addUpdateForm").data('formValidation').validate();
			var validate = $("#addUpdateForm").data('formValidation').isValid();
			//先判断校验非空项，再考虑附件的问题
			//新增专项行动时：判断是否已存在与当前输入行动级别和行动名称完全相同的行动
			//如果有，提示重新输入并清空名称与级别，如果没有，则继续。
			if(filees==''){
				if(sid==null || sid=='' || sid=='undefined'){
					//新增
					$.ajax({
						method:'POST',
						url:WEBPATH+'/SpecialAction/checkRepeat',
						data:{specialName:specialName,sid:sid},
					    success:function(data){
					    	if(data.valid){
					    		if(validate){
										$.ajax({
										        type:"post",
										        url:surl,
										        dataType:"json",
										        contentType:"application/json;charset=utf-8",
										        data:JSON.stringify(arraySend),
										        success:function(data){
										        	if(data.meta.result=="success"){
									        		   swal({
									                       title : "发起成功！",
									                       text : "专项行动发起成功",
									                       type : "success",
									                       allowOutsideClick :true
									                     });
									                     business.addMainContentParserHtml(WEBPATH +'/SpecialAction/toZxxd',null);
										        	}else{
										        		swal({title:"上传失败", text:"附件上传失败", type:"error",allowOutsideClick :true});
										        	}
										        }
										  	});
										
									
								} else{
									swal({
					                    title : "校验失败！",
					                    text : "请认真检查各项数据！",
					                    type : "error",
					                    allowOutsideClick :true
					                  })
								}  
					    	}else{
					    		swal({
					    			title:"提示",
					    			text:"已存在相同名称和级别的专项行动",
					    		    type:"info",
					    		    allowOutsideClick :true
					    		})
					    	}
					    }
					})
				}else{
					$.ajax({
						method:'POST',
						url:WEBPATH+'/SpecialAction/checkRepeat',
						data:{specialName:specialName,sid:sid},
					    success:function(data){
					    	if(data.valid){
					//更新
					if(validate){
						$.ajax({
					        type:"post",
					        url:surl,
					        dataType:"json",
					        contentType:"application/json;charset=utf-8",
					        data:JSON.stringify(arraySend),
					        success:function(data){
					        	if(data.meta.result=="success"){
				        		   swal({
				                       title : "修改成功！",
				                       text : "任务修改成功",
				                       type : "success",
				                       allowOutsideClick :true
				                     });
				                     business.addMainContentParserHtml(WEBPATH +'/SpecialAction/toZxxd',null);
					        	}else{
					        		swal({title:"上传失败", text:"附件上传失败", type:"error",allowOutsideClick :true});
					        	}
					        }
					  	});
					}
					    }else{
					    	swal({
				    			title:"提示",
				    			text:"已存在相同名称和级别的专项行动",
				    		    type:"info",
				    		    allowOutsideClick :true
				    		})
					    }
				}
					});
				}
			}else{
				swal({title:"提示", text:"请先上传附件", type:"info",allowOutsideClick :true});
			}
		});
			/* 
			if(id==null || id=='' || id=='undefined'){
				$.ajax({
					method:'POST',
					url:WEBPATH+'/SpecialAction/checkRepeat',
					data:{specialName:specialName,sid:sid},
				    success:function(data){
				    //加一个判断语句
				    
				      if(data.valid){
				    		if(validate){
								if(arraySend.length>0){
										$.ajax({
									        type:"post",
									        url:surl,
									        dataType:"json",
									        contentType:"application/json;charset=utf-8",
									        data:JSON.stringify(arraySend),
									        success:function(data){
									        	if(data.meta.result=="success"){
								        		   swal({
								                       title : "发起成功！",
								                       text : "任务发起成功",
								                       type : "success"
								                     });
								                     business.addMainContentParserHtml(WEBPATH +'/SpecialAction/toZxxd',null);
									        	}else{
									        		swal("上传失败", "附件上传失败", "error");
									        	}
									        }
									  	});
									
								}else{
									//没有新增图片
									swal({
									    title: "提示",
							  	        type: "info",
							  	        text:"请选择要上传的附件",
							  	        showCancelButton: true,
							  	        closeOnConfirm: false,
							  	        confirmButtonText: "没有新附件上传，继续发起！",
							  	        confirmButtonColor: "#4876FF"
										//swal("提示", "请选择要上传的附件", "info");
										//title:"提示",
										
									},function(){
										$.ajax({
									        type:"post",
									        url:surl,
									        dataType:"json",
									        contentType:"application/json;charset=utf-8",
									        data:JSON.stringify(arraySend),
									        success:function(data){
									        	if(data.meta.result=="success"){
								        		   swal({
								                       title : "发起成功！",
								                       text : "行动发起成功",
								                       type : "success"
								                     });
								                     business.addMainContentParserHtml(WEBPATH +'/SpecialAction/toZxxd',null);
									        	}else{
									        		swal("发起失败", "行动发起失败", "error");
									        	}
									        }
									  	});
									});
										
									
									
								}
							} else{
								swal({
				                    title : "校验失败！",
				                    text : "请认真检查各项数据！",
				                    type : "error"
				                  })
							}  
				    	}else{
				    		swal({
				    			title:"提示",
				    			text:"已存在相同名称和级别的专项行动",
				    		    type:"info"
				    		})
				    	}
				    }
				})
			}
}); */
			
	//		
	
</script>     

	<script>
	
	//下载文件
	
	function	downFile(filename,url){
	
		window.location.href="${pageContext.request.contextPath}/actionFileManager/downloadFile?url="+url+"&filename="+filename;
		//$.post(WEBPATH+'/actionFileManager/downloadFile',{url:url,filename:filename});
	}
	
		function editZxxd(){

			window.location.href="${pageContext.request.contextPath}/jcbl/zxxd-input";
			
		}
		$(function() {
			$('.chart').easyPieChart({
				easing : 'easeOutBounce',
				size : '140',
				lineWidth : '7',
				barColor : '#7266ba',
				onStep : function(from, to, percent) {
					$(this.el).find('.percent').text(Math.round(percent));
				}
			});

			$('.sortable-list').sortable();

			$('.todo-checkbox').click(function() {

				var _activeCheckbox = $(this).find('input[type="checkbox"]');

				if (_activeCheckbox.is(':checked')) {
					$(this).parent().addClass('selected');
				} else {
					$(this).parent().removeClass('selected');
				}

			});

			//Delete Widget Confirmation
			$('#deleteWidgetConfirm').popup({
				vertical : 'top',
				pagecontainer : '.container',
				transition : 'all 0.3s'
			});
		});
		
		
		//保存并发起任务  按钮功能
		$(document).ready(function(){
			var specialName = $("#yyzzzjh").val();
			var specialLevel = $("#selectID").val();
			
			//alert(repeatCode)
			//表单非空验证
	           $("#addUpdateForm").formValidation({
		        framework: 'bootstrap',
		        message: 'This value is not valid',
		        icon:{
			            valid: 'glyphicon glyphicon-ok',
			            invalid: 'glyphicon glyphicon-remove',
			            validating: 'glyphicon glyphicon-refresh'
	                   },
		        fields: {
		        	specialName: {
		                message: '专项行动名称不能为空',
		                validators: {
		                    notEmpty: {
		                        message: '专项行动名称不能为空'
		                    } 
		            	}
		            
		            },
		            specialDesc: {
		                message: '不能超过600个字符',
		                validators: {
		                    stringLength: {  
		                    min: 0,  
		                    max: 600,  
		                    message: '不能超过600个字'  
		                    }
		            	}
		            
		            }
		       }       
		    });  
		});
		
	</script>
	
	<script type="text/javascript">
	//删除指定的附件文件
	      function deleteFile(id){
	    	 /*  var specialId = $("#specialActionId").val();
	    	 
	    	  var url = "${pageContext.request.contextPath}/actionFileManager/deleteActionFile";
	          $.post(url,{id:id},function(data){
	        	  
	        	  business.addMainContentParserHtml(WEBPATH + '/SpecialAction/selectById',"specialActionID="+specialId);
	          }); */
	    	  swal({
		  	        title: "您确定执行删除操作吗？",
		  	        type: "warning",
		  	        showCancelButton: true,
		  	        closeOnConfirm: false,
		  	        confirmButtonText: "是的，我要删除",
		  	        confirmButtonColor: "#ec6c62"
		  	    	}, function() {
		  	            $.ajax({
		  	              type: "post",
		  	   		      url: WEBPATH+"/actionFileManager/deleteActionFile",
		  	   		      data:{id:id},
		  	            }).done(function(data) {
		  	            	 if(data.meta.result==="success"){
		  	 		        	swal({
		  	 		        		title : "删除成功", 
		  	                        text : "附件删除成功",
		  	                        type : "success"
		  	 		        	},function(){
		  	 		        		$("#"+id).remove();
		  	                    })
		  	 		        }else{
		  	 		          swal({title:"删除失败", text:"附件删除失败", type:"error",allowOutsideClick :true});
		  	 		        }
		  	            }).error(function(data) {
		  	                swal({title:"操作失败", text:"删除操作失败了!", type:"error",allowOutsideClick :true});
		  	            });
		  	    });
             
	      }
	
	
	    //返回上一步主菜单
	  	function goBack(preUrl) {
	  		if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
	  			business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", $("#searchForm").serialize());
	  		} else {
	  			swal({
	  				title : "提示！",
	  				text : "返回信息错误，请刷新后重试。",
	  				type : "error",
	  				allowOutsideClick :true
	  			})
	  		}
	  	}
	
	</script>
</body>
</html>
