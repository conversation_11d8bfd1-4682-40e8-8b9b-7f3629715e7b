<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>	
<link href="${webpath }/static/css/wnti-style.css" rel="stylesheet">
<div class="main-container">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
	<div class="padding-md">


		<%-- <jsp:include page="searchFilter.jsp"></jsp:include> --%>
		<!---快速查询--->
		<div class="kuaisu_search">
			<div class="panel-group" id="accordion">
				<div class="btn-group kuaisu_search_chuangjian">
					<button type="button" class="btn btn-info btn-mg" id="downloadExcel">导出EXCEL</button>
				</div>
				<div class="panel-heading">
					<div class="form-group" style="float: left;">
						<h5 class="panel-title">
							行政区划
							<!---省级--->
							<div style="width: 100px;" class="btn-group">
								<select class="form-control" id="belongProvince">
									<option value="350000">福建省</option>
								</select>
							</div>
							<!---市级--->
							<div style="width: 100px;" class="btn-group">
								<select class="form-control" id="belongCity">

								</select>
							</div>
							<!---县级--->
							<div style="width: 100px;" class="btn-group">
								<select class="form-control" id="belongCounty">

								</select>
							</div>
						</h5>
					</div>
					<!---搜索--->
					<div style="width: 255px; margin-left: 5px;" class="btn-group">
						<form class="bs-example bs-example-form" role="form">
							<div class="row">
								<div class="col-lg-6">
									<div class="input-group">
										<input type="text" class="form-control" style="width: 200px;"
											placeholder="请输入主题关键字" id="theme"> <span
											class="input-group-btn">
											<button class="btn btn-info" type="button"
												onclick="getList()">搜索</button>
										</span>
									</div>
									<!-- /input-group -->
								</div>
								<!-- /.col-lg-6 -->
							</div>
							<!-- /.row -->
						</form>
					</div>
					<a data-toggle="collapse" data-parent="#accordion"
						href="#collapseOne">
						<button type="button" class="btn btn-info">
							更多查询条件 <span class="caret"></span>
						</button>
					</a>
					<!---筛选--->
					<div class="btn-group">
						<button type="button" class="btn btn-info dropdown-toggle"
							data-toggle="dropdown">
							排序 <span class="caret"></span>
						</button>
						<ul class="dropdown-menu" role="menu">
							<c:if test="${pageCode=='10' || pageCode=='12'}"><li><a href="javascript:getList(1);">优先级正序</a></li>
							<li><a href="javascript:getList(2);">优先级倒序</a></li></c:if>
							<li><a href="javascript:getList(3);">创建时间正序</a></li>
							<li><a href="javascript:getList(4);">创建时间倒序</a></li>
						</ul>
					</div>
				</div>
				<div id="collapseOne" class="panel-collapse collapse">
					<div class="form-inline">
						<table class="table table-striped table-condensed">
							<tbody>
								<tr>
									<td>创建时间</td>
									<td>
										<div class="form-group">
											<input type="text" class="form-control input-sm"
												id="createTimeStart" name="TimeStart"
												data-parsley-required="true" placeholder="请输入开始时间">
										</div>~
										<div class="form-group">
											<input type="text" class="form-control input-sm"
												id="createTimeEnd" name="TimeEnd"
												data-parsley-required="true" placeholder="请输入结束时间">
										</div>
									</td>
									<td>问题分类</td>
									<td>
										<div class="form-group">
											<select class="form-control input-sm" id="questionType">
												<option value="">请选择</option>
												<option value="1">现有系统问题</option>
												<option value="2">新增需求或希望改善系统</option>
												<option value="3">希望获得操作指导</option>
											</select>
										</div>
									</td>
								</tr>
								<tr>
									<td>受理日期</td>
									<td>
										<div class="form-group">
											<input type="text" class="form-control input-sm"
												id="filterStartTime" placeholder="请输入开始时间"
												data-parsley-required="true" name="TimeStart">
										</div>~
										<div class="form-group">
											<input type="text" class="form-control input-sm"
												id="filterEndTime" placeholder="请输入结束时间"
												data-parsley-required="true" name="TimeEnd">
										</div>
									</td>
									<c:choose>
									     <c:when test="${pageCode=='11' }">
									          <td></td>
									          <td></td>
									     </c:when>
									     <c:otherwise>
											<td>优先级</td>
											<td>
												<div class="form-group">
													<select class="form-control input-sm" id="priority">
														<option value="">请选择</option>
														<!-- <option value="">中</option> -->
														<option value="1">待确定</option>
														<option value="2">低</option>
														<option value="3">高</option>
													</select>
												</div>
											</td>
										</c:otherwise>
									</c:choose>
									
								</tr>
								<tr>
									<td>结束日期</td>
									<td><div class="form-group">
											<input type="text" class="form-control input-sm"
												id="finishStartTime" placeholder="请输入开始时间"
												data-parsley-required="true" name="TimeStart">
										</div>~
										<div class="form-group">
											<input type="text" class="form-control input-sm"
												id="finishEndTime" placeholder="请输入结束时间"
												data-parsley-required="true" name="TimeEnd">
										</div></td>
                                    
									<c:if test="${pageCode=='10' }">
										<td>办理状态</td>
										<td>
											<div class="form-group">
												<select class="form-control input-sm" id="status">
													<option value="">请选择</option>
													<option value="1">待受理</option>
													<option value="2">解决中</option>
													<option value="3">已退回</option>
													<option value="4">已解决</option>
												</select>
											</div>
										</td>
									</c:if>
									<c:if test="${pageCode=='11' || pageCode=='12' }">
									    <td></td>
									    <td></td>
									</c:if>
								</tr>
								<tr>
									<td>&nbsp;</td>
                                    <td>&nbsp;</td> 
									<td>&nbsp;</td>
									<td><button type="button" class="btn btn-info"
											data-dismiss="modal" onclick="getList()">查 询</button></td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<!---快速查询结束--->
		<div class="row">
			<div class="col-lg-12">
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
						<i class="fa fa-comment" style="font-size: 16px;"></i><c:if test="${pageCode=='10' }">所有问题 </c:if><c:if test="${pageCode=='11' }">待受理问题 </c:if><c:if test="${pageCode=='12' }">待解决问题 </c:if> <span
							class="smart-widget-option"> <span
							class="refresh-icon-animated"> <i
								class="fa fa-circle-o-notch fa-spin"></i>
						</span> 

						</span>
					</div>
					<div class="smart-widget-inner table-responsive">
						<table width="100%" class="table table-striped table-hover"
							id="feedbackTable">
							   <thead>
                                            <tr>
                                                <th width="100">优先级</th>
                                                <th>问题信息</th>
                                                <th width="160">解决时间</th>
                                              <th width="160">工作量</th>
                                                <th width="160">状态及最新办理</th>
                                                <th width="80">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody v-for="(item, index) in items" :id ="item.id">
                                           
                                            <tr class="odd gradeX">
                                                <td colspan="6">{{item.creatDate}}<img src="${webpath }/static/img/interval.png" />{{item.quesNumber }}<img src="${webpath }/static/img/interval.png" />联系人姓名：{{item.contact }}<img src="${webpath }/static/img/interval.png" />联系人电话：{{item.contactPhone }}<img src="${webpath }/static/img/interval.png" />所属单位：{{item.belongDepartName }}</td>
                                            </tr>

								<tr class="even gradeA">
									<td v-if="item.levelCode==1">
										<p class="zhuangtai_yijieshu" :title="item.quesLevel"></p>
									</td>
									<td v-if="item.levelCode==2">
										<p class="zhuangtai_yishouli" :title="item.quesLevel"></p>
									</td>
									<td v-if="item.levelCode==3">
										<p class="zhuangtai_bushouli" :title="item.quesLevel"></p>
									</td>
									<td v-if="item.levelCode==0">
										<p>&nbsp;</p>
									</td>
								           <td class="even gradeC">
                                             
                                              <p>主题：<span class="list_zhuti">{{item.theme }}</span></p>
                                                <p>问题分类：<span class="list_wentifenlei">{{item.quesTypeName }}</span></p></td>
                                                <td><p>{{item.actualSolveDate}}</p></td>
                                                <td class="even gradeC" v-if="item.workload">{{item.workload}}人天</td>
                                                <td class="even gradeC" v-else>&nbsp;</td>
                                                <!-- 这个要做判断，根据当前状态显示提问者还是运维，要注意区分！！！ -->
                                               
                                                <td class="center"><p>{{item.state }}</p>
                                              <%--   <c:choose>
                                                    <c:when test="${item.stateCode=='1'}">
                                                         <p style="color:#090;">提问者：${item.creatUserName }</p>
                                                    </c:when>
                                                    <c:otherwise>
                                                         <p style="color:#090;">运维：${item.state }</p>
                                                    </c:otherwise>
                                                </c:choose> --%>
                                                  
                                                <p>{{item.stateDate}}</p></td>
                                                <td class="center">
                                                <c:if test="${pageCode=='10' }">
                                                     <p><a href="#"><button type="button" class="btn btn-info btn-xs" v-on:click="showDetails(item.id)">查看详情</button></a></p>
                                                </c:if>
                                                <c:if test="${pageCode=='11' }">
                                                      <p><a href="#"><button type="button" class="btn btn-info btn-xs" v-on:click="startHandle(item.id)">开始受理</button></a></p>
                                                </c:if>
                                                <c:if test="${pageCode=='12' }">
                                                       <p><button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#jjbl" v-bind:data-remote="WEBPATH+'/feedBack/toHandlePage?id='+item.id">解决办理</button></p>
                                                </c:if>
                                                <p><button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#lzgc" v-bind:data-remote="WEBPATH+'/feedBack/toCirculationInfo?id='+item.id">流转过程</button></p>
                                                <p><button type="button" class="btn btn-info btn-xs" v-if="item.stateCode==3 && item.isOpen==0" v-on:click="Open(item.id)">设为公开</button></p>
                                                <p><button type="button" class="btn btn-info btn-xs" v-if="item.stateCode==3 && item.isOpen==1" v-on:click="Close(item.id)">撤回公开</button></p>
                                                <p><button type="button" class="btn btn-info btn-xs" v-if="item.stateCode==4 && item.isOpen==0" v-on:click="Open(item.id)">设为公开</button></p>
                                                <p><button type="button" class="btn btn-info btn-xs" v-if="item.stateCode==4 && item.isOpen==1" v-on:click="Close(item.id)">撤回公开</button></p>
                                                </td>
                                            </tr> 
                                               
                                        </tbody>
							
						</table>
					</div>
				
				</div>
			</div>
		</div>
        <!--第三层翻页-->
      <%--   <c:if test="${ }"> --%>
		<div style="margin:30px 0 20px 0; float:right;">
                <div class="page" style="float:left;">
                    <ul class="pagination pagination-sm" id="pageCon"></ul>
                </div>
                <div style="float:right;margin:-25px 0 20px 0;">
                    <div class="form-group">						
                        <%-- <select class="form-control" id="pageSizeId">
                            <option value="5"
                            <c:if test="${empty pageBean.pageSize }">selected</c:if>
                                <c:if test="${pageBean.pageSize=='5' }">selected</c:if>>5条</option>
                            <option value="10"
                                
                                <c:if test="${pageBean.pageSize=='10' }">selected</c:if>>10条</option>
                            <option value="15"
                                <c:if test="${pageBean.pageSize=='15' }">selected</c:if>>15条</option>
                            <option value="30"
                                <c:if test="${pageBean.pageSize=='30' }">selected</c:if>>30条</option>
                            <option value="50"
                                <c:if test="${pageBean.pageSize=='50' }">selected</c:if>>50条</option>
                        </select> --%>
                    </div>
                </div>
        </div>
			<%-- </c:if> --%>
			<!--./第三层翻页-->

	</div>
</div>
<!--流转过程-->
<div class="modal fade" id="lzgc" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog" style="width: 50%; font-size: 14px;">
		<div class="modal-content"></div>
	</div>
</div>

<!-- vue模态框 -->
<!-- <template>
    <div class="modal fade" v-show="show" transition="fade">
        <div class="modal-dialog" style="width: 50%; font-size: 14px;">
            <div class="modal-content">
                头部
                <div class="modal-header">
                    <slot name="header">
                        <p class="title">{{modal.title}}</p>
                    </slot>
                    <a v-touch:tap="close(0)" class="close" href="javascript:void(0)"></a>
                </div>
                内容区域
                <div class="modal-body">
                    <slot name="body">
                        <p class="notice">{{modal.text}}</p>
                    </slot>
                </div>
                尾部,操作按钮
                <div class="modal-footer">
                    <slot name="button">
                        <a v-if="modal.showCancelButton" href="javascript:void(0)" class="button {{modal.cancelButtonClass}}" v-touch:tap="close(1)">{{modal.cancelButtonText}}</a>
                        <a v-if="modal.showConfirmButton" href="javascript:void(0)" class="button {{modal.confirmButtonClass}}" v-touch:tap="submit">{{modal.confirmButtonText}}</a>
                    </slot>
                </div>
            </div>
        </div>
    </div>
    <div v-show="show" class="modal-backup" transition="fade"></div>
</template> -->
<!-- 解决办理 -->
<div class="modal fade" id="jjbl" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog" style="width: 50%; font-size: 14px;">
		<div class="modal-content"></div>
	</div>
</div>


<script type="text/javascript">
	//跳转到详情页面
	/* function showDetails(id) {
		alert(id);
		alert("展示详情方法")
	} */
</script>
<script type="text/javascript">

var pageNum = null;
var totalPage = null;
var pageSize = $("#pageSizeId").val();
var dataArr = null;

var vue = new Vue({
	el: '#feedbackTable',
	data: {
		items:dataArr
	},
	methods: {
		showDetails:function(id){
			
			business.addMainContentParserHtml(WEBPATH + '/feedBack/questionInfo?id='+id, null);
		},
	    startHandle:function(id){
	    	business.addMainContentParserHtml(WEBPATH + '/feedBack/startHandle?id='+id, null);
	    }
	}
});
/* Vue.filter('time', function (value) {
	//shijianchuo是整数，否则要parseInt转换
	if(value!=null){
		var time = new Date(value);
		var y = time.getFullYear();
		var m = time.getMonth()+1;
		var d = time.getDate();
		var h = time.getHours();
		var mm = time.getMinutes();
		var s = time.getSeconds();
		return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
	}else{
		return "";
	}
    //return new Date(parseInt(value) * 1000).toLocaleString().replace(/年|月/g, "-").replace(/日/g, " ");
}); */
 function add0(m){return m<10?'0'+m:m }
function getList(i){
	    //alert("排序策略："+i);
	    var sortNumber = i;
	     //区划，主题关键字
		var belongAreaCode = "";
		var belongProvince = $("#belongProvince").val();
		var belongCity = $("#belongCity").val();
		var belongCounty = $("#belongCounty").val();

		if (belongCity == '' || belongCity == 'undefined' || belongCity == null) {
			//市级参数为空，以用户自身权限查询
			belongAreaCode = "35";

		} else if (belongCounty == '' || belongCounty == null
				|| belongCounty == 'undefined') {
			//县级参数为空
			belongAreaCode = belongCity.substr(0,4);
		} else {
			//县级参数不为空
			belongAreaCode = belongCounty.substr(0,6);
		}

		//alert(belongAreaCode);
		//时间控件数据
		var createTimeStart = $("#createTimeStart").val();
		var createTimeEnd = $("#createTimeEnd").val();
		var filterStartTime = $("#filterStartTime").val();
		var filterEndTime = $("#filterEndTime").val();
		var finishStartTime = $("#finishStartTime").val();
		var finishEndTime = $("#finishEndTime").val();

		var quesTypeCode = $("#questionType").val();
		var levelCode = $("#priority").val();
		var stateCode = $("#status").val();
		var theme = $("#theme").val();
		var menuId = '${pageCode}';
		var data = {
			menuId : menuId,	
			sortNumber : sortNumber,
			belongAreaCode : belongAreaCode,	
			createTimeStart : createTimeStart,
			createTimeEnd : createTimeEnd,
			filterStartTime : filterStartTime,
			filterEndTime : filterEndTime,
			finishStartTime : finishStartTime,
			finishEndTime : finishEndTime,
			quesTypeCode : quesTypeCode,
			levelCode : levelCode,
			theme : theme,
			stateCode : stateCode,
			pageNum : pageNum,
			pageSize : pageSize
		};
		$.ajax({
			//cache : true,
			type : "POST",
			url : WEBPATH + '/feedBack/FeedbackList',
			data : data,
			async : false,
			error : function(request) {
				swal("错误!", "获取提问列表失败！", "error");
			},
			success : function(data) {
				vue.items = data.data.list;
				pageNum = data.data.pageNum;
				totalPage = data.data.pages;
				//alert("totalPage is:"+totalPage)
				if (totalPage > 0) {
			var options = {
				bootstrapMajorVersion : 3,
				currentPage : pageNum,
				totalPages : totalPage,
				numberOfPages : 5,
				itemTexts : function(type, page, current) {
					switch (type) {
					case "first":
						return "首页";
					case "prev":
						return "&laquo;";
					case "next":
						return "&raquo;";
					case "last":
						return "尾页";
					case "page":
						return page;
					}
				},
				onPageClicked : function(event, originalEvent, type, page) {
					pageNum = page;
					getList();
				}
			};
			$('#pageCon').bootstrapPaginator(options);
		}
			}
		});

	}

	$(document).ready(function() {
		//返回按钮之后参数和页码的回显
		var params = $("#groundBackParams").val();
		//console.info(params);
		if(params != null && params != '' && params != 'undefined'){
			
			var jsonParam = $.parseJSON(params);
			
			for(var key in jsonParam){
				//绑定设定条件
				$("#"+key).val(jsonParam[key]);
				console.info("["+key+"]="+jsonParam[key]);
			}
			//$('#dataTable').bootstrapTable('refresh');
			//$('#dataTable').bootstrapTable('refresh',{pageNumber:parseInt(jsonParam['pageNum']),pageSize:parseInt(jsonParam['pageSize'])});
		}
		
		getList();
		//alert("totalPage is:"+totalPage)
		/* if (totalPage > 0) {
			var options = {
				bootstrapMajorVersion : 3,
				currentPage : pageNum,
				totalPages : totalPage,
				numberOfPages : 5,
				itemTexts : function(type, page, current) {
					switch (type) {
					case "first":
						return "首页";
					case "prev":
						return "&laquo;";
					case "next":
						return "&raquo;";
					case "last":
						return "尾页";
					case "page":
						return page;
					}
				},
				onPageClicked : function(event, originalEvent, type, page) {
					pageNum = page;
					getList();
				}
			};
			$('#pageCon').bootstrapPaginator(options);
		} */

	});
</script>
<script type="text/javascript">
	
			//====================
			//创建时间自定义时间段
			$("#createTimeStart").datetimepicker({
				language:'cn',
		        format:'yyyy-mm-dd hh:ii',
		        todayBtn: true,
		        clearBtn:true,
		        autoclose: true,
		        minView:0,
		        maxView:4,
		        endDate:new Date()
			 });
			$("#createTimeStart").change(function(){
				var beginDate = $("#createTimeStart").val();
				//var now = new Date();
				$("#createTimeEnd").datetimepicker('remove');
				$("#createTimeEnd").val(business.getCurrentDate()+" 23:59");
				$("#createTimeEnd").datetimepicker({
					language:'cn',
					format:'yyyy-mm-dd hh:ii',
					todayBtn: true,
			        clearBtn:true,
					autoclose: true,
					minView:0,
					maxView:4,
					startDate:new Date(beginDate),
					endDate:new Date()
				})
			 });
			
			//办理时间自定义时间段
			$("#filterStartTime").datetimepicker({
				language:'cn',
		        format:'yyyy-mm-dd hh:ii',
		        todayBtn: true,
		        clearBtn:true,
		        autoclose: true,
		        minView:0,
		        maxView:4,
		        endDate:new Date()
			 });
			$("#filterStartTime").change(function(){
				var beginDate = $("#filterStartTime").val();
				//var now = new Date();
				$("#filterEndTime").datetimepicker('remove');
				$("#filterEndTime").val(business.getCurrentDate()+" 23:59");
				$("#filterEndTime").datetimepicker({
					language:'cn',
					format:'yyyy-mm-dd hh:ii',
					todayBtn: true,
			        clearBtn:true,
					autoclose: true,
					minView:0,
					maxView:4,
					startDate:new Date(beginDate),
					endDate:new Date()
				})
			 });
			
			//结束时间自定义时间段
			$("#finishStartTime").datetimepicker({
				language:'cn',
		        format:'yyyy-mm-dd hh:ii',
		        todayBtn: true,
		        clearBtn:true,
		        autoclose: true,
		        minView:0,
		        maxView:4,
		        endDate:new Date()
			 });
			$("#finishStartTime").change(function(){
				var beginDate = $("#finishStartTime").val();
				//var now = new Date();
				$("#finishEndTime").datetimepicker('remove');
				$("#finishEndTime").val(business.getCurrentDate()+" 23:59");
				$("#finishEndTime").datetimepicker({
					language:'cn',
					format:'yyyy-mm-dd hh:ii',
					todayBtn: true,
			        clearBtn:true,
					autoclose: true,
					minView:0,
					maxView:4,
					startDate:new Date(beginDate),
					endDate:new Date()
				})
			 });
	//选择地区
	var htmlCity = "<option value=''>—市级—</option>";
	var htmlCounty = "<option value=''>—县级—</option>";
	var belongAreaCode = '${authentication.belongAreaId}';
	$.ajax({
		type : "post",
		url : WEBPATH + "/tArea/chickUserArea",
		dataType : "json",
		data : {},
		success : function(data) {
			if (data.cityStatus == '1') {
				//省级用户
				$.ajax({
					type : "post",
					url : WEBPATH + "/tArea/cityList",
					dataType : "json",
					success : function(data) {
						$("#belongCity")
								.append("<option value=''>请选择</option>");
						$("#belongCounty").append(
								"<option value=''>请选择</option>");
						$.each(data, function(i, item) {
							$("#belongCity").append(
									"<option value="+item.code+">" + item.name
											+ "</option>");
						});
					}
				});
			} else if (data.cityStatus == "2") {
				//市级用户
				$("#belongCity").append(
						"<option selected value="+data.cityCode+">"
								+ data.cityName + "</option>");
				$.ajax({
					type : "post",
					url : WEBPATH + "/tArea/countyListByCode",
					dataType : "json",
					data : {
						parentCode : data.cityCode
					},
					success : function(data) {
						$("#belongCounty").append(
								"<option value=''>请选择</option>");
						$.each(data, function(i, item) {
							$("#belongCounty").append(
									"<option value="+item.code+"  >"
											+ item.name + "</option>");
						});
					}
				});
			} else {
				//县级用户
				$("#belongCity").append(
						"<option selected value="+data.cityCode+">"
								+ data.cityName + "</option>");
				$("#belongCounty").append(
						"<option selected value="+data.countyCode+"  >"
								+ data.countyName + "</option>");
			}
		}
	});
	//
	$("#belongCity").change(
			function() {
				if ($(this).val() == "") {
					$("#belongCounty option").remove();
					$("#belongCounty").html(htmlCounty);
					return;
				}
				var parentCode = $(this).val();
				$("#belongCounty option").remove();
				$.ajax({
					type : "post",
					url : WEBPATH + "/tArea/countyListByCode",
					dataType : "json",
					data : {
						parentCode : parentCode
					},
					success : function(data) {
						$("#belongCounty").html(htmlCounty);
						$.each(data, function(i, item) {
							$("#belongCounty").append(
									"<option value="+item.code+"  >"
											+ item.name + "</option>");
						});
					}
				});
			});

	$.ajax({
		type : "post",
		url : WEBPATH + "/tArea/cityList",
		dataType : "json",
		success : function(data) {
			$("#powerCountry").html(htmlCity);
			$("#powerCity").append("<option value=''>请选择</option>");
			$.each(data, function(i, item) {
				$("#powerCity").append(
						"<option value="+item.code+">" + item.name
								+ "</option>");
			});
		}
	});

	$("#belongCity").change(
			function() {
				if ($(this).val() == "") {
					$("#belongCounty option").remove();
					$("#belongCounty").html(htmlCounty);
					return;
				}
				var parentCode = $(this).val();
				$("#belongCounty option").remove();
				$.ajax({
					type : "post",
					url : WEBPATH + "/tArea/countyListByCode",
					dataType : "json",
					data : {
						parentCode : parentCode
					},
					success : function(data) {
						$("#belongCounty").html(htmlCounty);
						$.each(data, function(i, item) {
							$("#belongCounty").append(
									"<option value="+item.code+"  >"
											+ item.name + "</option>");
						});
					}
				});
			});
	
	//设为公开
	function Open(id){
		$.ajax({
			method:'GET',
			url:WEBPATH+'/feedBack/openFeedback',
			data:{id:id},
			success:function(data){
				if(data.meta.code==200){
					swal({
    					title : "提示",
    					text : data.meta.message,
    					type : "success"
    				});
					getList();
				}else{
					swal({
    					title : "提示",
    					text : data.meta.message,
    					type : "error"
    				});
				}
			}
		});
	}
	
	//撤回公开
	function Close(id){
		$.ajax({
			method:'GET',
			url:WEBPATH+'/feedBack/closeFeedback',
			data:{id:id},
			success:function(data){
				if(data.meta.code==200){
					swal({
    					title : "提示",
    					text : data.meta.message,
    					type : "success"
    				});
					getList();
				}else{
					swal({
    					title : "提示",
    					text : data.meta.message,
    					type : "error"
    				});
				}
			}
		});
	}
	//导出Excel
	$("#downloadExcel").click(function(){
		var createTimeStart = $("#createTimeStart").val();
		var createTimeEnd = $("#createTimeEnd").val();
		var filterStartTime = $("#filterStartTime").val();
		var filterEndTime = $("#filterEndTime").val();
		var finishStartTime = $("#finishStartTime").val();
		var finishEndTime = $("#finishEndTime").val();
		var questionType = $("#questionType").val();
		var priority = $("#priority").val();
		var status = $("#status").val();
		var theme = $("#theme").val();
		var menuId = '${pageCode}';
		window.location.href=WEBPATH+'/feedBack/downloadExcel?theme='+theme
												+"&createTimeStart="+createTimeStart
												+"&menuId="+menuId+"&createTimeEnd="+createTimeEnd
												+"&filterStartTime="+filterStartTime
												+"&filterEndTime="+filterEndTime
												+"&finishStartTime="+finishStartTime
												+"&finishEndTime="+finishEndTime
												+"&quesTypeCode="+questionType
												+"&levelCode="+priority
												+"&stateCode="+status;
	});
</script>
