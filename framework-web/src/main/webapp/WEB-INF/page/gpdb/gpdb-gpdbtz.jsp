<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
</head>
<body class="overflow-hidden">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
	<div class="main-container">
		<div class="padding-md">
		
		<div class="row">
						<div class="col-md-12">
							<div class="smart-widget">
								<div class="smart-widget-inner">									
									<div class="smart-widget-body form-horizontal">
									<div class="form-group">
									<label for="所属行政区" class="control-label col-md-2 col-sm-2">违法主体所在行政区</label>
									<div class="col-md-1 col-sm-2">
										<select class="form-control" id ="belong_province">
											<option value="35000000">福建省</option>
										</select>
									</div>
									<div class="col-md-2 col-sm-2">
										<select class="form-control" id="belong_city">
										</select>
									</div>
									<div class="col-md-2 col-sm-2">
										<select class="form-control" id="belong_county">
										
										</select>
									</div>
									  <div class="col-md-2 col-sm-2"> 
                                    <div class="custom-checkbox" style="margin:7px auto;">
                                                <input type="checkbox" id="areaStatus" value ="">
                                                 <label for="areaStatus" class="checkbox-blue"></label>
                                                </div> 只选本级
                                     </div>
									</div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">挂牌通知文书名称</label>
                                            <div class="col-md-3">
                                            <input id='swingTagName' name="swingTagName" type="text" placeholder="挂牌通知文书名称" class="form-control" data-parsley-required="true">
                                            </div> 
                                            <label class="control-label col-md-2">挂牌通知文号</label>
                                            <div class="col-md-3">
                                            <input id="swingTagMark" name="swingTagMark" type="text" placeholder="挂牌通知文号" class="form-control" data-parsley-required="true">
                                            </div>                                             
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">发布日期</label>
                                            <div class="col-lg-3" style="padding:0px;">
                                                <div class="col-md-6">
                                                <input type="text" readonly="readonly" id="startReleaseDate" name="startReleaseDate" placeholder="开始时间" class="form-control" data-parsley-required="true">
                                                </div> 
                                                <div class="col-md-6">
                                                <input type="text" readonly="readonly"  id="endReleaseDate" name="endReleaseDate" placeholder="结束时间" class="form-control" data-parsley-required="true">
                                                </div>
                                            </div>
 
                                            <label class="control-label col-md-2">发起单位</label>
                                            <div class="col-md-3 col-sm-2">
                                                <div class="input-group">
                                                <input type="text" readonly="readonly" placeholder="发起单位" id ="releaseUnitName"  name ="releaseUnitName" class="form-control">
                                                 <input type="hidden" readonly="readonly" id ="releaseUnitId"  name ="releaseUnitId" class="form-control">
                                                    <div class="input-group-btn">
                                                    	  <button  type="button" class="btn btn-info no-shadow"
                                             					data-remote="${webpath}/swingTagQuery/chick-releaseUnit-page?status=1"
                                             			 tabindex="-1" data-toggle="modal" data-target="#handlingUnitModeler">选择</button>
                                                       <!--  <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#setPower">选择</button>	 -->										
                                                   </div>
                                                </div>
                                            </div>                                         
                                        </div>	
                                        <div class="form-group">                                              
                                            <label class="control-label col-md-2">限办日期</label>
                                            <div class="col-lg-3" style="padding:0px;">
                                                <div class="col-md-6">
                                                <input readonly="readonly" id="startLimitTime" name="startLimitTime" type="text" placeholder="开始时间" class="form-control" data-parsley-required="true">
                                                </div> 
                                                <div class="col-md-6">
                                                <input readonly="readonly" id="endLimitTime" name ="endLimitTime" type="text" placeholder="结束时间" class="form-control" data-parsley-required="true">
                                                </div>
                                            </div>    
                                            <label class="control-label col-md-2">办理单位</label>
                                            <div class="col-md-3 col-sm-2">
                                            
                                                <div class="input-group">
                                                <input type="text" id ="manageUnitName" placeholder="办理单位" name="manageUnitName" class="form-control" readonly="readonly">
                                               	<input type="hidden" id ="manageUnitId" name="manageUnitId" class="form-control" >
                                                    <div class="input-group-btn">
                                                    <button  type="button" class="btn btn-info no-shadow"
                                             					data-remote="${webpath}/swingTagQuery/chick-releaseUnit-page?status=0"
                                             			 tabindex="-1" data-toggle="modal" data-target="#handlingUnitModeler">选择</button>
                                                   </div>
                                                </div>
                                            </div>                                   
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">违法主体名称</label>
                                            <div class="col-md-3">
                                            <input type="text" placeholder="违法主体名称"
                                             id ="lawObjectName" name="lawObjectName"
                                             class="form-control" data-parsley-required="true">
                                            </div>
                                            <label class="control-label col-md-2">挂牌状态</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id="isSwingTag" name="isSwingTag">
                                                    <option value="">——请选择——</option>
                                                    <option value="1">挂牌中</option>
                                                    <option value="0">已解牌</option>
                                                </select>
                                            </div>                                            
                                        </div> 
                                        <div class="form-group">
                                        	<label class="control-label col-md-2">解牌日期</label>
                                            <div class="col-lg-3" style="padding:0px;">
                                                <div class="col-md-6">
                                                <input type="text" id="startRelieveTime" name ="startRelieveTime" readonly="readonly" placeholder="开始时间" class="form-control" data-parsley-required="true">
                                                </div> 
                                                <div class="col-md-6">
                                                <input type="text" id="endRelieveTime" name="endRelieveTime" readonly="readonly" placeholder="结束时间" class="form-control" data-parsley-required="true">
                                                </div>
                                            </div>
                                            <label class="control-label col-md-2">办理状态</label>
                                            <div class="col-md-3">
                                                <select class="form-control" id ="nodeTimeoutState" name ="nodeTimeoutState">
                                                    <option value="">——请选择——</option>
                                                    <option value="0">正常</option>
                                                    <option value="1">预警</option>
                                                    <option value="2">超时</option>
                                                </select>
                                            </div>                                                                                     
                                        </div>                                        
                                        <div class="form-group">
                                            <div class="col-md-10 text-right">
                                            <button id='swingTagQuerySearch' id="searchBtn" class="btn btn-info" style="width:80px;">查询</button>
                                            </div>
                                        </div>
										
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="row">
                        <div class="col-md-12">
                        	<div class="smart-widget-body form-horizontal">
                                <div class="form-group" >
                                    <div class="col-md-12 text-right">
                                        <button class="btn btn-info"  onClick="downloadExcel()" type="button" style="width:100px; margin-right:10px;">导出EXCEL</button>
                                    </div>
                                </div>
                            </div>
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 挂牌详细信息
                                </div>
                              <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div>
							  	<div id="div_container" style="text-align:center;">
                                    <!-- 固定表头 table外层必须包有一个div  id="my_div"   class="fakeContainer first_div" 引入superTables.js  在页面下面还需要调用 -->
                                    <div id="my_div" class="fakeContainer first_div" style="padding:1px">
                                       
                                    <table class="smart-widget-body form-horizontal"
										id="swingTagQueryTable"></table>
                                        
                                    </div>
                                </div>

								  
                             </div>
                            </div>
                        </div>
					</div>
				</div>
			</div>
			
			<div class="modal fade" id="lawobjectmodel" tabindex="-1"
					role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
					<div class="modal-dialog  modal-lg">
						<div class="modal-content"></div>
					</div>
			</div>
			<!-- 办理单位model start  -->
			<div class="modal fade" id="handlingUnitModeler" tabindex="-1"
					role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
					<div class="modal-dialog  modal-lg">
						<div class="modal-content"></div>
					</div>
			</div>
<script type="text/javascript">
$(document).ready(function(){
	//监听enter查询，内包含监听回退键
 	 business.listenEnter("searchBtn");
	});
var pageNumber=1 ;
var pageSize=15;
				$(document).ready(function(){
					loadArea();
					$("#belong_city").change(function(){
					    if ($(this).val() == "") {
					    	 $("#belong_county option").remove();
					    	 $("#belong_county").append("<option value=''>——请选择——</option>"); 
					    	return;
					    }
					    var parentCode = $(this).val();
					    $.ajax({
					        type:"post",
					        url:WEBPATH+"/tArea/countyListByCode",
					        dataType:"json",
					        async:false,
					        data:{parentCode:parentCode},
					        success:function(data){
					        	 $("#belong_county option").remove();
					        	 $("#belong_county").append("<option value=''>——请选择——</option>"); 
						         $.each(data,function(i,item){
						          	$("#belong_county").append("<option value="+item.code+"  >"+item.name+"</option>"); 
						         });
					        }
					    });
					});
					
					//返回按钮之后参数和页码的回显
					var params = $("#groundBackParams").val();
					if(params != null && params != '' && params != 'undefined'){
						
						var jsonParam = $.parseJSON(params);
						
						pageNumber = parseInt(jsonParam['pageNumber']);
						pageSize = parseInt(jsonParam['pageSize']);
						for(var key in jsonParam){
							 
							//绑定设定条件
							if(key=='belong_city'){
								$("#belong_city").find("option[value='"+jsonParam[key]+"']").attr("selected","selected");
								//$("#"+key).val(jsonParam[key]);
								//$("#belong_county option").remove();
								//$("#belong_city").trigger('change');//值改变之后触发加载事件
								$("#"+key).trigger('change');//值改变之后触发加载事件
								continue;
							}
							
							/* if(key=='belong_county'){
								$("#"+key).find("option[value='"+jsonParam[key]+"']").attr("selected",true);
								$("#"+key).val(jsonParam[key]);
								alert(jsonParam[key]);
								alert($("#belong_county").val());
								continue;
							} */
							
							$("#"+key).val(jsonParam[key]);
						}
					}
					
					LoadingSwingTagManageItems();
					$('#swingTagQueryTable').bootstrapTable('hideColumn', 'id');
					$('#swingTagQueryTable').bootstrapTable('hideColumn', 'lawObjectId');
					
				});
				
				function LoadingSwingTagManageItems() {
					$('#swingTagQueryTable').bootstrapTable({
						method : 'post',
						dataType : "json",
						url : WEBPATH + '/swingTagQuery/swingtagQueryList',
						undefinedText : '-',
						pagination : true, // 分页  
						striped : true, // 是否显示行间隔色  
						queryParamsType : "",
						locale : 'zh-CN',
						clickToSelect : true,
						pageList : [10, 20, 30, 50 ], // 自定义分页列表
						contentType : "application/x-www-form-urlencoded",
						// showColumns : true, // 显示隐藏列  
						sidePagination : "server", //服务端请求
						queryParams:queryParams,//参数
						uniqueId : "id", // 每一行的唯一标识  
						columns : [{
							field : "",
							title : "操作",
							align : 'center',
							width:'100px',
							formatter:function(value,row,index){
								return "<a href='#' style='cursor:pointer;' onclick=\"swingTagQueryBtn('"+row.id+"')\"><i class='fa fa-search' style='color:#23b7e5;'>详情</i></a>";
							}
						},{
							field : "",
							title : "序号",
							align : 'center',
							formatter:function(value,row,index){
								return index+1;
							}
						}, {
							field : "releaseUnit",
							title : "发起单位",
							align : 'center'
						}, {
							field : "lawObjectId",
							title : "发起单位",
							align : 'center'
						}, {
							field : "lawObjectName",
							title : "违法主体",
							align : 'center',
							formatter:function(value,row,index){
								return "<a href='#' style='color:#23b7e5;' onclick=\"queryLawObjectBtn('"+row.lawObjectId+"')\">"+value+"</a>"
							}
						}, {
							field : "belongAreaName",
							title : "违法主体所在行政区",
							align : 'center'
						}, {
							field : "isSwingTag",
							title : "挂牌状态",
							align : 'center',
							formatter:function(value,row,index){
								if(value ==1){
									return "挂牌中";
								}else{
									return "已解牌";
								}
							}
						}, {
							field : "nodeTimeoutState",
							title : "办理状态",
							align : 'center',
							formatter:function(value,row,index){
									if(row.nodeTimeoutState ==0){
										return "正常";
									}else if(row.nodeTimeoutState ==1){
										return "预警";
									}else if(row.nodeTimeoutState ==2){
										return "超时";
									}else{
										return "";
									}
							}
						},
						 {
							field : "releaseDate",
							title : "发布日期",
							align : 'center',
							formatter: function (value, row, index) {
									if(value==null){
										return "-";
									}else {
										var date = new Date(value);
						                var y = date.getFullYear();
						                var m = date.getMonth() + 1;
						                var d = date.getDate();
						                return y + '-' +m + '-' + d;
									}
				              } 
						},{
							field : "swingTagName",
							title : "挂牌通知文书名称",
							align : 'center'
						}, {
							field : "swingTagMark",
							title : "挂牌通知文号",
							align : 'center'
						}, 
						{
							field : "manageUnitName",
							title : "办理单位",
							align : 'center'
						},
						{
							field : "limitTime",
							title : "限办日期",
							align : 'center',
							formatter: function (value, row, index) {
									if(value==null){
										return "-";
									}else {
										var date = new Date(value);
						                var y = date.getFullYear();
						                var m = date.getMonth() + 1;
						                var d = date.getDate();
						                return y + '-' +m + '-' + d;
								}
				             }
						},
						{
							field : "relieveTime",
							title : "解牌日期",
							align : 'center',
							formatter: function (value, row, index) {
									var time = row.relieveTime;
									if(time == null){
										return "";
									}else {
										var date = new Date(time);
						                var y = date.getFullYear();
						                var m = date.getMonth() + 1;
						                var d = date.getDate();
						               // alert(y + '-' +m + '-' + d);
						                return y + '-' +m + '-' + d;
								}
				             }
						},{
							field : "creatUserName",
							title : "填报人",
							align : 'center'
						}],
						responseHandler : function(res) {
							return {
								total : res.data.total,
								rows : res.data.list
							};
						},
						onCheck : function(row, $element) {

						},//单击row事件
						onUncheck : function(row, $element) {

						},
						onUncheckAll : function(row, $element) {

						},
						onCheckAll : function(row, $element) {

						},
						onRefresh : function() {

						},
						formatLoadingMessage : function() {
							return "请稍等，正在加载中...";
						},
						formatNoMatches : function() { //没有匹配的结果
							return '无符合条件的记录';
						}
					});
					}
					function queryParams(params) {
						var swingTagName =$("#swingTagName").val();
					    var swingTagMark =  $("#swingTagMark").val();
					    var belongProvince = $("#belong_province").val();
					    var belongCity =  $("#belong_city").val();
					    var belongCounty = $("#belong_county").val();
					    var areaStatus = $("#areaStatus").val();
					    var startReleaseDate = $("#startReleaseDate").val();
					    var endReleaseDate =  $("#endReleaseDate").val();
					    var releaseUnitId =  $("#releaseUnitId").val(); //发起单位id
					    var releaseUnitName =  $("#releaseUnitName").val(); //发起单位name
					    var startLimitTime =  $("#startLimitTime").val();
					    var endLimitTime =  $("#endLimitTime").val(); 
					    var manageUnitId =  $("#manageUnitId").val();
					    var manageUnitName =  $("#manageUnitName").val();
					    var lawObjectName =  $("#lawObjectName").val();
					    var isSwingTag =  $("#isSwingTag").val();
					    var startRelieveTime =  $("#startRelieveTime").val();
					    var endRelieveTime =  $("#endRelieveTime").val();
					    var nodeTimeoutState =  $("#nodeTimeoutState").val();
					    var startRelieveTime =  $("#startRelieveTime").val();
					    var endRelieveTime =  $("#endRelieveTime").val();
					    var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
					    	 pageNumber: params.pageNumber,
					         pageSize: params.pageSize,
							 swingTagName:swingTagName,
							 swingTagMark:swingTagMark,
							 belongProvince:belongProvince,
							 belongCity:belongCity,
							 belongCounty:belongCounty,
							 areaStatus:areaStatus,
							 startReleaseDate:startReleaseDate,
							 endReleaseDate:endReleaseDate,
							 releaseUnitId:releaseUnitId,
							 releaseUnitName:releaseUnitName,
							 startLimitTime:startLimitTime,
							 endLimitTime:endLimitTime,
							 manageUnitId:manageUnitId,
							 manageUnitName:manageUnitName,
							 lawObjectName:lawObjectName,
							 isSwingTag:isSwingTag,
							 startRelieveTime:startRelieveTime,
							 endRelieveTime:endRelieveTime,
							 nodeTimeoutState:nodeTimeoutState,
							 startRelieveTime:startRelieveTime,
							 endRelieveTime:endRelieveTime,
					    };
					    return temp;
					}
				
				//处罚主体详细 
				function queryLawObjectBtn(lawObjectId){
					var options = {
							remote:WEBPATH+'/swingtagmanage/select-law-object-page?lawObjectId='+lawObjectId
						  };
					$('#lawobjectmodel').modal(options);
				}
					
				//绑定搜索按钮
				$('#swingTagQuerySearch').click(function() {
					$('#swingTagQueryTable').bootstrapTable('refreshOptions',{pageNumber:pageNumber,pageSize:pageSize});
				});
				
				$("[name='startRelieveTime']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
				  	 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='endRelieveTime']").datetimepicker('setStartDate',new Date($("[name='startRelieveTime']").val()));
				});
				
				$("[name='endRelieveTime']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
					 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='startRelieveTime']").datetimepicker('setEndDate',new Date($("[name='endRelieveTime']").val()));
				});
				
				
				$("[name='startLimitTime']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
				  	 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='endLimitTime']").datetimepicker('setStartDate',new Date($("[name='startLimitTime']").val()));
				});
				$("[name='endLimitTime']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
					 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='startLimitTime']").datetimepicker('setEndDate',new Date($("[name='endLimitTime']").val()));
				});
				
				
				
				$("[name='startReleaseDate']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
				  	 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='endReleaseDate']").datetimepicker('setStartDate',new Date($("[name='startReleaseDate']").val()));
				});
				$("[name='endReleaseDate']").datetimepicker({
				     format:'yyyy-mm-dd',
				     clearBtn:true,
					 language: 'cn',
					 autoclose : true,
					 minView : 'year',
					 maxView : 'decade',
				}).on('changeDate',function(ev){
					$("[name='startReleaseDate']").datetimepicker('setEndDate',new Date($("[name='endReleaseDate']").val()));
				});
				
				
				function swingTagQueryBtn(swingTagSubjectId){
				    macroMgr.onLevelTwoMenuClick(null, 'swingTagQuery/swingTagQuery',{swingTagSubjectId:swingTagSubjectId});
				}
				
				$("#areaStatus").change(function(){
					var areaStatus = document.getElementById('areaStatus');
					if(areaStatus.checked){
						$("#areaStatus").val("1");
					}else{
						$("#areaStatus").val("");
					}
				});
				
				
				
				//导出xls文件
				function downloadExcel(){
					var swingTagName =$("#swingTagName").val();
				    var swingTagMark =  $("#swingTagMark").val();
				    var belongProvince = $("#belong_province").val();
				    var belongCity =  $("#belong_city").val();
				    var belongCounty = $("#belong_county").val();
				    var areaStatus = $("#areaStatus").val();
				    var startReleaseDate = $("#startReleaseDate").val();
				    var endReleaseDate =  $("#endReleaseDate").val();
				    var releaseUnitId =  $("#releaseUnitId").val(); //发起单位id
				    var startLimitTime =  $("#startLimitTime").val();
				    var endLimitTime =  $("#endLimitTime").val(); 
				    var manageUnitId =  $("#manageUnitId").val();
				    var lawObjectName =  $("#lawObjectName").val();
				    var isSwingTag =  $("#isSwingTag").val();
				    var startRelieveTime =  $("#startRelieveTime").val();
				    var endRelieveTime =  $("#endRelieveTime").val();
				    var nodeTimeoutState =  $("#nodeTimeoutState").val();

					var path = WEBPATH+'/swingTagQuery/download-swingTag'+
					'?swingTagName='+swingTagName+
					'&&swingTagMark='+swingTagMark+
					'&&belongProvince='+belongProvince+
					'&&belongCounty='+belongCounty+
					'&&belongCity='+belongCity+
					'&&areaStatus='+areaStatus+
					'&&startReleaseDate='+startReleaseDate+
					'&&endReleaseDate='+endReleaseDate+
					'&&releaseUnitId='+releaseUnitId +
					'&&startLimitTime='+startLimitTime+
					'&&endLimitTime='+endLimitTime+
					'&&manageUnitId='+manageUnitId+
					'&&lawObjectName='+lawObjectName+
					'&&isSwingTag='+isSwingTag+
					'&&startRelieveTime='+startRelieveTime+
					'&&endRelieveTime='+endRelieveTime+
					'&&nodeTimeoutState='+nodeTimeoutState+' ';
				    window.location.href=path;
				}
				
				//区划加载 
				function loadArea(){
					var htmlCity = "<option value=''>——请选择——</option>"; 
			  		var htmlCounty = "<option value=''>——请选择——</option>"; 
			  		var swingTagSnapInId = $("#swingTagSnapInId").val();
			  			$.ajax({
			  	            type:"post",
			  	            url:WEBPATH+"/tArea/chickUserArea",
			  	            async:false,
			  	            dataType:"json",
			  	            data:{},
			  	            success:function(data){
			  	            	if(data.cityStatus =='1'){
			  						//省级用户
			  						$.ajax({
			  							type:"post",
			  							url:WEBPATH+"/tArea/cityList",
			  							dataType:"json",
			  							async:false,
			  							success:function(data){
			  								$("#belong_city").append("<option value=''>——请选择——</option>"); 
			  								$("#belong_county").append("<option value=''>——请选择——</option>"); 
			  								$.each(data,function(i,item){
			  									$("#belong_city").append("<option value="+item.code+">"+item.name+"</option>");
			  								});
			  							}
			  						});
			  	            	}else if(data.cityStatus =="2"){
			  	            		//市级用户
			  						$("#belong_city").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
			  	            	    $.ajax({
			  	                        type:"post",
			  	                        url:WEBPATH+"/tArea/countyListByCode",
			  	                        dataType:"json",
			  	                        data:{parentCode:data.cityCode},
			  	                        success:function(data){
			  								$("#belong_county").append("<option value=''>——请选择——</option>"); 
			  								$.each(data,function(i,item){
			  									$("#belong_county").append("<option value="+item.code+"  >"+item.name+"</option>"); 
			  								});
			  	                        }
			  	                    });
			  	            	}else{
			  	            		//县级用户
			  						$("#belong_city").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
			  						$("#belong_county").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
			  	            	}
			  	            }
			  	        });
				}
				
				</script>
				
		
  	
</body>

</html>
