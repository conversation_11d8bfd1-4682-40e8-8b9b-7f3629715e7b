.divider {
	margin-top: 40px;
	}
	
.button {
	/* appearance */
	background-color: #3f3f3f;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.0) 0%,
		rgba(255,255,255,0.1) 50%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(100%,rgba(255,255,255,0.0)),
		color-stop(50%,rgba(255,255,255,0.1)));
	border: 1px solid #000000;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	-webkit-box-shadow: 0 1px 0 rgba(139,139,139,1) inset, 0 1px 0 rgba(88,88,88,1);
	-moz-box-shadow: 0 1px 0 rgba(139,139,139,1) inset, 0 1px 0 rgba(88,88,88,1);
	box-shadow: 0 1px 0 rgba(139,139,139,1) inset, 0 1px 0 rgba(88,88,88,1);
	cursor: pointer;
	
	/* position */
	display: inline-block;
	margin: 10px;
	
	/* size */
	padding: 0 10px;
	
	/* text */
	color: #eaeaea;
	font-size: 12px;
	line-height: 30px;
	text-decoration: none;
	white-space: nowrap;
	}
.button:hover {
	/* appearance */
	background-color: #6495ed;
	-webkit-box-shadow: 0 0 3px #6495ed;
	-moz-box-shadow: 0 0 3px #6495ed;
	box-shadow: 0 0 3px #6495ed;
	}

.myTableWrapper {
	width: 800px;
	height: 500px;
	}

.height250 {
        height: 250px;
        overflow-x: auto;
        overflow-y: auto;
}

.height400 {
        height: 400px;
        overflow-x: auto;
        overflow-y: auto;
}

.fancyTable td, .fancyTable th {
	/* appearance */
	border: 1px solid #778899;
	
	/* size */
	padding: 5px;
	}

.fancyTable {
	/* text */
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	}

.fancyTable tbody tr td {
	/* appearance */
	background-color: #eef2f9;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.4) 0%,
		rgba(255,255,255,0.2) 50%,
		rgba(255,255,255,0.1) 51%,
		rgba(255,255,255,0.0) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.4)),
		color-stop(50%,rgba(255,255,255,0.2)),
		color-stop(51%,rgba(255,255,255,0.1)),
		color-stop(100%,rgba(255,255,255,0.0)));
		
	/* text */
	color: #262c31;
	font-size: 11px;
	}

.fancyTable tbody tr.odd td {
	/* appearance */
	background-color: #d6e0ef;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.4) 0%,
		rgba(255,255,255,0.2) 50%,
		rgba(255,255,255,0.1) 51%,
		rgba(255,255,255,0.0) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.4)),
		color-stop(50%,rgba(255,255,255,0.2)),
		color-stop(51%,rgba(255,255,255,0.1)),
		color-stop(100%,rgba(255,255,255,0.0)));
	}

.fancyTable thead tr th,
.fancyTable thead tr td,
.fancyTable tfoot tr th, 
.fancyTable tfoot tr td {
	/* appearance */
	background-color: #8ca9cf;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.4) 0%,
		rgba(255,255,255,0.2) 50%,
		rgba(255,255,255,0.1) 51%,
		rgba(255,255,255,0.0) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.4)),
		color-stop(50%,rgba(255,255,255,0.2)),
		color-stop(51%,rgba(255,255,255,0.1)),
		color-stop(100%,rgba(255,255,255,0.0)));
		
	/* text */
	color: #121517;
	font-size: 12px;
	font-weight: bold;
	text-shadow: 0 1px 1px #e8ebee;
	}
	

/* Fancy Dark Table */	
.fancyDarkTable .numeric {
	/* text */
	text-align: right;
	}

.fancyDarkTable td, .fancyDarkTable th {
	border: 1px solid #000000;
	padding: 5px;
}

.fancyDarkTable thead tr th {
	padding: 10px 5px 10px 5px;
	}

.fancyDarkTable {
	/*border-collapse: separate;*/
	
	/* text */
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	}

.fancyDarkTable tbody tr td {
	/* appearance */
	background-color: #48535e;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.0) 0%,
		rgba(255,255,255,0.02) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.0)),
		color-stop(100%,rgba(255,255,255,0.02)));
	border-bottom-color: #22272e;
	border-top-color: #708090;
	border-right-color: #000;
	border-left-color: #3c454f;

	/* size */
	padding: 10px 5px 30px 5px;

	/* text */
	color: #FFFFFF;
	font-size: 11px;
	font-weight: bold;
	text-shadow: 0 -1px 1px #000000;
	}

.fancyDarkTable tbody tr.odd td {
	/* appearance */
	background-color: #3c454f;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.0) 0%,
		rgba(255,255,255,0.02) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.0)),
		color-stop(100%,rgba(255,255,255,0.02)));
	border-right-color: #000;
	}

.fancyDarkTable thead tr th,
.fancyDarkTable tfoot tr td {
	/* appearance */
	background-color: #0b0d10;
	background-image: -moz-linear-gradient(
		top,
		rgba(255,255,255,0.4) 0%,
		rgba(255,255,255,0.2) 50%,
		rgba(255,255,255,0.1) 51%,
		rgba(255,255,255,0.0) 100%);
	
	background-image: -webkit-gradient(
		linear, left top, left bottom,
		color-stop(0%,rgba(255,255,255,0.4)),
		color-stop(50%,rgba(255,255,255,0.2)),
		color-stop(51%,rgba(255,255,255,0.1)),
		color-stop(100%,rgba(255,255,255,0.0)));
		
	/* text */
	color: #ffffff;
	font-size: 12px;
	font-weight: bold;
	text-shadow: 0 -1px 1px #000;
	}
	
.fancyDarkTable .fht-head {
	-webkit-box-shadow: 0 5px 10px #000;
	z-index: 1;
	position: relative;
	}