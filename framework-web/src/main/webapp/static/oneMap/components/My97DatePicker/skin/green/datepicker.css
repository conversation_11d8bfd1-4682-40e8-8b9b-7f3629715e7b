/* 
 * My97 DatePicker 4.7
 * 皮肤名称:green
 */ 
 /*外边框*/
.WdateDiv *{
	font-family:"宋体";
	font-size:12px;
}
.WdateDiv{
	width:180px;
	background-color:#fff;
	border:#87a34d 1px solid;
	padding:0x 2px 0px 2px;
}

.WdateDiv2{
	width:360px;
}

.WdateDiv .NavImg a{
	cursor:pointer;
	display:block;
	width:16px;
	height:16px;
	margin-top:2px;
}

.WdateDiv .NavImgll a{
	float:left;
	background:url(img.gif) no-repeat;
}
.WdateDiv .NavImgl a{
	float:left;
	background:url(img.gif) no-repeat -16px 0px;
}
.WdateDiv .NavImgr a{
	float:right;
	background:url(img.gif) no-repeat -32px 0px;
}
.WdateDiv .NavImgrr a{
	float:right;
	background:url(img.gif) no-repeat -48px 0px;
}
/*标题*/
.WdateDiv #dpTitle{
	height:24px;
	padding:1px;
}
/*年月输入框*/
.WdateDiv .yminput{
	text-align:center;
	border:0px;
	height:20px;
	line-height:16px;
	width:50px;
	color:#333;
	background-color:transparent;
	cursor:pointer;
}
/*年月输入框*/
.WdateDiv .yminputfocus{
	text-align:center;
	border:#999 1px solid;
	font-weight:bold;
	color:#333;	
	height:20px;
	line-height:16px;
	width:50px;
}
/*年月下拉框*/
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;
	border:#ccc 1px solid;
	display:none;
	padding:2px;
}
/*下拉框字*/
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
	color:#333;
}
/*下拉框hover*/
.WdateDiv .menuOn{
	cursor:pointer;
	background-color:#ebf4d8;
}
/*未知*/
.WdateDiv .invalidMenu{
	color:#aaa;
}

.WdateDiv .YMenu{
	margin-top:20px;
}

.WdateDiv .MMenu{
	margin-top:20px;
	*width:62px;
}

.WdateDiv .hhMenu{
	margin-top:-90px; 
	margin-left:26px;
}

.WdateDiv .mmMenu{
	margin-top:-46px; 
	margin-left:26px;
}

.WdateDiv .ssMenu{
	margin-top:-24px; 
	margin-left:26px;
}
/*未知*/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
	border-right:#bed393 1px solid;
 }
/*周背景*/
.WdateDiv .MTitle{
	color:#333;
	line-height:22px;
	background-color:#bed393;
}
.WdateDiv .MTitle td{
	border-bottom-width: 2px;
	border-bottom-style: solid;
	border-bottom-color: #FFF;
}
/*快速选择*/
.WdateDiv .MTitle div{
	margin:0px 5px;
}
/*未知*/
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:#bed393 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/*日期*/
.WdateDiv .WdayTable{
	line-height:18px;	
	color:#333;
}
.WdateDiv .WdayTable td{
	text-align:center;
}

.WdateDiv .Wday{
	cursor:pointer;
}
/*日期hover背景*/
.WdateDiv .WdayOn{
	cursor:pointer;
	background-color:#bed393 ;
}
/*周末*/
.WdateDiv .Wwday{
	cursor:pointer;
	color:#F33;
}
/*周末hover*/
.WdateDiv .WwdayOn{
	cursor:pointer;
	background-color:#bed393;
}
/*今天*/
.WdateDiv .Wtoday{
	cursor:pointer;
	font-weight: bold;
}
/*当天背景*/
.WdateDiv .Wselday{
	background-color:#ebf4d8;
}
/*未知*/
.WdateDiv .WspecialDay{
	background-color:#bed393;
}
/*非本月日期*/
.WdateDiv .WotherDay{ 
	cursor:pointer;
	color:#999;	
}
/*非本月日期hover*/
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background-color:#bed393;	
}

.WdateDiv .WinvalidDay{
	color:#aaa;
}

.WdateDiv #dpTime{
	float:left;
	margin-top:3px;
	margin-right:30px;
}

.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
	color:#497F7F;
}
/*未知*/
.WdateDiv #dpTime input{
	height:20px;
	width:18px;
	text-align:center;
	color:#333;
	border:#bed393 1px solid;	
}

.WdateDiv #dpTime .tB{
	border-right:0px;
}

.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}

.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}

.WdateDiv #dpTime #dpTimeUp{
	height:10px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -32px -16px;
}

.WdateDiv #dpTime #dpTimeDown{
	height:10px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -48px -16px;
}
/*快速选择*/
.WdateDiv #dpQS {
 	float:left;
	margin-left:3px;
	margin-top:5px;
	background:url(img.gif) no-repeat 0px -16px;
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;
	margin-top:3px;
}
/*按钮*/
.WdateDiv .dpButton{ 
	height:20px;
	line-height:14px;
	padding:0px;
	width:45px;
	margin:0px 0px 0px 1px;
	border:#87a34d 1px solid;
	background-color:#bed393;
	color:#333;
}