@charset "utf-8";
/* CSS Document */
/**{*/
 /* box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;*/
/*}*/
html,
body {
  padding: 0;
  margin: 0;
  font-family: 'Microsoft yahei';
  height: 100%;
  min-height: 100%;
}
ul,
li,
span,
p,h1,h2,h3,h4,h5,h6,a,dl,dt,dd{
  padding: 0;
  margin: 0;
  list-style: none;
  color: #333333;
  text-decoration: none;
  font-weight: normal;
  font-style: normal;
}
img{
	vertical-align: middle;
	border: 0;
}
input{
	font-family: 'Microsoft yahei';
}
/*滚动条*/   
::-webkit-scrollbar {
	width:8px;
	height:10px;
}
::-webkit-scrollbar-button    {
	background-color:#f8f8f8;
}
::-webkit-scrollbar-track     {
	background:#f8f8f8;
}
::-webkit-scrollbar-track-piece {
	background:#f8f8f8;
}
::-webkit-scrollbar-thumb{
	background:#dcdcdc;
	border-radius:4px;
}
::-webkit-scrollbar-corner {
	background:#f8f8f8;
}
::-webkit-scrollbar-resizer  {
	background:#f8f8f8;
}

/***
 * 以下大框架的样式
 */
.map{
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1;
	width: 100%;
	height: 100%;
	/*background: url(../img/images/leftlist/map.jpg) no-repeat;*/
	background-size: 100% 100%;
}
#mymap{
	width: 100%;
	height: 100%;
}
.left-list-wrap{
	position: relative;
	z-index: 2;
	width: 100px;
	height: 100%;
	background: #fafafa;
	border-right: 1px solid #e6e7ea;
	overflow: visible;
	float: left;
	/*margin-top: 64px;*/
}
/*#mapLeftContent{*/
	/*overflow: hidden;*/
/*}*/
.left-list-wrap dl{
	width: 100%;
	height: 112px;
	border-top: 1px solid #fafafa;
	border-bottom: 1px solid #fafafa;
	text-align: center;
	overflow: hidden;
	position: relative;
	cursor: pointer;
}
.left-list-wrap dl dt{
	margin-top: 22px;
	height: 45px;
}
.left-list-wrap dl dt.img1{
	background: url(../img/images/leftlist/1.png) no-repeat center;
}
.left-list-wrap dl dt.img2{
	background: url(../img/images/leftlist/3.png) no-repeat center;
}
.left-list-wrap dl dt.img3{
	height: 50px;
	background: url(../img/images/leftlist/5.png) no-repeat center top;
}
.left-list-wrap dl dt.img4{
	background: url(../img/images/leftlist/7.png) no-repeat center;
}
.left-list-wrap dl dt.img5{
	background: url(../img/images/leftlist/9.png) no-repeat center;
}
.left-list-wrap dl.cur dt.img1{
	background: url(../img/images/leftlist/2.png) no-repeat center;
}
.left-list-wrap dl.cur dt.img2{
	background: url(../img/images/leftlist/4.png) no-repeat center;
}
.left-list-wrap dl.cur dt.img3{
	height: 50px;
	background: url(../img/images/leftlist/6.png) no-repeat center top;
}
.left-list-wrap dl.cur dt.img4{
	background: url(../img/images/leftlist/8.png) no-repeat center;
}
.left-list-wrap dl.cur dt.img5{
	background: url(../img/images/leftlist/10.png) no-repeat center;
}
.left-list-wrap dl dd{
	font-size: 14px;
	color: #161616;
}
.left-list-wrap dl.cur{
	background: #fff;
	border-top: 1px solid #e6e7ea;
	border-bottom: 1px solid #e6e7ea;
	padding-right: 1px;
}
.left-list-wrap dl.cur dd{
	color: #549f57;
}
.left-list-wrap dl.cur:before{
	content: ' ';
	display: block;
	position: absolute;
	left: 0;
	top: 22px;
	bottom: 22px;
	width: 5px;
	background: #549f57;
}
.left-list-wrap dl.cur:after{
	content: ' ';
	display: block;
	position: absolute;
	top: 50%;
	left: 5px;
	margin-top: -5px;
  	width: 0;
	height: 0;
	border-width: 5px;
	border-style: solid;
	border-color: transparent transparent transparent #549f57;
}
.list-main-wrap{
	position: relative;
	z-index: 1;
	width: 500px;
	height: 100%;
	float: left;
	background: #efefef;
	background: #ffffff;
	box-shadow: 0 0 1px 1px #efece6;
	/*margin-top: 64px;*/
}
.list-main{
	width: 100%;
	height: 100%;
	display: none;
}
.list-main.cur{
	display: block;
}
.list-main-tabname{
	height: 51px;
	border-bottom: 1px solid #e6e7ea;
	overflow: hidden;
	background: #fff;
	border-right: none;
}
.list-main:first-child .list-main-tabname{
	/*border-left: 1px solid #e6e7ea;*/
} 
.list-main-tabname p{
	float: left;
	line-height: 51px;
	font-size: 16px;
	color: #333d53;
	width: 25%;
	text-align: center;
	cursor: pointer;
	font-weight: 600;
}
.list-main-tabname p.cur,.list-main-tabname p:hover{
	color: #549f57;
}
.list-main-tab-wrap{
	width: 100%;
	height: calc(100% - 52px);
	overflow: auto;
}
.list-main-tab{
	display: none;
	overflow: hidden;
}
.list-main-tab.cur{
	display: block;
}
/**以下可以放主要内容的样式**/
.areas{
	overflow: hidden;
	padding: 9px 0;
	border-bottom: 1px solid #efefef;
}
.areas p{
	float: left;
    /*width: 13%;*/
    font-size: 14px;
    padding-left: 6%;
    line-height: 32px;
    /*overflow: hidden;*/
    text-overflow: ellipsis;
    white-space: nowrap;
}
.areas p span{
   cursor: pointer;
   color: #161616;	
}

.areas p.cur span{
	color: #549f57;
}
.small-title{
    overflow: hidden;
    padding: 17px;
}
.small-title p{
	font-size: 16px;
    color: #549f57;	
    float: left;
    font-weight: 600;
}
.small-title span{
	float: right;
	font-size: 14px;
    color: #333d53;
}
.condition{
	border-bottom: 1px solid #efefef;
	margin: 0 17px;
	padding: 20px 0;
}
.condition .time{
	overflow: hidden;
}
.condition .time span,.condition .time input{
	float: left;
	display: inline-block;
	font-size: 14px;
	color: #161616;
	height: 31px;
	line-height: 31px;
}
.condition .time span.zhi{
	padding: 0 9px;
}
.condition .time input[type=text]{
	width: 166px;
	padding-left: 8px;
	height: 29px;
	line-height: 29px;
	background: #f2f2f2 url(../img/images/time.png) no-repeat right center;
    border: 1px solid #f2f2f2;
    outline: none;
    border-radius: 2px;
}
.condition .time input[type=text]:focus{
    border: 1px solid #549f57;
    background: #f2f2f2 url(../img/images/timeGreen.png) no-repeat right center;
}
.condition .radio{
    overflow: hidden;
}
.condition .radio p{
	float: left;
	padding-left:24px;
	height: 20px;
	line-height: 20px;
	font-size: 14px;
	color: #161616;
	background: url(../img/images/bigRadio.png) no-repeat left center;
	cursor: pointer;
	margin-right: 27px;
}
.condition .radio p.cur{
	background: url(../img/images/smallRadio.png) no-repeat left center;
}
.condition .radio-poll p{
	width: 46px;
    height: 25px;
    padding: 0;
    background: #f1f1f1;
    text-align: center;
    line-height: 25px;
    border-radius: 2px;
    margin-right: 14px;
}
.condition .radio-poll p.cur{
	background: #549f57;
	color: #fff;
}
.condition .search{
	overflow: hidden;
}
.condition .search span,.condition .search input,.condition .search a{
	float: left;
	display: inline-block;
	font-size: 14px;
	color: #161616;
	height: 31px;
	line-height: 31px;
}
.condition .search input[type=text]{
	width: 330px;
	padding: 0 8px;
	height: 29px;
	line-height: 29px;
	background: #f2f2f2;
    border: 1px solid #f2f2f2;
    outline: none;
    border-radius: 2px;
}
.condition .search input[type=text]:focus{
    border: 1px solid #549f57;
}
.condition .search a{
	width: 58px;
	background: #549f57;
	border-radius: 2px;
	text-align: center;
	color: #fff;
	margin-left: 10px;
}
.condition .select{
	height: 31px;
}
.condition .select>span{
	float: left;
	display: inline-block;
	font-size: 14px;
	color: #161616;
	height: 31px;
	line-height: 31px;
}
.condition .select .selectdiv{
	float: left;
	width: 170px;
	height: 29px;
	position: relative;
}
.condition .select .selectdiv .select-value{
	line-height: 29px; 
	border:1px solid #f2f2f2; 
	background:#f2f2f2 url(../img/images/input.png) no-repeat right center;
	padding: 0 22px 0px 8px;
    border-radius: 2px;
    font-size: 14px;
    color: #161616;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}
.condition .select .selectdiv.cur .select-value{
	border: 1px solid #549f57;
	background:#f2f2f2 url(../img/images/inputGreen.png) no-repeat right center;
}
.condition .select .selectdiv .select-valueall{
	background: #fafafa;
	border: 1px solid #549f57;
    margin-top: -1px;
	display: none;
}
.condition .select .selectdiv .select-valueall p{
	font-size: 13px;
    color: #161616;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 6px 8px;
    cursor: pointer;
}
.condition .select .selectdiv .select-valueall p:hover{
	background: #549f57;
	color: #fff;
}
.condition .select .selectdiv.cur .select-valueall{
	display: block;
}
.none{
	display: none;
}
.echartsDiv{
	padding: 20px 17px;
}
.border{
	border-top: 10px solid #efefef;
}
.table{
	padding-top: 6px;
}
.table table {
	width: 100%;
	table-layout: fixed;
}
.table td {
  line-height: 34px;
  height: 34px;
  text-align: left;
  color: #161616;
  font-size: 14px;
  /*overflow: hidden;*/
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table .tab-head td{
	font-weight: 600;
	text-align: center;
}
.table td i.location{
	display: block;
	margin: 0 auto;
	width: 20px;
	height: 23px;
	background: url(../img/images/position.png) no-repeat;
	cursor: pointer;
}
.table td i.radio{
	display: block;
	margin: 0 auto;
	width: 25px;
	height: 25px;
	background: url(../img/images/bigRadio.png) no-repeat center center;
	cursor: pointer;
}
.table td i.radio.cur{
	background: url(../img/images/smallRadio.png) no-repeat center center;
}
.table td i.checkbox,.table td i.allcheckbox{
	display: block;
	margin: 0 auto;
	width: 25px;
	height: 25px;
	background: url(../img/images/bigRadio.png) no-repeat center center;
	cursor: pointer;
}
.table td i.checkbox.cur,.table td i.allcheckbox.cur{
	background: url(../img/images/smallRadio.png) no-repeat center center;
}
.table .tab-body td.green{
	color: #549f57;
	font-weight: 600;
}
.table .tab-body td.yellow{
	color: #efb400;
	font-weight: 600;
}
.table .tab-body td.red{
	color: #e70303;
	font-weight: 600;
}
/*弹框*/
.alert{
	width: 485px;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 5;
    margin-left: -242px;
    background: #fff;
    box-shadow: 0 0 5px 2px #b3b0b0;
    display: none;
}
.alert-title{
	overflow: hidden;
	height: 35px;
    background: #549f57;
    position: relative;
    padding: 0 35px 0 10px;
}
.alert-title p{
	float: left;
	font-size: 14px;
    color: #fefefe;
    line-height: 34px;
}
.alert-title p.right{
	float: right;
}
.alert-close{
	width: 20px;
	height: 20px;
	background: url(../img/images/x.png) no-repeat;
	position: absolute;
	right: 10px;
	top: 7px;
	cursor: pointer;
}
.alert-main{
	overflow: hidden;
	padding: 10px 0 15px;
}
.alert-main .info{
	overflow: hidden;
}
.alert-main .info li{
	float: left;
	width: 40%;
    padding: 6px 0 6px 10%;
    font-size: 14px;
}
.alert-main .info li span{
	display: inline-block;
	float: left;
    color: #676767;
}
.alert-main .info li span.val{
	color: #161616;
}
.alert-main .video{
	overflow: hidden;
	padding: 0px 20px;
}
.alert-main .video a{
	float: right;
	font-size: 14px;
	color: #549f57;
}
/*图例*/
.legend {
	background: #fff;
	border-radius: 6px;
	box-shadow: 0px 0px 3px 1px #dbd3c8;
	position: absolute;
	bottom: 20px;
	right: 20px;
	z-index: 5;
	display: none;
	overflow: hidden;
	padding: 0 19px;
}
.legend ul{
	overflow: hidden;
	padding: 3px 0;
}
.legend ul li{
	margin: 15px 0;
	overflow: hidden;
}
.legend ul li div{
	width: 20px;
	height: 20px;
	background: #ccc;
	border-radius: 50%;
	float: left;
}
.legend ul li p{
	font-size: 16px;
	color: #161616;
	float: left;
	text-align: right;
	padding-left: 12px;
	width: 34px;
}

.legend .one div{
	background: #6dca70;
}
.legend .two div{
	background:#f1c93e;
}
.legend .three div{
	background:#f1933e;
}
.legend .four div{
	background:#d35124;
}
.legend .five div{
	background:#a93fc0;
}
.legend .six div{
	background:#5d0b0b;
}
/*机动车*/
.vehicle{
    overflow: hidden;
}
.vehicle-table .tab-body td{
	padding-left: 5%;
}
/*污染源-污染源*/
.pollsource{
	overflow: hidden;
}
.pollsource .condition .radio p{
	margin-right: 20px;
}
.legend-pollsource1 ul li p{
	width: 50px;
}
.legend-pollsource1 .one div{
	background: #009f00;
}
.legend-pollsource1 .two div{
	background: #ea371a;
}
.legend-pollsource2 ul li p{
	width: auto;
}
.legend-pollsource2 .one div{
	background: #cccc00;
}
.legend-pollsource2 .two div{
	background: #cc6666;
}
.legend-pollsource2 .three div{
	background: #800080;
}
.pollsource-alert{
	margin-top: -100px;
	margin-left: -175px;
	width: 350px;
}
.pollsource-alert .alert-main .info li{
	float: none;
	width: auto;
	padding: 6px 5%;
	overflow: hidden;
}
/*污染源-两牛一猪*/
.animal{
	overflow: hidden;
}
.legend-animal{
	
}
.legend-animal .one div{
	background: #f38042;
}
.legend-animal .two div{
	background:#00c8c2;
}
.legend-animal .three div{
	background:#f8bc9d;
}
/*生态-自然保护区*/
.ecology{
	overflow: hidden;
}
.ecologyRedThread-table{
	margin: 20px;
}
.ecologyRedThread-table .tab-head{
	border-top: 1px solid #efefef;
}
.ecologyRedThread-table .tab-head,.ecologyRedThread-table .tab-body{
	border-right: 1px solid #efefef;
}
.ecologyRedThread-table .tab-head td,.ecologyRedThread-table .tab-body td{
	border-left: 1px solid #efefef;
	border-bottom: 1px solid #efefef;
	text-align: center;
	line-height: 40px;
	height: 40px;
}
/*水环境*/
.surfacewater{
	overflow: hidden;
}
.surfacewater .condition .select{
	margin-top: 20px;
}
.surfacewater .condition .select .selectdiv{
	width: 395px;
}
.legend-water ul li p{
	width: 30px;
}
.legend-water .one div{
	background: #ace1dd;
}
.legend-water .two div{
	background:#00bbdd;
}
.legend-water .three div{
	background:#00e225;
}
.legend-water .four div{
	background:#ffcf00;
}
.legend-water .five div{
	background:#ff9960;
}
.legend-water .six div{
	background:#ff2c00;
}
/*机动车弹框*/
.vehicle-alert{
	margin-top: -100px;
	margin-top: -100px;
	margin-left: -200px;
	width: 400px;
}
.vehicle-alert .alert-main .info li{
	padding: 6px 0 6px 5%;
}
/*趋势分析*/
.trend{
	
}
.trend .condition .radio-poll p{
    margin-top: 20px;
}
.trend .condition{
	padding-top: 0;
}
.trend-table{
	padding: 0;
}
/*变化分析*/
.change{
	
}
.change .condition{
	position: relative;
	z-index: 2;
}
.change .condition .select .selectdiv{
	margin-right: 10px;
}
.change .condition .radio{
	position: relative;
}
.change-detail{
	position: absolute;
	right: 0;
	top: 0;
	font-size: 14px;
	color: #464646;
}
.change-detail:hover{
	color: #549f57;
}
/*空气监测*/
.monitor .condition .time{
	margin-top: 20px;
}
.monitor .condition .select .selectdiv{
	margin-right: 10px;
}
/*空气概况*/
.atmosphere .atmosphereList{
	overflow: hidden;
	border-bottom: 1px solid #e6e7ea;
}
.atmosphere .atmosphereList dl{
	overflow: hidden;
	float: left;
	width: 33%;
}
.atmosphere .atmosphereList dl dt{
	height: 22px;
	line-height: 22px;
	width: 47px;
	text-align: center;
	color: #fff;
	font-size: 14px;
	border-radius: 2px;
	background: #ddd;
	margin: 0 auto;
}
.atmosphere .atmosphereList dl dt.numnone{
	background: #ddd;
}
.atmosphere .atmosphereList dl dt.one{
	background: #6dca70;
}
.atmosphere .atmosphereList dl dt.two{
	background:#f1c93e;
}
.atmosphere .atmosphereList dl dt.three{
	background:#f1933e;
}
.atmosphere .atmosphereList dl dt.four{
	background:#d35124;
}
.atmosphere .atmosphereList dl dt.five{
	background:#a93fc0;
}
.atmosphere .atmosphereList dl dt.six{
	background:#5d0b0b;
}
.atmosphere .atmosphereList dl dd{
	text-align: center;
	margin: 10px 0 13px;
}
.atmosphere .atmosphereList dl dd span{
	line-height: 12px;
	font-size: 12px;
	color: #868686;
}

.atmosphere .pollvaluediv{
   margin: 0 17px;
   border-bottom: 1px solid #efefef;
   overflow: hidden;
   position: relative;
}
.atmosphere .pollvaluediv .pollvalue{
    position: relative;
    width: 136px;
    height: 127px;
    margin-left: 64px;
    overflow: hidden;
}
.atmosphere .pollvaluediv .pollvalue p{
    width: 100%;
    text-align: center;
}
.atmosphere .pollvaluediv .pollvalue .zhi{
    font-size: 14px;
    color: #101010; 
    margin-top: 26px;  
}
.atmosphere .pollvaluediv .pollvalue .num{
    font-size: 38px;
    line-height: 44px;
    color: #000;
    font-weight: 800;
}
.atmosphere .pollvaluediv .pollvalue .li{
    font-size: 16px;    
}
.atmosphere .pollvaluediv .pollvalue.numnone{
	background: url(../img/images/poll-0.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.numnone .li{
	color: #ddd;
}
.atmosphere .pollvaluediv .pollvalue.one{
	background: url(../img/images/poll-1.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.one .li{
	color: #6dca70;
}
.atmosphere .pollvaluediv .pollvalue.two{
	background: url(../img/images/poll-2.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.two .li{
	color: #f1c93e;
}
.atmosphere .pollvaluediv .pollvalue.three{
	background: url(../img/images/poll-3.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.three .li{
	color: #f1933e;
}
.atmosphere .pollvaluediv .pollvalue.four{
	background: url(../img/images/poll-4.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.four .li{
	color: #d35124;
}
.atmosphere .pollvaluediv .pollvalue.five{
	background: url(../img/images/poll-5.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.five .li{
	color: #a93fc0;
}
.atmosphere .pollvaluediv .pollvalue.six{
	background: url(../img/images/poll-6.png) no-repeat;
}
.atmosphere .pollvaluediv .pollvalue.six .li{
	color: #5d0b0b;
}
.atmosphere .pollvaluediv p.p{
    margin: 27px 0;
    font-size: 14px;
    color: #686868;
}
.atmosphere .pollvaluediv span{
    font-size: 14px;
    color: #161616;
}
.atmosphere .pollvaluediv ul.ranking{
	position: absolute;
    right: 0;
    top: -16px;
}
.atmosphere .pollvaluediv ul.ranking li{
	float: left;
	line-height: 50px;
}
.atmosphere .pollvaluediv ul.ranking .name{
	width: 90px;
}
.atmosphere .pollvaluediv ul.ranking .name p{
	font-size: 14px;
    color: #888888;
    text-align: right;
    font-weight: 600;
}
.atmosphere .pollvaluediv ul.ranking .val p{
	font-size: 16px;
    color: #549f57;
    font-weight: 600;
    padding-left: 5px;
    text-align: right;
}
.atmosphere .pressureList{
    height: 87px;
    overflow: hidden;
}
.atmosphere .pressureList ul{
    margin-left: -76px;
    margin-top: 10px;
}
.atmosphere .pressureList ul li{
    list-style: none;
    float: left;
    margin-left: 93px;
    line-height: 35px;
    font-size: 14px;
    color: #686868;
    text-align: justify;
    position: relative;
    left: 21px;
    font-weight: 600;
}
.atmosphere .pressureList ul li img{
    position: absolute;
    top: 8px;
    left: -20px;
}
.atmosphere .pressureList ul li span{
    font-size: 14px;
    color: #161616;
    font-weight: 600;
}
/*第一种弹框样式*/
.alert-style .atmosphere-value{
	overflow: hidden;
}
.alert-style .atmosphere-value ul{
	overflow: hidden;
	margin: 0 16px 0px;
	border-bottom: 1px solid #efefef;
}
.alert-style .atmosphere-value ul .left{
	float: left;
	padding: 5px 0;
}
.alert-style .atmosphere-value ul .left dl{
	overflow: hidden;
}
.alert-style .atmosphere-value ul .left dt{
	float: left;
	text-align: center;
	color: #161616;
    font-weight: 800;
    font-size: 38px;
    line-height: 32px;
}
.alert-style .atmosphere-value ul .left dt span{
	display: block;
	font-size: 14px;
}
.alert-style .atmosphere-value ul .left dd{
	float: left;
	width: 74px;
    height: 42px;
    text-align: center;
    line-height: 42px;
    border-radius: 12px;
    font-size: 22px;
    color: #fff;
    font-weight: 600;
    background: #ddd;
    margin: 7px 0 0 5px;
}
.alert-style .atmosphere-value ul .right{
	float: right;
	padding: 5px 0;
}
.alert-style .atmosphere-value ul .right p{
	text-align: right;
    font-size: 14px;
    color: #676767;
    padding-bottom: 8px;
}
.alert-style .atmosphere-value ul .right p span{
	color: #161616;
}
.alert-style .alert-main .info{
	margin: 5px 0;
}
/*空气概况弹框*/
.atmosphere-alert {
	margin-top: -280px;
}
.atmosphere-alert .atmosphere-value ul .left dd.one{
	background: #6dca70;
}
.atmosphere-alert .atmosphere-value ul .left dd.two{
	background:#f1c93e;
}
.atmosphere-alert .atmosphere-value ul .left dd.three{
	background:#f1933e;
}
.atmosphere-alert .atmosphere-value ul .left dd.four{
	background:#d35124;
}
.atmosphere-alert .atmosphere-value ul .left dd.five{
	background:#a93fc0;
}
.atmosphere-alert .atmosphere-value ul .left dd.six{
	background:#5d0b0b;
}
.atmosphere-alert .condition{
	padding: 10px 0 0 0;
	border: none;
}
.atmosphere-alert .condition .radio-poll p{
	margin-right: 10px;
	margin-bottom: 10px;
}
.atmosphere-alert .echartsDiv{
	padding: 0 17px;
}
/*水环境弹框*/
.water-alert {
	margin-top: -280px;
}
.water-alert .condition{
	padding: 10px 0 10px;
}
.water-alert .echartsDiv{
	padding: 0 17px;
}
.water-alert .atmosphere-value ul .right{
	padding: 0 0 10px 0;
}
.water-alert .atmosphere-value ul .left{
	padding: 0 0 10px 0;
}
.water-alert .atmosphere-value ul .left dt{
	float: left;
	text-align: center;
    line-height: 61px;
    width: 61px;
    height: 61px;
    border-radius: 50%;
    font-size: 38px;
    color: #fff;
    background: #ddd;
    font-weight: normal;
}
.water-alert .atmosphere-value ul .left dd{
	float: left;
	width: 74px;
    height: 42px;
    line-height: 42px;
    font-weight: normal;
    background: none;
    margin: 7px 0 0 5px;
    font-size: 27px;
    color: #161616;
}
.water-alert .atmosphere-value ul .left dt.one{
	background: #50dddd;
}
.water-alert .atmosphere-value ul .left dt.two{
	background:#6dca70;
}
.water-alert .atmosphere-value ul .left dt.three{
	background:#5094dd;
}
.water-alert .atmosphere-value ul .left dt.four{
	background:#f1c93e;
}
.water-alert .atmosphere-value ul .left dt.five{
	background:#f1933e;
}
.water-alert .atmosphere-value ul .left dt.six{
	background:#d35124;
}
.water-alert .atmosphere-value ul .left dt.red{
	background: #e70303;
}
.water-alert .atmosphere-value ul .left dt.green{
	background: #549f57;
}
/*地图图例*/
.legend-main{
	position: absolute;
	top: 20px;
	right: 20px;
	z-index: 5;
}
.legend-main ul li{
	width: 55px;
    height: 55px;
    background: #fff;
	border-radius: 50%;
	box-shadow: 0px 0px 3px 1px #dbd3c8;
	cursor: pointer;
	margin-bottom: 31px;
}
.legend-main ul li.one{
	background: #fff url(../img/images/map/map_chuag.png) no-repeat center center;
}
.legend-main ul li.one:hover{
	background: #4da154 url(../img/images/map/map_chuagGreen.png) no-repeat center center;
}
.legend-main ul li.two{
	background: #fff url(../img/images/map/map_height.png) no-repeat center center;
}
.legend-main ul li.two:hover{
	background: #4da154 url(../img/images/map/map_heightGreen.png) no-repeat center center;
}
.legend-main ul li.three{
	background: #fff url(../img/images/map/map_position.png) no-repeat center center;
}
.legend-main ul li.three:hover{
	background: #4da154 url(../img/images/map/map_positionGreen.png) no-repeat center center;
}
.legend-main ul li.four{
	background: #fff url(../img/images/map/map_delete.png) no-repeat center center;
}
.legend-main ul li.four:hover{
	background: #4da154 url(../img/images/map/map_deleteGreen.png) no-repeat center center;
}
.legend-main ul li.five{
	background: #fff url(../img/images/map/map_house.png) no-repeat center center;
}
.legend-main ul li.five:hover{
	background: #4da154 url(../img/images/map/map_houseGreen.png) no-repeat center center;
}
.page{
	overflow: hidden;
	padding: 17px;
}
.page a{
	display: block;
	float: left;
	width: 31px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	border: 1px solid #efefef;
	border-radius: 2px;
	margin-left: 10px;
	font-size: 14px;
	color: #666666;
}
.page a.first,.page a.last{
	width: 50px;
}
.page a.cur{
	background: #549f57;
	color: #fff;
	border-color: #549f57;
}
.page p{
	float: right;
	color: #999999;
	font-size: 14px;
	line-height: 30px;
	padding-right: 10px;
}

