# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=喈瘉喈ㄠ瘝喈む瘓喈� 喈畷喁嵿畷喈瘝
previous_label=喈瘉喈ㄠ瘝喈む瘓喈喁�
next.title=喈呧疅喁佮喁嵿 喈畷喁嵿畷喈瘝
next_label=喈呧疅喁佮喁嵿喁�

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} 喈囙喁�
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages={{pagesCount}}) 喈囙喁� ({{pageNumber}}

zoom_out.title=喈氞喈编喈む喈曕瘝喈曕瘉
zoom_out_label=喈氞喈编喈む喈曕瘝喈曕瘉
zoom_in.title=喈瘑喈班喈む喈曕瘝喈曕瘉
zoom_in_label=喈瘑喈班喈む喈曕瘝喈曕瘉
zoom.title=喈瘑喈班喈む喈曕瘝喈曕瘉
presentation_mode.title=喈掂喈赤畷喁嵿畷喈曕喈熰瘝喈氞 喈喈┼瘝喈瘉喈编瘓喈曕瘝喈曕瘉 喈喈编瘉
presentation_mode_label=喈掂喈赤畷喁嵿畷喈曕喈熰瘝喈氞 喈喈┼瘝喈瘉喈编瘓
open_file.title=喈曕瘚喈瘝喈喈┼瘓 喈む喈�
open_file_label=喈む喈�
print.title=喈呧畾喁嵿畾喈苦疅喁�
print_label=喈呧畾喁嵿畾喈苦疅喁�
download.title=喈喈苦喈苦喈曕瘝喈曕瘉
download_label=喈喈苦喈苦喈曕瘝喈曕瘉
bookmark.title=喈む喁嵿喁嬥喁堗 喈曕喈熰瘝喈氞 (喈瘉喈む喈� 喈氞喈赤喈む瘝喈む喈编瘝喈曕瘉 喈ㄠ畷喈侧瘑喈熰瘉 喈呧喁嵿喈む瘉 喈瘉喈む喈� 喈氞喈赤喈む瘝喈む喈侧瘝 喈む喈�)
bookmark_label=喈む喁嵿喁嬥喁堗 喈曕喈熰瘝喈氞

# Secondary toolbar and context menu
tools.title=喈曕喁佮喈苦畷喈赤瘝
tools_label=喈曕喁佮喈苦畷喈赤瘝
first_page.title=喈瘉喈む喁� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
first_page.label=喈瘉喈む喁� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
first_page_label=喈瘉喈む喁� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
last_page.title=喈曕疅喁堗畾喈� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
last_page.label=喈曕疅喁堗畾喈� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
last_page_label=喈曕疅喁堗畾喈� 喈畷喁嵿畷喈む瘝喈む喈编瘝喈曕瘉 喈氞瘑喈侧瘝喈侧喁佮喁�
page_rotate_cw.title=喈掂喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�
page_rotate_cw.label=喈掂喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�
page_rotate_cw_label=喈掂喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�
page_rotate_ccw.title=喈囙疅喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�
page_rotate_ccw.label=喈囙疅喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�
page_rotate_ccw_label=喈囙疅喈炧瘝喈氞瘉喈脆喈喈� 喈氞瘉喈脆喁嵿喁�

hand_tool_enable.title=喈曕瘓 喈曕喁佮喈苦喁� 喈氞瘑喈喈距畷喁嵿畷喁�
hand_tool_enable_label=喈曕瘓 喈曕喁佮喈苦喁� 喈氞瘑喈喈距畷喁嵿畷喁�
hand_tool_disable.title=喈曕瘓 喈曕喁佮喈苦喁� 喈瘉喈熰畷喁嵿畷喁�
hand_tool_disable_label=喈曕瘓 喈曕喁佮喈苦喁� 喈瘉喈熰畷喁嵿畷喁�

# Document properties dialog box
document_properties.title=喈嗋喈� 喈喁嵿喁佮畷喈赤瘝...
document_properties_label=喈嗋喈� 喈喁嵿喁佮畷喈赤瘝...
document_properties_file_name=喈曕瘚喈瘝喈瘉 喈瘑喈喁�:
document_properties_file_size=喈曕瘚喈瘝喈喈┼瘝 喈呧喈掂瘉:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} 喈曕喈瘓 ({{size_b}} 喈瘓喈熰瘝喈熰瘉喈曕喁�)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} 喈瘑喈瘓 ({{size_b}} 喈瘓喈熰瘝喈熰瘉喈曕喁�)
document_properties_title=喈む喁堗喁嵿喁�:
document_properties_author=喈庎喁佮喈苦喈掂喁�
document_properties_subject=喈瘖喈班瘉喈赤瘝:
document_properties_keywords=喈瘉喈曕瘝喈曕喈� 喈掂喈班瘝喈む瘝喈む瘓喈曕喁�:
document_properties_creation_date=喈疅喁堗喁嵿 喈む瘒喈む :
document_properties_modification_date=喈む喈班瘉喈む瘝喈む喈� 喈む瘒喈む:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=喈夃喁佮喈距畷喁嵿畷喁佮喈掂喁�:
document_properties_producer=喈喈熰喈庎畠喈瘝 喈む喈距喈苦喁嵿喈距喈班瘝:
document_properties_version=PDF 喈喈苦喁嵿喁�:
document_properties_page_count=喈畷喁嵿畷 喈庎喁嵿喈苦畷喁嵿畷喁�:
document_properties_close=喈瘋喈熰瘉喈�

print_progress_message=喈呧畾喁嵿畾喈苦疅喁佮喈む喁嵿畷喈距 喈嗋喈｀喁� 喈む喈距喈距畷喈苦喈む瘉...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=喈畷喁嵿畷喈瘝 喈疅喁嵿疅喈苦喁� 喈ㄠ喈侧瘓喈喈编瘝喈编瘉
toggle_sidebar_label=喈畷喁嵿畷喈瘝 喈疅喁嵿疅喈苦喁� 喈ㄠ喈侧瘓喈喈编瘝喈编瘉
document_outline.title=喈嗋喈� 喈呧疅喈曕瘝喈曕喁嵿喁堗畷喁� 喈曕喈熰瘝喈熰瘉 (喈囙喁佮喁佮喁堗畾喁� 喈氞瘖喈熰瘉喈曕瘝喈曕 喈呧喁堗喁嵿喁� 喈夃喁佮喁嵿喈苦疅喈苦畷喈赤瘓喈瘉喈瘝 喈掂喈班/喈氞瘒喈班瘝)
document_outline_label=喈嗋喈� 喈掂瘑喈赤喈掂喁�
attachments.title=喈囙喁堗喁嵿喁佮畷喈赤瘓 喈曕喈｀瘝喈
attachments_label=喈囙喁堗喁嵿喁佮畷喈赤瘝
thumbs.title=喈氞喈编瘉喈疅喈權瘝喈曕喁堗畷喁� 喈曕喈｀瘝喈
thumbs_label=喈氞喈编瘉喈疅喈權瘝喈曕喁�
findbar.title=喈嗋喈｀喁嵿喈苦喁� 喈曕喁嵿疅喈编
findbar_label=喈曕喁嵿疅喁佮喈苦疅喈�

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=喈畷喁嵿畷喈瘝 {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=喈畷喁嵿畷喈む瘝喈む喈┼瘝 喈氞喈编瘉喈疅喈瘝 {{page}}

# Find panel button title and messages
find_label=喈曕喁嵿疅喈编:
find_previous.title=喈囙喁嵿 喈氞瘖喈编瘝喈编瘖喈熰喈苦喁� 喈瘉喈ㄠ瘝喈む瘓喈� 喈ㄠ喈曕喁嵿喁� 喈む瘒喈熰瘉
find_previous_label=喈瘉喈ㄠ瘝喈む瘓喈喁�
find_next.title=喈囙喁嵿 喈氞瘖喈编瘝喈编瘖喈熰喈苦喁� 喈呧疅喁佮喁嵿 喈ㄠ喈曕喁嵿喁� 喈む瘒喈熰瘉
find_next_label=喈呧疅喁佮喁嵿喁�
find_highlight=喈呧喁堗喁嵿喁堗喁佮喁� 喈む喈苦喁嵿喈熰瘉喈む瘝喈む瘉
find_match_case_label=喈瘒喈班瘑喈脆瘉喈む瘝喈む喈曕瘝喈曕喁嵿喁� 喈夃喈班瘝
find_reached_top=喈嗋喈｀喁嵿喈苦喁� 喈瘒喈侧瘝 喈畷喁佮喈苦喁� 喈呧疅喁堗喁嵿喈む瘉, 喈呧疅喈苦喁嵿喈曕瘝喈曕喁嵿喈苦喈苦喁佮喁嵿喁� 喈む瘖喈熰喁嵿喁嵿喈む瘉
find_reached_bottom=喈嗋喈｀喁嵿喈苦喁� 喈瘉喈熰喈掂瘓 喈呧疅喁堗喁嵿喈む瘉, 喈瘒喈侧喈班瘉喈ㄠ瘝喈む瘉 喈む瘖喈熰喁嵿喁嵿喈む瘉
find_not_found=喈氞瘖喈编瘝喈编瘖喈熰喁� 喈曕喈｀喈苦喁嵿喁�

# Error panel labels
error_more_info=喈曕瘋喈熰瘉喈む喁� 喈む畷喈掂喁�
error_less_info=喈曕瘉喈编瘓喈ㄠ瘝喈� 喈む畷喈掂喁�
error_close=喈瘋喈熰瘉喈�
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=喈氞瘑喈瘝喈む: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=喈膏瘝喈熰瘒喈曕瘝: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=喈曕瘚喈瘝喈瘉: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=喈掂喈�: {{line}}
rendering_error=喈囙喁嵿喈瘝 喈畷喁嵿畷喈む瘝喈む瘓 喈曕喈熰瘝喈氞喈瘝喈疅喁佮喁嵿喁佮喁� 喈瘚喈む瘉 喈掄喁� 喈喈脆瘓 喈忇喁嵿喈熰瘝喈熰喁�.

# Predefined zoom values
page_scale_width=喈畷喁嵿畷 喈呧畷喈侧喁�
page_scale_fit=喈畷喁嵿畷喈瘝 喈瘖喈班瘉喈む瘝喈む喁�
page_scale_auto=喈む喈┼喈畷喁嵿畷 喈瘑喈班喈む喈曕瘝喈曕喁�
page_scale_actual=喈夃喁嵿喁堗喈距 喈呧喈掂瘉
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=喈喈脆瘓
loading_error=PDF 喈� 喈忇喁嵿喁佮喁� 喈瘚喈む瘉 喈掄喁� 喈喈脆瘓 喈忇喁嵿喈熰瘝喈熰喁�.
invalid_file_error=喈氞瘑喈侧瘝喈侧瘉喈疅喈苦喈距畷喈距 喈呧喁嵿喈む瘉 喈氞喈む瘓喈ㄠ瘝喈� PDF 喈曕瘚喈瘝喈瘉.
missing_file_error=PDF 喈曕瘚喈瘝喈瘉 喈曕喈｀喈苦喁嵿喁�.
unexpected_response_error=喈氞瘒喈掂畷喈┼瘝 喈喈苦喁� 喈庎喈苦喁嵿喈距喈む喁�.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 喈掂喈赤畷喁嵿畷喈瘝]
password_label=喈囙喁嵿 PDF 喈曕瘒喈距喁嵿喁� 喈む喈编畷喁嵿畷 喈曕疅喈掂瘉喈氞瘝喈氞瘑喈距喁嵿喁� 喈夃喁嵿喈苦疅喈掂瘉喈瘝.
password_invalid=喈氞瘑喈侧瘝喈侧瘉喈疅喈苦喈距畷喈距 喈曕疅喈掂瘉喈氞瘝喈氞瘖喈侧瘝, 喈む喁� 喈氞瘑喈瘝喈む瘉 喈瘈喈｀瘝喈熰瘉喈瘝 喈瘉喈喁嵿畾喈� 喈氞瘑喈瘝喈�.
password_ok=喈氞喈�

printing_not_supported=喈庎畾喁嵿畾喈班喈曕瘝喈曕瘓: 喈囙喁嵿 喈夃喈距喈� 喈呧畾喁嵿畾喈苦疅喁佮喈侧瘓 喈瘉喈脆瘉喈瘓喈喈� 喈嗋喈班喈曕瘝喈曕喈苦喁嵿喁�.
printing_not_ready=喈庎畾喁嵿畾喈班喈曕瘝喈曕瘓: PDF 喈呧畾喁嵿畾喈苦疅 喈瘉喈脆瘉喈掂喁佮喈距畷 喈忇喁嵿喈瘝喈疅喈掂喈侧瘝喈侧瘓.
web_fonts_disabled=喈掂喁� 喈庎喁佮喁嵿喁佮喁佮畷喁嵿畷喈赤瘝 喈瘉喈熰畷喁嵿畷喈瘝喈疅喁嵿疅喁佮喁嵿喈�: 喈夃疅喁嵿喁娻喈苦畷喁嵿畷喈瘝喈疅喁嵿疅 PDF 喈庎喁佮喁嵿喁佮喁佮畷喁嵿畷喈赤瘓喈瘝 喈喈┼瘝喈疅喁佮喁嵿 喈瘉喈熰喈喈苦喁嵿喁�.
document_colors_not_allowed=PDF 喈嗋喈｀畽喁嵿畷喈赤瘉喈曕瘝喈曕瘉喈氞瘝 喈氞瘖喈ㄠ瘝喈� 喈ㄠ喈编畽喁嵿畷喈赤瘓喈瘝 喈喈┼瘝喈疅喁佮喁嵿 喈呧喁佮喈む喈喈侧瘝喈侧瘓: 喈夃喈距喈苦喈苦喁� "喈畷喁嵿畷喈權瘝喈曕喁� 喈む畽喁嵿畷喈赤瘝 喈氞瘖喈ㄠ瘝喈� 喈ㄠ喈编畽喁嵿畷喈赤瘓喈む瘝 喈む瘒喈班瘝喈掂瘉 喈氞瘑喈瘝喈む瘉喈曕瘖喈赤瘝喈� 喈呧喁佮喈む" 喈庎喁嵿喁佮喁� 喈掂喈班瘉喈瘝喈喁� 喈瘉喈熰畷喁嵿畷喈瘝喈疅喁嵿疅喁佮喁嵿喈む瘉.
