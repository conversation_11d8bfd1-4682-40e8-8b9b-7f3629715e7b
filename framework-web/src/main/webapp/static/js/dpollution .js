var dpollution = new Object({
	init : function(){
		this.type = "污染源";
		this.pollareas = "全市";
		this.pollutantType = "全部";
		this.polltime = "2018-04-15";//自动监控下的时间
		this.pollexceeding = "全部";//自动监控下的超标
		this.pollrisk = "全部";//风险源下的风险类型
		this.animalareas = "全市";
		this.animalType = "全部";
		this.bindEvent();//绑定事件
	},
	bindEvent : function(){
		var _this = this;
		//污染源
		$('.left-list-wrap dl.cur').click(function(){
			console.log('污染源')
			_this.getJson();
		})
		//污染源、两牛一猪、缓冲切换
		$('.list-main').find('.list-main-tabname p').click(function(){
			var index=$(this).index();
			$(this).parent('.list-main-tabname').next('.list-main-tab-wrap').find('.list-main-tab').eq(index).addClass('cur').siblings('.list-main-tab').removeClass('cur');
			_this.type = $(this).html();
			_this.getJson();
		})
		//污染源下的城市选择
		$('.pollsource .areas p span').click(function(){
			_this.pollareas = $(this).parent('p').text();
			_this.getJson();
		})
		//污染源下的全部、自动监控、风险源切换
		$('.pollsource .condition .radio').eq(0).find('p').click(function(){
			_this.pollutantType = $(this).html();
			_this.getJson();
		})
		//污染源下的自动监控下的超标
		$('.pollsource .condition .radio').eq(1).find('p').click(function(){
			_this.pollexceeding = $(this).html();
			_this.getJson();
		})
		//污染源下的风险源下的风险类型
		$('.pollsource .condition .radio').eq(2).find('p').click(function(){
			_this.pollrisk = $(this).html();
			_this.getJson();
		})
		//两牛一猪的城市选择
		$('.animal .areas p span').click(function(){
			_this.animalareas = $(this).parent('p').text();
			_this.getJson();
		})
		//两牛一猪的城市选择
		$('.animal .condition .radio').eq(0).find('p').click(function(){
			_this.animalType = $(this).html();
			_this.getJson();
		})
	},
	getJson : function(){
		var _this = this;
		publicObj.removeAllLayer();//先清楚所有图层再去添加新的图层
		if(_this.type == "污染源"){
			console.log(_this.pollareas)
			console.log(_this.pollutantType)
			_this.pollTabTitle();
			if(_this.pollutantType == '全部'){
				
			}else if(_this.pollutantType == '自动监控'){
				console.log(_this.polltime)
				console.log(_this.pollexceeding)
			}else if(_this.pollutantType == '风险源'){
				console.log(_this.pollrisk)
			}else if(_this.pollutantType == '集中式污染治理设施'){
				
			}
			$.ajax({
				type: "get",
				cache:false,
				crossDomain: true == !(document.all),
				url:'./json/e.json',
				success:function(json){
					_this.pollGis(json)
				},
				error: function(){
					
				}
			});
		}else if(_this.type == "两牛一猪"){
			console.log(_this.animalareas)
			console.log(_this.animalType)
			$('.legend').hide();//图例隐藏
			$('.legend-animal').show();//两牛一猪的图例显示
			$.ajax({
				type: "get",
				cache:false,
				crossDomain: true == !(document.all),
				url:'./json/e.json',
				success:function(json){
					_this.animalGis(json)
				},
				error: function(){
					
				}
			});
		}
		
	},
	pollTabTitle : function(){
		var _this = this;
		$('.legend').hide();
		$('.pollsource .condition .none').hide();
		if(_this.pollutantType == '全部'){
			$('.pollsource-table .tab-head td').eq(2).html('控制级别');
		}else if(_this.pollutantType == '自动监控'){
			$('.pollsource .condition .none').eq(0).show();
			$('.pollsource-table .tab-head td').eq(2).html('状态');
			$('.legend-pollsource1').show();
		}else if(_this.pollutantType == '风险源'){
			$('.pollsource .condition .none').eq(1).show();
			$('.pollsource-table .tab-head td').eq(2).html('风险等级');
			$('.legend-pollsource2').show();
		}else if(_this.pollutantType == '集中式污染治理设施'){
			$('.pollsource-table .tab-head td').eq(2).html('控制级别');
		}
	},
	pollGis : function(json){
		var _this = this;
		var pointjson = [];
		for(var i = 0 ; i < json.length ; i++){
			var imgSrc = '/../qiqiHTML/images/point/5.png';
			if(_this.pollutantType == '自动监控'){
				if(json[i].AQI < 100){
					imgSrc = '/../qiqiHTML/images/point/8.png';
				}else{
					imgSrc = '/../qiqiHTML/images/point/9.png';
				}
			}else if(_this.pollutantType == '风险源'){
				imgSrc = '/../qiqiHTML/images/point/12.png';
				if(json[i].AQI < 100){
					imgSrc = '/../qiqiHTML/images/point/10.png';
				}else if(json[i].AQI < 200){
					imgSrc = '/../qiqiHTML/images/point/11.png';
				}
			}
            var symbogif = {
                "type": "esriPMS",
                "url": imgSrc, //图片位置   真正点的坐标在图片中心点，如果要将图片上移，yoffset为正，右移 xoffset为正
                "width": 43, //当前图片宽度
                "height": 43,//当前图片高度
                "xoffset": 0,
                "yoffset": 0
            };
            var obj = {
                "geometry": {
                    "x": json[i].LONGITUDE, 
                    "y": json[i].LATITUDE,  //经纬度
                    "spatialReference": {"wkid": 4326}
                },
                "attributes": {
                    "Name": json[i].REGIONNAME
                },
                "symbol": symbogif
            }
            pointjson.push(obj)
        }
		 mymap.addGraphic("dpollLayer", null, pointjson, {mouseEnter: showinfowindow,mouseOut: removewindow,click:clickEvent});
		 function showinfowindow(event){
            var n = event.graphic.attributes.Name
            var content = "站点名称：" + n;
            mymap.showPopupWindow("<span style='color:yellow'>详细信息</span>", content, event.graphic.geometry.x, event.graphic.geometry.y, null);
        }
        function removewindow(){
            mymap.removewPopupWindow()
        }
        function clickEvent(event){
        	$('.pollsource-alert').show();
        }
	},
	animalGis : function(json){
		var pointjson = [];
		for(var i = 0 ; i < json.length ; i++){
			var imgSrc = '/../qiqiHTML/images/point/6.png';
			if(json[i].AQI < 100){
				imgSrc = '/../qiqiHTML/images/point/2.png';
			}else if(json[i].AQI < 200){
				imgSrc = '/../qiqiHTML/images/point/3.png';
			}
            var symbogif = {
                "type": "esriPMS",
                "url": imgSrc, //图片位置   真正点的坐标在图片中心点，如果要将图片上移，yoffset为正，右移 xoffset为正
                "width": 43, //当前图片宽度
                "height": 43,//当前图片高度
                "xoffset": 0,
                "yoffset": 0
            };
            var obj = {
                "geometry": {
                    "x": json[i].LONGITUDE, 
                    "y": json[i].LATITUDE,  //经纬度
                    "spatialReference": {"wkid": 4326}
                },
                "attributes": {
                    "Name": json[i].REGIONNAME
                },
                "symbol": symbogif
            }
            pointjson.push(obj)
        }
		 mymap.addGraphic("dpollLayer", null, pointjson, {mouseEnter: showinfowindow,mouseOut: removewindow});
		 function showinfowindow(event){
            var n = event.graphic.attributes.Name
            var content = "站点名称：" + n;
            mymap.showPopupWindow("<span style='color:yellow'>详细信息</span>", content, event.graphic.geometry.x, event.graphic.geometry.y, null);
        }
        function removewindow(){
            mymap.removewPopupWindow()
        }
	}
});
dpollution.init();