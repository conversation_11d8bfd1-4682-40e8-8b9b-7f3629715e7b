

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>福建环境监察全过程业务智能办理系统</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<meta name="description" content="">
<meta name="author" content="">

<!-- Bootstrap core CSS -->
<link rel="stylesheet" type="text/css" href="../libs/bootstrap/3.3.4/css/bootstrap.css">

<!-- Bootstrap Core CSS -->
<link rel="stylesheet" type="text/css" href="../jquery/bootstrap-3.3.4.css">

<!-- Font Awesome -->
<link rel="stylesheet" type="text/css" href="../font-awesome/4.7.0/css/font-awesome.min.css">

<!-- 可删除TAB标签 -->
<link rel="stylesheet" href="../css/bootstrap.addtabs.css" type="text/css" media="screen" />

<!-- <PERSON> -->
<link href="../css/morris.css" rel="stylesheet" />

<!-- Datepicker -->
<link href="../css/datepicker.css" rel="stylesheet" />

<!-- Animate -->
<link href="../css/animate.min.css" rel="stylesheet">

<!-- Owl Carousel -->
<link href="../css/owl.carousel.min.css" rel="stylesheet">
<link href="../css/owl.theme.default.min.css" rel="stylesheet">

<!-- Simplify -->
<link href="../css/simplify.min.css" rel="stylesheet">

<!-- 上传附件 CSS -->
<link href="../css/fileinput.css" media="all" rel="stylesheet" type="text/css" />


<link href="../css/style.css" rel="stylesheet">

</head>

<body class="overflow-hidden">
	<div class="wrapper preload">
		<header class="top-nav">
			<div class="top-nav-inner">
				<!--手机端呼出左侧菜单-->
				<div class="nav-header">
					<button type="button"
						class="navbar-toggle pull-left sidebar-toggle"
						id="sidebarToggleSM">
						<span class="icon-bar"></span> <span class="icon-

bar"></span> <span
							class="icon-bar"></span>
					</button>
					<!--手机端个人信息-->
					<ul class="nav-notification pull-right">
						<li><a href="#" class="dropdown-toggle"
							data-toggle="dropdown"><i class="fa fa-cog fa-

lg"></i></a> <span
							class="badge badge-danger bounceIn">1</span>
							<ul class="dropdown-menu dropdown-sm pull-right 

user-dropdown">
								<li class="user-avatar"><img 

src="img/mep.png" alt=""
									class="img-circle">
									<div class="user-content">
										<h5 class="no-m-bottom">管

理员</h5>
										<div class="m-top-xs">
											<a 

href="profile.html" class="m-right-sm">个人空间</a> <a
												

href="signin.html">退出系统</a>
										</div>
									</div></li>
								<li><a href="inbox.html"> 我的邮件 <span
										class="badge badge-danger 

bounceIn animation-delay2 pull-right">1</span>
								</a></li>
								<li><a href="#"> 站内信息 <span
										class="badge badge-purple 

bounceIn animation-delay3 pull-right">2</span>
								</a></li>
								<li class="divider"></li>
								<li><a href="#">设置</a></li>
							</ul></li>
					</ul>
					<!--logo-->
					<a href="../home.html"> <span><img src="../img/logo.png"
							style="margin: 5px 0 0 0;" /></span>
					</a>
				</div>
				<!--./手机端呼出左侧菜单-->

				<!-- 框架top -->
				<div class="nav-container">
					<button type="button"
						class="navbar-toggle pull-left sidebar-toggle"
						id="sidebarToggleLG">
						<span class="icon-bar"></span> <span class="icon-

bar"></span> <span
							class="icon-bar"></span>
					</button>
					<ul class="nav-notification">
						<li class="search-list">
							<div class="search-input-wrapper">
								<div class="search-input">
									<input type="text" class="form-

control input-sm inline-block">
									<a href="#" class="input-icon 

text-normal"><i
										class="ion-ios7-search-

strong"></i></a>
								</div>
							</div>
						</li>
					</ul>
					<div class="pull-right m-right-sm">
						<!--用户信息-->
						<div class="user-block hidden-xs">
							<a href="#" id="userToggle" data-

toggle="dropdown"> <i
								class="fa fa-user-circle fa-lg"></i>
								<div class="user-detail inline-block">
									管理员您好！ <i class="fa fa-

angle-down"></i>
								</div>
							</a>
							<div class="panel border dropdown-menu user-

panel">
								<div class="panel-body paddingTB-sm">
									<ul>
										<li><a 

href="profile.html"> <i
												class="fa 

fa-edit fa-lg"></i><span class="m-left-xs">个人空间</span>
												<span 

class="badge badge-danger bounceIn animation-delay3">2</span>
										</a></li>
										<li><a href="signin.html"> 

<i
												class="fa 

fa-power-off fa-lg"></i><span class="m-left-xs">退出系统</span>
										</a></li>
									</ul>
								</div>
							</div>
						</div>
						<ul class="nav-notification">
							<!--站内信息-->
							<li><a href="#" data-toggle="dropdown"><i
									class="fa fa-envelope fa-

lg"></i></a> <span
								class="badge badge-purple bounceIn 

animation-delay5 active">2</span>
								<ul class="dropdown-menu message pull-

right">
									<li><a>你有4个新的未读邮件

</a></li>
									<li><a class="clearfix" href="#"> 

<img
											src="img/mep.png" 

alt="User Avatar">
											<div 

class="detail">
												<strong>环

保厅</strong>
												<p 

class="no-margin">关于待办任务的执行流程与规则...</p>
												<small 

class="text-muted"><i
													

class="fa fa-check text-success"></i> 2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> 

<img
											src="img/mep.png" 

alt="User Avatar">
											<div 

class="detail">
												<strong>环

保厅</strong>
												<p 

class="no-margin">关于待办任务的执行流程与规则...</p>
												<small 

class="text-muted"><i
													

class="fa fa-check text-success"></i> 2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> 

<img
											src="img/mep.png" 

alt="User Avatar">
											<div class="detail 

m-left-sm">
												<strong>环

保厅</strong>
												<p 

class="no-margin">关于待办任务的执行流程与规则...</p>
												<small 

class="text-muted"><i class="fa fa-reply"></i>
													

2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> 

<img
											src="img/mep.png" 

alt="User Avatar">
											<div 

class="detail">
												<strong>环

保厅</strong>
												<p 

class="no-margin">关于待办任务的执行流程与规则...</p>
												<small 

class="text-muted"><i class="fa fa-reply"></i>
													

2017-3-1</small>
											</div>
									</a></li>
									<li><a href="#">查看所有消息

</a></li>
								</ul></li>
							<!--信息提醒-->
							<li><a href="#" data-toggle="dropdown"><i
									class="fa fa-bell fa-lg"></i></a> 

<span
								class="badge badge-info bounceIn 

animation-delay6 active">4</span>
								<ul class="dropdown-menu notification 

dropdown-3 pull-right">
									<li><a href="#">你有5个新的通知

</a></li>
									<li><a href="#"> <span
											

class="notification-icon bg-warning"> <i
												class="fa 

fa-warning"></i>
										</span> <span class="m-

left-xs">#2服务器没有响应。</span> <span
											class="time text-

muted">2分钟前</span>
									</a></li>
									<li><a href="#"> <span
											

class="notification-icon bg-success"> <i
												class="fa 

fa-plus"></i>
										</span> <span class="m-

left-xs">新用户注册。</span> <span
											class="time text-

muted">30分钟前</span>
									</a></li>
									<li><a href="#"> <span
											

class="notification-icon bg-danger"> <i
												class="fa 

fa-bolt"></i>
										</span> <span class="m-

left-xs">应用程序错误。</span> <span
											class="time text-

muted">1小时前</span>
									</a></li>
									<li><a href="#"> <span
											

class="notification-icon bg-success"> <i
												class="fa 

fa-usd"></i>
										</span> <span class="m-

left-xs">应用程序错误。</span> <span
											class="time text-

muted">2小时前</span>
									</a></li>
									<li><a href="#"> <span
											

class="notification-icon bg-success"> <i
												class="fa 

fa-plus"></i>
										</span> <span class="m-

left-xs">新用户注册。</span> <span
											class="time text-

muted">5小时前</span>
									</a></li>
									<li><a href="#">查看所有的通知

</a></li>
								</ul></li>
						</ul>
					</div>
				</div>
			</div>
		</header>
		<!-- 框架左侧菜单 -->
		<aside class="sidebar-menu fixed">
			<div class="sidebar-inner scrollable-sidebar">
				<div class="main-menu">
					<ul class="accordion">
						<li class="menu-header">Main Menu</li>
						<li class="bg-palette2 active"><a href="../home.html"> 

<span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-home 

fa-lg"></i></span> <span
									class="text m-left-sm">系统首页

</span>
							</span> <span class="menu-content-hover block"> 系

统首页 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-edit 

fa-lg"></i></span> <span
									class="text m-left-sm">监察办理

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 监

察办理 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-

label">业务办理</span>
								</a>
									<ul class="submenu third-level">
										<li><a 

href="rwbl.html"><span class="submenu-label">任务办理</span></a></li>
										<li class="active"><a 

href="rwfp.html"><span
												

class="submenu-label">任务分配</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-

label">任务管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">任务计划管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">任务综合台账</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-

label">智能稽查</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">稽查对象智能筛选</span></a></li>
										<li><a href="#"><span 

class="submenu-label">执法与处罚规范性分析</span></a></li>
										<li><a href="#"><span 

class="submenu-label">电子稽查规则配置</span></a></li>
									</ul></li>
							</ul></li>
						<li class="openable bg-palette2 open"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-

commenting-o fa-lg"></i></span> <span
									class="text m-left-sm">执法对象

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 执

法对象 </span>
						</a>
							<ul class="submenu">
								<li><a href="../zfdx-all.html"><span
										class="submenu-label">所有

对象管理</span></a></li>
								<li><a href="../zfdx-

enterprise.html"><span
										class="submenu-label">企业

事业单位</span></a></li>
								<li><a href="../zfdx-

individual.html"><span
										class="submenu-label">个人

</span></a></li>
								<li><a href="../zfdx-individual-

Three.html"><span
										class="submenu-label">个人

、三无、小三产</span></a></li>
								<li><a href="../zfdx-

natureReserve.html"><span
										class="submenu-label">自然

保护区</span></a></li>
								<li><a href="../zfdx-stray.html"><span
										class="submenu-label">无主

对象</span></a></li>


							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-

commenting-o fa-lg"></i></span> <span
									class="text m-left-sm">异常线索

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 异

常线索 </span>
						</a>
							<ul class="submenu">
								<li><a href="#"><span class="submenu-

label">多源排放数据对比分析</span></a></li>
								<li><a href="#"><span class="submenu-

label">自动监控与自行监测数据相关性分析</span></a></li>
							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-globe 

fa-lg"></i></span> <span
									class="text m-left-sm">GIS应用

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 

GIS应用 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-

label">GIS应用</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">污染源分步</span></a></li>
										<li><a href="#"><span 

class="submenu-label">任务统计</span></a></li>
										<li><a href="#"><span 

class="submenu-label">代办统计</span></a></li>
									</ul></li>
							</ul></li>
						<li class="bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-check-

square-o fa-lg"></i></span> <span
									class="text m-left-sm">监察考核

</span>
							</span> <span class="menu-content-hover block"> 监

察考核 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-bar-

chart fa-lg"></i></span> <span
									class="text m-left-sm">统计分析

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 统

计分析 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">5</small>
										<span class="submenu-

label">统计分析</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">综合统计</span></a></li>
										<li><a href="#"><span 

class="submenu-label">日历展示</span></a></li>
										<li><a href="#"><span 

class="submenu-label">决策支持</span></a></li>
										<li><a href="#"><span 

class="submenu-label">规范性分析</span></a></li>
										<li><a href="#"><span 

class="submenu-label">超标处置</span></a></li>
									</ul></li>
							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-users 

fa-lg"></i></span> <span
									class="text m-left-sm">监察队伍

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 监

察队伍 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">1</small>
										<span class="submenu-

label">机构查询</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">机构查询</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-

label">人员查询</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">人员查询</span></a></li>
										<li><a href="#"><span 

class="submenu-label">执法证件</span></a></li>
										<li><a href="#"><span 

class="submenu-label">培训记录</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">1</small>
										<span class="submenu-

label">本地人员管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">人员管理</span></a></li>
									</ul></li>
							</ul></li>
						<li class="bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-book 

fa-lg"></i></span> <span
									class="text m-left-sm">环保智库

</span>
							</span> <span class="menu-content-hover block"> 环

保智库 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span 

class="menu-icon"><i
										class="block fa fa-cog 

fa-lg"></i></span> <span
									class="text m-left-sm">系统设置

</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 系

统设置 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-

label">用户管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">部门管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">系统用户</span></a></li>
										<li><a href="#"><span 

class="submenu-label">角色管理</span></a></li>
									</ul></li>
								<li><a href="form_element.html"><span
										class="submenu-label">类型

表维护</span></a></li>
								<li><a href="form_validation.html"><span
										class="submenu-label">预警

设置</span></a></li>
								<li><a href="form_wizard.html"><span
										class="submenu-label">流程

设置</span></a></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">4</small>
										<span class="submenu-

label">现场检查表设置</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">检查表管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">检查项管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">表单项管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">检查表配置</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-

label">询问笔录配置</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">询问笔录模板管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">询问内容管理</span></a></li>
										<li><a href="#"><span 

class="submenu-label">询问笔录配置</span></a></li>
									</ul></li>
								<li><a href="dropzone.html"><span 

class="submenu-label">任务分配规则配置</span></a></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success 

badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-

label">系统日志</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span 

class="submenu-label">污染源更新日志</span></a></li>
										<li><a href="#"><span 

class="submenu-label">系统日志</span></a></li>
									</ul></li>
							</ul></li>
					</ul>
				</div>

			</div>
		</aside>

		<div class="main-container">
			<div class="padding-md">


				<div class="row">
					<div class="col-lg-12">
						<div class="smart-widget widget-blue">
							<div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-header">
                                    <i class="fa fa-comment"></i> 丽珠集团福州福兴医药有限公司： <span
                                        class="smart-widget-option" style="margin-top: -7px;">
                                        <span class="refresh-icon-animated"><i
                                            class="fa fa-circle-o-notch fa-spin"></i></span> <a href="#"
                                        class="widget-toggle-hidden-option"> <i
                                            class="fa fa-cog"></i></a> <a href="#"
                                        class="widget-collapse-option" data-toggle="collapse">
                                            <i class="fa fa-chevron-up"></i>
                                    </a> <a href="#" class="widget-refresh-option"> <i
                                            class="fa fa-refresh"></i></a>
                                    </span>
                                </div>
                                <div class="smart-widget-inner table-responsive">
                                    <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color: #20232b;"
                                                data-color="widget-dark"></li>
                                            <li style="background-color: #4c5f70;"
                                                data-color="widget-dark-blue"></li>
                                            <li style="background-color: #23b7e5;"
                                                data-color="widget-blue"></li>
                                            <li style="background-color: #2baab1;"
                                                data-color="widget-green"></li>
                                            <li style="background-color: #edbc6c;"
                                                data-color="widget-yellow"></li>
                                            <li style="background-color: #fbc852;"
                                                data-color="widget-orange"></li>
                                            <li style="background-color: #e36159;"
                                                data-color="widget-red"></li>
                                            <li style="background-color: #7266ba;"
                                                data-color="widget-purple"></li>
                                            <li style="background-color: #f5f5f5;"
                                                data-color="widget-light-grey"></li>
                                            <li style="background-color: #fff;" data-color="reset"></li>
                                        </ul>
                                    </div>
									<div class="smart-widget-body 

form-horizontal">
                                        <div class="text-right m-top-md" style="margin-right: 15px;">
                                            <button class="btn btn-info" style="width: 120px;">返回查看结

果</button>
                                            <button class="btn btn-info" style="width: 100px;">保存

</button>
                                            <button class="btn btn-info" style="width: 140px;">保存并发起

任务</button>
    
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span 

style="color: red;">*</span>对象类别:</label>
                                            <div class="col-md-6"  style ="margin-top:5px;">
                                                <span>企事业单位</span>
                                            </div>
                                        </div>                                        
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                <span style="color: red;">*</span>执法对象ID:
                                            </label>
                                            <div class="col-md-6"  style ="margin-top:5px;">               

                                 
                                                <span>11225551121</span>
                                            </div>
                                        </div>                                        
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                <span style="color: red;">*</span>名称:
                                            </label>
                                            <div class="col-md-6" style ="margin-top:5px;">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                <span style="color: red;">*</span>执法对象所在行政区:
                                            </label>

                                            <div class="col-lg-2">
                                                <select class="form-control">
                                                    <option value="福建省">福建省</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-2">
                                                <select class="form-control">
                                                    <option value="">市级</option>
                                                    <option value="福州市">福州市</option>
                                                    <option value="厦门市">厦门市</option>
                                                    <option value="漳州市">漳州市</option>
                                                    <option value="泉州市">泉州市</option>
                                                    <option value="...">...</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-2">
                                                <select class="form-control">
                                                    <option value="">县级</option>
                                                    <option value="鼓楼区">鼓楼区</option>
                                                    <option value="台江区">台江区</option>
                                                    <option value="仓山区">仓山区</option>
                                                    <option value="晋安区">晋安区</option>
                                                    <option value="...">...</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                <span style="color: red;">*</span>地址:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                <span style="color: red;">*</span>权属行政区:
                                            </label>
                                            <div class="col-md-6">
                                            <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="省">省</option>
                                                <option value="市">市</option>
                                                <option value="县">县</option>                             

               
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="地理坐标" class="col-md-3 control-label">地理坐

标:</label>
                                            <div class="col-lg-2">
                                                经度<input type="hbfzrdh" class="form-control" id="mapJD"
                                                    placeholder="经度">
                                            </div>
                                            <div class="col-lg-2">
                                                纬度<input type="hbfzrdh" class="form-control" id="mapWD"
                                                    placeholder="纬度">
                                            </div>
                                            <div class="col-lg-2">
                                                <button  class="btn btn-info" type="button" data-

toggle="modal" data-target="#Map" id="createMap" style="margin-top: 18px;">定位</button>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                营业执照证件号:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                组织机构代码:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                统一社会信用代码:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="行业类型" class="col-md-3 control-label">行业类型

</label>
                                            <div class="col-lg-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control">
                                                    <div class="input-group-btn">
                                                        <button type="button" class="btn btn-info no-

shadow" tabindex="-1" data-toggle="modal" data-target="#Industrytype">行业类型选择</button>
                                                   
                                                        
                                                   </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                监管级别:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                                    <option value="请选择">请选择</option>
                                                    <option value="国控">国控</option>
                                                    <option value="省控">省控</option>
                                                    <option value="市控">市控</option>
                                                    <option value="县控">县控</option>
                                                    <option value="非控">非控</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                法人代表:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                法人电话:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                环保负责人:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                            环保负责人电话:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否重点源:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否在线监控企业:</label>
                                            <div class="col-md-6">
                                                    <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>                                        
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                生产状态:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                                    <option value="请选择">请选择</option>
                                                    <option value="正常生产">正常生产</option>
                                                    <option value="停产">停产</option>
                                                    <option value="在建">在建</option>
    
                                                </select>
                                            </div>
                                        </div>                                        
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否固废经营单位:</label>
                                            <div class="col-md-6">
                                                    <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否固废产生单位:</label>
                                            <div class="col-md-6">
                                                    <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否风险源:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                排污口是否规范化:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                                    <option value="请选择">请选择</option>
                                                    <option value="是">是</option>
                                                    <option value="否">否</option>
                                                
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                当事人性质:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                
                                                    <option value="请选择">请选择</option>
                                                    <option value="国有企业">国有企业</option>
                                                    <option value="国有独资公司">国有独资公司</option>
                                                    <option value="其他">其他</option>
                                                    <option value="未了解">未了解</option>			

									
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属级别:</label>
                                            <div class="col-md-6">
                                                <select class="form-control">
                                
                                                    <option value="请选择">请选择</option>
                                                    <option value="央属">央属</option>
                                                    <option value="省属">省属</option>
                                                    <option value="市属">市属</option>
                                                                                    
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否上市公司:</label>
                                            <div class="col-md-6">
                                                    <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>                                        
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                股票代码:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                是否所属集团公司:</label>
                                            <div class="col-md-6">
                                                    <select class="form-control">
                                                <option value="">请选择</option>
                                                <option value="是">是</option>
                                                <option value="否">否</option>
                                
                                            </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司名称:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司组织机构代码:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司股票代码:</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="inputtext3">
                                            </div>
                                        </div>
										
										 <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                对象介绍:</label>
                                            <div class="col-md-6">
                                              <textarea class="form-control" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
							</div>
						</div>
					</div>
				</div>
             </div>
         </div>
                
        <!--版权信息-->
        <footer class="footer">
            <span class="footer-brand"> <strong class="text-danger">福建省环境保护厅</strong>
                版权所有
            </span>
            <p class="no-margin">
                &copy; 2017 <strong>北京长能环境大数据科技有限公司</strong> 研发并提供技术支持
            </p>
        </footer>
        <!--./版权信息-->
    </div>

	<!--百度地图 模态框（Modal） -->
	<div class="modal fade" id="Map" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">百度地图选点(鼠标滚动缩

放地图,鼠标拖动移动地图)</h4>
				</div>
				<div class="modal-body" id="map_main" style="height: 600px;">在这里添加一

些文本</div>
				<div class="modal-footer">
					<input style="width: 300px; float: left;" type="text" value=""
						id="map_txt" class="form-control" readonly/>
					<button type="button" class="btn btn-primary"
						onclick="setMapValue()">设置为此地址</button>
					<button type="button" class="btn btn-default" data-

dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>
	<script src="jquery/2.1.1/jquery.min.js"></script>
	<script type="text/javascript" src="http://api.map.baidu.com/api?

v=2.0&ak=2d12993ce41407db4050140fe342d9ba"></script>
	<script type="text/javascript">
		$(function() {
			$("#createMap").click(
					function() {
						setTimeout(function() { //添加延时加载。解决问题
							var map = new BMap.Map("map_main");

							//map.setCenter("三明市莘口镇");
							map.enableScrollWheelZoom();//启动鼠标滚轮缩放地图
							map.enableKeyboard();//启动键盘操作地图
							map.addControl(new BMap.NavigationControl()); //添

加默认缩放平移控件
							map.addControl(new BMap.OverviewMapControl()); //

添加默认缩略地图控件
							map.addControl(new BMap.OverviewMapControl({
								isOpen : true,
								anchor : BMAP_ANCHOR_BOTTOM_RIGHT
							})); //右下角，打开

							var myCity = new BMap.LocalCity();

							myCity.get(function(res) {
								map.centerAndZoom(res.center, res.level);
								var mapWD = $("#mapWD").val();
								var mapJD = $("#mapJD").val();
								var old_map = "";//$('#map').val(); 
								if(mapJD!="" && mapWD!=""){
									old_map = mapJD+","+mapWD;
								}
								if (old_map.length > '5') { //打开的时候显

示已设置的
									$("#map_txt").val(old_map);
									var oldMap = old_map.split(",");
									var point = new BMap.Point(oldMap

[0],
											oldMap[1]);

									map.setCenter(point);
									map.setZoom(12)
									var marker = new BMap.Marker

(point); // 创建标注    
									map.clearOverlays();
									map.addOverlay(marker);
								}
								map.addEventListener("click", function(e) 

{
									var lng_lat = e.point.lng + ','
											+ e.point.lat;
									$("#map_txt").val(lng_lat); //加入

到设置框
									var point = new BMap.Point

(e.point.lng,
											e.point.lat);
									var marker = new BMap.Marker

(point); // 创建标注    
									map.clearOverlays(); //清除地图上

所有覆盖物。
									map.addOverlay(marker);
								});
							});

						}, 300);
					});
		});

		//设置经纬度
		function setMapValue() {
			if ($("#map_txt").val() == "") {
				swal("错误!","你还没选择相应的坐标点^_^哦", "error"); 
				return false;
			}
			var mapJW = $("#map_txt").val().split(",");
			$("#mapJD").val(mapJW[0]);
			$("#mapWD").val(mapJW[1]);
			$('#myModal').modal('hide')
		}
	</script>
	<!--百度地图 模态框（Modal） end --

    <!-- 行业类型选择（Modal） -->
	<div class="modal fade" id="Industrytype" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">行业类型</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body form-horizontal">
							<div class="form-group">
								<label for="行业名称" class="col-lg-2 

control-label">行业名称</label>
								<div class="col-lg-8">
									<input type="hylxmc" class="form-

control" id="hylxmc"
										placeholder="行业名称">
								</div>
							</div>
							<div class="form-group">
								<label for="门类" class="col-lg-2 

control-label">门类</label>
								<div class="col-lg-8">
									<select class="form-control">
										<option value="农、林、牧

、渔业">农、林、牧、渔业</option>
										<option value="采矿业">采

矿业</option>
										<option value="制造业">制

造业</option>
										<option value="电力、热力

、燃气及水生产和供应业">电力、热力、燃气及水生产和供应业</option>
										<option value="建筑业">建

筑业</option>
										<option value="批发和零售

业">批发和零售业</option>
										<option value="交通运输、

仓储和邮政业">交通运输、仓储和邮政业</option>
										<option value="住宿和餐饮

业">住宿和餐饮业</option>
										<option value="信息传输、

软件和信息技术服务业">信息传输、软件和信息技术服务业</option>
										<option value="金融业">金

融业</option>
										<option value="房地产业">

房地产业</option>
										<option value="租赁和商务

服务业">租赁和商务服务业</option>
										<option value="科学研究和

技术服务业">科学研究和技术服务业</option>
										<option value="水利、环境

和公共设施管理业">水利、环境和公共设施管理业</option>
										<option value="居民服务、

修理和其他服务业">居民服务、修理和其他服务业</option>
										<option value="教育">教育

</option>
										<option value="卫生和社会

工作">卫生和社会工作</option>
										<option value="文化、体育

和娱乐业">文化、体育和娱乐业</option>
										<option value="公共管理、

社会保障和社会组织">公共管理、社会保障和社会组织</option>
										<option value="国际组织">

国际组织</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="大类" class="col-lg-2 

control-label">大类</label>
								<div class="col-lg-8">
									<select class="form-control">
										<option value="农业">农业

</option>
										<option value="林业">林业

</option>
										<option value="牧业">牧业

</option>
										<option value="渔业">渔业

</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="中类" class="col-lg-2 

control-label">中类</label>
								<div class="col-lg-8">
									<select class="form-control">
										<option value="谷物种植">

谷物种植</option>
										<option value="豆类、油料

和薯类种植">豆类、油料和薯类种植</option>
										<option value="棉、麻、糖

、烟草种植">棉、麻、糖、烟草种植</option>
										<option value="蔬菜、食用

菌及园艺作物种植">蔬菜、食用菌及园艺作物种植</option>
										<option value="水果种植">

水果种植</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="小类" class="col-lg-2 

control-label">小类</label>
								<div class="col-lg-8">
									<select class="form-control">
										<option value="稻谷种植">

稻谷种植</option>
										<option value="小麦种植">

小麦种植</option>
										<option value="玉米种植">

玉米种植</option>
										<option value="其他谷物种

植">其他谷物种植</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<div class="col-lg-offset-2 col-lg-10">
									<button type="submit" class="btn 

btn-info"
										style="width: 120px;">查 

询</button>
								</div>
							</div>
					</div>
                    <table class="table table-striped table-bordered no-margin">
						<thead>
							<tr>
								<th class="text-center" style="width: 

5%;">门类</th>
								<th class="text-center" style="width: 

6%;">大类</th>
								<th class="text-center" style="width: 

6%;">中类</th>
								<th class="text-center" style="width: 

6%;">小类</th>
								<th class="text-center" style="width: 

30%;">类别名称</th>
								<th class="text-center" style="width: 

40%;">说明</th>
								<th class="text-center" style="width: 

7%;">操作</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">农、林、牧、渔业

</td>
								<td class="text-center">本门类包括01～05大

类</td>
								<td class="text-center">&nbsp;</td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">农业</td>
								<td class="text-center">指对各种农作物的种

植</td>
								<td class="text-center">&nbsp;</td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">011</td>
								<td class="text-center">&nbsp;</td>
								<td class="text-center">谷物种植</td>
								<td class="text-center">指以收获籽实为主，

供人类食用的农作物的种植，如稻谷、小麦、玉米等农作物的种植。</td>
								<td class="text-center">&nbsp;</td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">011</td>
								<td class="text-center">0111</td>
								<td class="text-center">稻谷种植</td>
								<td class="text-center"></td>
								<td class="text-center"><i class="fa fa-

check"
									style="color: #23b7e5;">选择

</i></td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">011</td>
								<td class="text-center">0112</td>
								<td class="text-center">小麦种植</td>
								<td class="text-center"></td>
								<td class="text-center"><i class="fa fa-

check"
									style="color: #23b7e5;">选择

</i></td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">011</td>
								<td class="text-center">0113</td>
								<td class="text-center">玉米种植</td>
								<td class="text-center"></td>
								<td class="text-center"><i class="fa fa-

check"
									style="color: #23b7e5;">选择

</i></td>
							</tr>
							<tr>
								<td class="text-center">A</td>
								<td class="text-center">01</td>
								<td class="text-center">011</td>
								<td class="text-center">0119</td>
								<td class="text-center">其他谷物种植</td>
								<td class="text-center"></td>
								<td class="text-center"><i class="fa fa-

check"
									style="color: #23b7e5;">选择

</i></td>
							</tr>
							<tr>
								<td class="text-center">B</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">采矿业</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">C</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">制造业</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">D</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">电力、热力、燃气及

水生产和供应业</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">E</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">建筑业</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">F</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">批发和零售业</td>

								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">G</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">交通运输、仓储和邮

政业</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
							<tr>
								<td class="text-center">...</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center"></td>
								<td class="text-center">...</td>
								<td class="text-center"></td>
								<td class="text-center"></td>
							</tr>
						</tbody>
					</table>
				</div>
				<!--翻页-->
				<div style="margin: -20px 15px 0 0; float: right;">
					<div style="float: left;">
						<ul class="pagination pagination-split pagination-sm">
							<li class="disabled"><a href="#">&laquo;</a></li>
							<li class="active"><a href="#">1</a></li>
							<li><a href="#">2</a></li>
							<li><a href="#">3</a></li>
							<li><a href="#">4</a></li>
							<li><a href="#">5</a></li>
							<li><a href="#">&raquo;</a></li>
						</ul>
					</div>
					<div style="float: left; margin-top: 20px;">
						<div class="form-group">
							<select class="form-control input-sm">
								<option value="5条">5条</option>
								<option value="10条">10条</option>
								<option value="20条">20条</option>
								<option value="30条">30条</option>
								<option value="50条">50条</option>
							</select>
						</div>
					</div>
				</div>
				<!--./翻页-->
				<div class="modal-footer" style="margin-top: 40px;">
					<button type="button" class="btn btn-info" data-dismiss="modal">确

定</button>
					<button type="button" class="btn btn-default" data-

dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
	<!-- ./行业类型选择（Modal） -->



	<a href="#" class="scroll-to-top hidden-print"><i
		class="fa fa-chevron-up fa-lg"></i></a>



	<!-- Le javascript
	    ================================================== -->
	<!-- Placed at the end of the document so the pages load faster -->

	<!-- Jquery -->
	<script src="../jquery/1.10.2/jquery.min.js"></script>

	<!-- Bootstrap -->
	<script src="../libs/bootstrap/3.3.4/js/bootstrap.min.js"></script>

	<!-- Flot -->
	<script src='../js/jquery.flot.min.js'></script>

	<!-- Slimscroll -->
	<script src='../js/jquery.slimscroll.min.js'></script>

	<!-- Morris -->
	<script src='../js/rapheal.min.js'></script>
	<script src='../js/morris.min.js'></script>

	<!-- Datepicker -->
	<script src='../js/uncompressed/datepicker.js'></script>

	<!-- Sparkline -->
	<script src='../js/sparkline.min.js'></script>

	<!-- Skycons -->
	<script src='../js/uncompressed/skycons.js'></script>

	<!-- Popup Overlay -->
	<script src='../js/jquery.popupoverlay.min.js'></script>

	<!-- Easy Pie Chart -->
	<script src='../js/jquery.easypiechart.min.js'></script>

	<!-- Sortable -->
	<script src='../js/uncompressed/jquery.sortable.js'></script>

	<!-- Owl Carousel -->
	<script src='../js/owl.carousel.min.js'></script>

	<!-- Modernizr -->
	<script src='../js/modernizr.min.js'></script>

	<!-- Simplify -->
	<script src="../js/simplify/simplify.js"></script>
	<script src="../js/simplify/simplify_dashboard.js"></script>
	<script src="../js/lory.min.js"></script>

	<script>
			$(function()	{
				$('.chart').easyPieChart({
					easing: 'easeOutBounce',
					size: '140',
					lineWidth: '7',
					barColor: '#7266ba',
					onStep: function(from, to, percent) {
						$(this.el).find('.percent').text(Math.round(percent));
					}
				});

				$('.sortable-list').sortable();

				$('.todo-checkbox').click(function()	{
					
					var _activeCheckbox = $(this).find('input[type="checkbox"]');

					if(_activeCheckbox.is(':checked'))	{
						$(this).parent().addClass('selected');				

	
					}
					else	{
						$(this).parent().removeClass('selected');
					}
				
				});

				//Delete Widget Confirmation
				$('#deleteWidgetConfirm').popup({
					vertical: 'top',
					pagecontainer: '.container',
					transition: 'all 0.3s'
				});
					function evntClick(){
			
			
			window.open

('zhtzxqlb.html','','toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=ye

s,width=950,height=680,left=180,top=140');
				
			};
			
			 
			
		</script>
	<script>
			
			function openImg(){
				
				
			}
		
		</script>

	<script>

	  document.addEventListener('DOMContentLoaded', function () {
			var multiSlides = document.querySelector('.js_multislides');
	
			lory(multiSlides, {
				infinite: 4,
				slidesToScroll: 4
			});
		});
       
    </script>

</body>
</html>
