{"name": "bootstrap-fileinput", "version": "4.3.9", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "authors": ["Kart<PERSON> <<EMAIL>>"], "description": "An enhanced HTML 5 file input for Bootstrap 3.x with file preview, multiple selection, ajax uploads, and more features.", "repository": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git"}, "bugs": {"url": "https://github.com/kartik-v/bootstrap-fileinput/issues"}, "keywords": ["bootstrap", "file", "input", "preview", "image", "upload", "ajax", "multiple", "delete", "progress", "gallery"], "main": "./js/fileinput.js", "style": "./css/fileinput.css", "peerDependencies": {"jquery": ">= 1.9.0", "bootstrap": "~3"}, "license": "BSD-3-<PERSON><PERSON>"}