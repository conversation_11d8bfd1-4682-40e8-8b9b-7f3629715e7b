package org.changneng.framework.frameworkweb.controller;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

@RequestMapping("Analysis")
@Controller
public class AnalysisController {
	
	private Logger logger = LogManager.getLogger(AnalysisController.class);
   //455788
	
	/**
	 * 跳转到历史案件分析页面
	 * @return
	 */
	@RequestMapping("historyCaseAnalysis")
	public ModelAndView toHistoryCaseAnaly(){
		// 李宏利
		ModelAndView modelAndView = new ModelAndView("analysis/tjfx-lsajfx");
		String url=PropertiesHandlerUtil.getValue("historyCaseAnalysis","fastdfs");
		modelAndView.addObject("url", url);
		return modelAndView;
	}
	
	/**
	 * 跳转到历史任务页面
	 * @return
	 */
	@RequestMapping("toHistoryTaskList")
	public ModelAndView toHistoryTaskList(){
		ModelAndView modelAndView = new ModelAndView("analysis/toHistoryListPage");
		logger.info("跳转到页面");
		String url=PropertiesHandlerUtil.getValue("historyCaseInfo","fastdfs");
		modelAndView.addObject("url", url);
		//response.setHeader("Access-Control-Allow-Origin", "*");
		return modelAndView;
	}
}
