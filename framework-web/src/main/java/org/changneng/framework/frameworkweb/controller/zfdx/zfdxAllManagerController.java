package org.changneng.framework.frameworkweb.controller.zfdx;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Month;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import oracle.net.aso.q;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.dao.LawCircleFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.LawCircleMapper;
import org.changneng.framework.frameworkbusiness.dao.LawEnforceObjectMapper;
import org.changneng.framework.frameworkbusiness.dao.PPunishmentHistoryMapper;
import org.changneng.framework.frameworkbusiness.dao.TcDictionaryMapper;
import org.changneng.framework.frameworkbusiness.entity.Area;
import org.changneng.framework.frameworkbusiness.entity.ChickObjectBean;
import org.changneng.framework.frameworkbusiness.entity.DoubleRandomType;
import org.changneng.framework.frameworkbusiness.entity.EnterpriseRandomDatabase;
import org.changneng.framework.frameworkbusiness.entity.EnvironmentChickTaskBean;
import org.changneng.framework.frameworkbusiness.entity.IndustryType;
import org.changneng.framework.frameworkbusiness.entity.LawCircle;
import org.changneng.framework.frameworkbusiness.entity.LawCircleFiles;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.LawObjectJsonBean;
import org.changneng.framework.frameworkbusiness.entity.LawobjOperaLog;
import org.changneng.framework.frameworkbusiness.entity.ObjectCommentResult;
import org.changneng.framework.frameworkbusiness.entity.PPunishmentHistory;
import org.changneng.framework.frameworkbusiness.entity.PowerAreaInfo;
import org.changneng.framework.frameworkbusiness.entity.RandomPollutionDatabase;
import org.changneng.framework.frameworkbusiness.entity.SeachObjectBean;
import org.changneng.framework.frameworkbusiness.entity.SysResources;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TaskFlow;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatCommit;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatToken;
import org.changneng.framework.frameworkbusiness.repeatCommit.PutSessionValue;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkbusiness.service.ISysRolesService;
import org.changneng.framework.frameworkbusiness.service.IndustryTypeService;
import org.changneng.framework.frameworkbusiness.service.Init84Service;
import org.changneng.framework.frameworkbusiness.service.JcblService;
import org.changneng.framework.frameworkbusiness.service.LawobjOperaLogService;
import org.changneng.framework.frameworkbusiness.service.RandomPollutionSDBService;
import org.changneng.framework.frameworkbusiness.service.TAreaService;
import org.changneng.framework.frameworkbusiness.service.ZfdxManagerService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.changneng.framework.frameworkcore.utils.ViewExcel;
import org.changneng.framework.frameworkcore.utils.ZfdxQueryExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.github.pagehelper.PageHelper;

@SuppressWarnings("all")
@Controller
@RequestMapping("/zfdx")
public class zfdxAllManagerController {

	@Autowired
	private ZfdxManagerService zfdxManagerService;
	@Autowired
	private JcblService jcblService;

	@Autowired
	private IndustryTypeService industryTypeService;

	@Autowired
	private TAreaService tAreaService;
	
	@Autowired
	private RandomPollutionSDBService randomPollutionSDBService;
	
	@Autowired
	private LawobjOperaLogService lawobjOperaLogService;
	
	@Autowired
	private ICommonService commonService;
	
	@Autowired
	private ISysRolesService iSRService;
	
	@Autowired
	private TcDictionaryMapper tcDictionaryMapper;
	
	@Autowired
	private PPunishmentHistoryMapper ppMapper;
	
	@Autowired
	private Init84Service init84Service;
	@Autowired
	private LawEnforceObjectMapper lawEnforceObjectMapper;
	
	@Autowired
	private LawCircleMapper lawCircleMapper;
	
	@Autowired
	private LawCircleFilesMapper lawCircleFilesMapper;

	/**
	 * 给企事业单位添加双随机属性
	 * 
	 * @param allUnitSelected
	 * @param randomAttrs
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/addRandomAttr")
	@ResponseBody
	public ResponseJson addRandomAttr(String allUnitSelected, String randomAttrs,
			HttpServletRequest request, HttpServletResponse response){
		ResponseJson json = null;
		try {
			json = randomPollutionSDBService.addRandomAttr(allUnitSelected, randomAttrs);
		} catch (Exception e) {
			e.printStackTrace();
			json = new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "500", "添加失败", e.getMessage(), null);
		}
		return json;
	}
	/**
	 * 修改企事业单位双随机属性
	 * @param allUnitSelected  选中的执法对象id拼接串
	 * @param randomAttrs 页面选中的双随机属性id拼接串
	 * @param request
	 * @param response
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/batchModifyRandomAttr")
	@ResponseBody
	public ResponseJson batchModifyRandomAttr(String allUnitSelected, String randomAttrs,String halfCheckAttrs,
			HttpServletRequest request, HttpServletResponse response){
		ResponseJson json = null;
		String[] split = allUnitSelected.split(",");
		try {
			for(String objId:split){
				json = randomPollutionSDBService.batchModifyRandomAttr(objId, randomAttrs,halfCheckAttrs);
			}
		} catch (Exception e) {
			e.printStackTrace();
			json = new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "500", "修改失败", e.getMessage(), null);
		}
		return json;
	}
	
	
	
	@RequestMapping(value = "/intLawObjectNumber")
	@ResponseBody
	public ResponseJson intLawObjectNumber(	HttpServletRequest request, HttpServletResponse response){
		ResponseJson json = null;
		try {
			zfdxManagerService.intLawObjectNumber();
		} catch (Exception e) {
			e.printStackTrace();
			json = new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "500", "添加失败", e.getMessage(), null);
		}
		return json;
	}
	
	/**
	 * 新增一个企事业单位
	 * 
	 * @param path
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/pages/zfdx-jbxx-qsyxg", method = RequestMethod.POST)
	public ModelAndView toZfdxAddPage(HttpServletRequest request,
			HttpServletResponse response) {
		// 查询所有的执法对象信息
		ModelAndView mav = new ModelAndView("zfdx/pages/zfdx-jbxx-qsyxg");
		return mav;
	}

	/**
	 * 所有对象首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-All")
	 */
	@RequestMapping(value = "/zfdx-all", method = RequestMethod.POST)
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	public ModelAndView zfdxAllForwardMethow(String back,
			HttpServletRequest request, HttpServletResponse response,String menuId,
			Model model, ZfdxQueryExample query) {
		// 页面回显
		ModelAndView mav = new ModelAndView();
		// 查看当前登录用户是否有访问该菜单的权限
	/*	if(!iSRService.getLoginUserPower(request.getServletPath())){
   			mav.setViewName("error/405");
			return mav;
		}*/ 
		mav.setViewName("zfdx/zfdx-all");
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
 
		mav.addObject("menuId",menuId);
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session
					.getAttribute("paramsInSession");

			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)
						&& !"randomProvince".equals(key)
						&& !"randomCity".equals(key)
						&& !"randomCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			linkedParams.put("randomProvince", paramsMap.get("randomProvince"));
			linkedParams.put("randomCity", paramsMap.get("randomCity"));
			linkedParams.put("randomCountry", paramsMap.get("randomCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-all".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		
		return mav;
	}
	
	/***
	 * 失信被执行人
	 * @param back
	 * @param request
	 * @param response
	 * @param menuId
	 * @param model
	 * @param query
	 * @return
	 * <AUTHOR>
	 * @date 2018年8月22日-下午5:14:32
	 */
	@RequestMapping(value = "/zfdx-supervise", method = RequestMethod.POST)
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	public ModelAndView zfdxSuperviseForwardMethow(String back, HttpServletRequest request, HttpServletResponse response,String menuId, Model model, ZfdxQueryExample query) {
		// 页面回显
		ModelAndView mav = new ModelAndView();
		// 查看当前登录用户是否有访问该菜单的权限
		mav.setViewName("zfdx/zfdx-supervise");
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		mav.addObject("menuId",menuId);
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)
						&& !"randomProvince".equals(key)
						&& !"randomCity".equals(key)
						&& !"randomCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			linkedParams.put("randomProvince", paramsMap.get("randomProvince"));
			linkedParams.put("randomCity", paramsMap.get("randomCity"));
			linkedParams.put("randomCountry", paramsMap.get("randomCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-supervise".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		return mav;
	}

	// newLawObject
	@RequestMapping(value = "/newLawObjectPage", method = RequestMethod.POST)
	@PutSessionValue
	public ModelAndView newLawObjectPage(HttpServletRequest request,
			HttpServletResponse response, Model model, String type, String id,String menuId,String backAllMenuId) {
		LawEnforceObjectWithBLOBs entity = null;
		PowerAreaInfo areaInfo = new PowerAreaInfo();
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		Area areatemp = tAreaService.getLevelArea(sysUser.getBelongAreaId());
		areaInfo.setArealevel(areatemp.getArealevel());
		if (id != null && !"".equals(id)) {
			entity = zfdxManagerService.selectByPrimaryKey(id);
			//从修改执法对象类型过来时 type和entity中的不一样，以传入的为准
			entity.setTypeCode(type);
			String belongAreaId = entity.getBelongAreaId();
			String powerAreaId = entity.getPowerAreaId();
			if(!"null".equals(entity.getGridCity())&&entity.getGridCity()!=null){
				String gridCityCode = entity.getGridCity().split(",")[0];
				areaInfo.setGridCityCode(gridCityCode);
			}
			if(!"null".equals(entity.getGridCounty())&&entity.getGridCounty()!=null){
				String gridCountyCode = entity.getGridCounty().split(",")[0];
				areaInfo.setGridCountyCode(gridCountyCode);
			}
			if(!"null".equals(entity.getGridStreet())&&entity.getGridStreet()!=null){
				String gridStreetCode = entity.getGridStreet().split(",")[0];
				areaInfo.setGridStreetCode(gridStreetCode);
			}
			if(!"null".equals(entity.getGridCommunity())&&entity.getGridCommunity()!=null) {
				String gridCommunityCode = entity.getGridCommunity().split(",")[0];
				areaInfo.setGridCommunityCode(gridCommunityCode);
			}

			if(!"null".equals(entity.getGridUcode())&&entity.getGridUcode()!=null) {
				String getGridUcode = entity.getGridUcode();
				areaInfo.setGridUcode(getGridUcode);
			}

			if (belongAreaId != null && !"".equals(belongAreaId)) {
				String belongAreaIdTemp = belongAreaId.substring(4);
				if ("35000000".equals(belongAreaId)) {
					areaInfo.setCityStatus("1");
				} else if ("0000".equals(belongAreaIdTemp)
						&& !"35000000".equals(belongAreaId)) {
					areaInfo.setCityStatus("2");
					Area area = tAreaService
							.getCityCodeByBelongAreaId(belongAreaId);
					String cityName = area.getName();
					areaInfo.setCityCode(belongAreaId);
					areaInfo.setCityName(cityName);
				} else {
					areaInfo.setCityStatus("3");
					Area area = tAreaService.getCityCodeByBelongAreaId(belongAreaId);
					if(area!=null){
						String countyName = area.getName();
						Area areaCity = tAreaService.getCityCodeByCityName(area.getCity());
						String cityCode = areaCity.getCode();
						String cityName = areaCity.getName();
						areaInfo.setCityCode(cityCode);
						areaInfo.setCityName(cityName);
						areaInfo.setCountyName(countyName);
					}
					areaInfo.setCountyCode(belongAreaId);
				}
			}
			if (powerAreaId != null && !"".equals(powerAreaId)) {
				String powerAreaIdTemp = powerAreaId.substring(4);
				if ("35000000".equals(powerAreaId)) {
					areaInfo.setPowerCityStatus("1");
				} else if ("0000".equals(powerAreaIdTemp)
						&& !"35000000".equals(powerAreaId)) {
					areaInfo.setPowerCityStatus("2");
					Area area = tAreaService
							.getCityCodeByBelongAreaId(powerAreaId);
					String cityName = area.getName();
					areaInfo.setPowerCityCode(powerAreaId);
					areaInfo.setPowerCityName(cityName);

				} else {
					areaInfo.setPowerCityStatus("3");
					Area area = tAreaService
							.getCityCodeByBelongAreaId(powerAreaId);
					String countyName = area.getName();
					Area areaCity = tAreaService.getCityCodeByCityName(area
							.getCity());
					String cityCode = areaCity.getCode();
					String cityName = areaCity.getName();
					areaInfo.setPowerCityCode(cityCode);
					areaInfo.setPowerCityName(cityName);
					areaInfo.setPowerCountyCode(powerAreaId);
					areaInfo.setPowerCountyName(countyName);
				}

			}
		} else {
			String belongAreaId = sysUser.getBelongAreaId();
			if (belongAreaId != null && !"".equals(belongAreaId)) {
				String belongAreaIdTemp = belongAreaId.substring(4);
				if ("35000000".equals(belongAreaId)) {
					areaInfo.setCityStatus("1");
				} else if ("0000".equals(belongAreaIdTemp)
						&& !"35000000".equals(belongAreaId)) {
					areaInfo.setCityStatus("2");
					Area area = tAreaService
							.getCityCodeByBelongAreaId(belongAreaId);
					String cityName = area.getName();
					areaInfo.setCityCode(belongAreaId);
					areaInfo.setCityName(cityName);
				} else {
					areaInfo.setCityStatus("3");
					Area area = tAreaService.getCityCodeByBelongAreaId(belongAreaId);
					String countyName = area.getName();
					Area areaCity = tAreaService.getCityCodeByCityName(area.getCity());
					String cityCode = areaCity.getCode();
					String cityName = areaCity.getName();
					areaInfo.setCityCode(cityCode);
					areaInfo.setCityName(cityName);
					areaInfo.setCountyCode(belongAreaId);
					areaInfo.setCountyName(countyName);
				}
			}
		}
		// 页面回显
		//往LawEnforceObjectWithBLOBs中放入双随机属性参数
		String databaseIds = randomPollutionSDBService.getAttrIdsByEntId(id);
		if (databaseIds != null && !"".equals(databaseIds)) {
			entity.setHiddenAttrIds(databaseIds);
		}
		model.addAttribute("entity", entity);
		//污染源类别
		model.addAttribute("pollutionSourceTypes",tcDictionaryMapper.getTcDictionaryListByCode("POLLUTION_SOURCE_TYPE", null));
		//适用排污许可行业技术规范
		model.addAttribute("SYPWXKHYJSGF",tcDictionaryMapper.getTcDictionaryListByCode("CHECK_ITEM_SELECT_BSLT", null));

		model.addAttribute("SEWAGECLASSIFY",tcDictionaryMapper.getTcDictionaryListByCode("SEWAGE_CLASSIFY", null));
		ModelAndView mav = new ModelAndView();
		if ("1".equals(type)) {
			// 企事业单位类型回显
			model.addAttribute("objectType", "企事业单位");
			model.addAttribute("typeCode", "1");
			model.addAttribute("randomAttr",randomPollutionSDBService.selectAllAttr(sysUser.getBelongAreaId()));
			mav.setViewName("zfdx/pages/zfdx-jbxx-qsyxg");
		} else if ("2".equals(type)) {
			// 个人类型回显
			model.addAttribute("objectType", "个人");
			model.addAttribute("typeCode", "2");
			mav.setViewName("zfdx/pages/zfdx-jbxx-grxg");
		} else if ("3".equals(type)) {
			// 个人、三无、小三产
			model.addAttribute("objectType", " 个体、三无、小三产");
			model.addAttribute("typeCode", "3");
			model.addAttribute("randomAttr",randomPollutionSDBService.selectAllAttr(sysUser.getBelongAreaId()));
			mav.setViewName("zfdx/pages/zfdx-jbxx-gtxg");

		} else if ("4".equals(type)) {
			// 自然保护区
			model.addAttribute("objectType", "自然保护区");
			model.addAttribute("typeCode", "4");
			mav.setViewName("zfdx/pages/zfdx-jbxx-natureReservexg");
		} else if ("6".equals(type)) {
			// 水源地
			model.addAttribute("objectType", "水源地");
			model.addAttribute("typeCode", "6");
			mav.setViewName("zfdx/pages/zdfx-jbxx-addWatersource");

		} else {
			//什么问题啊
			// 无主对象
			model.addAttribute("objectType", "无主");
			model.addAttribute("typeCode", "5");
			model.addAttribute("randomAttr", randomPollutionSDBService.selectAllAttr(sysUser.getBelongAreaId()));
			mav.setViewName("zfdx/pages/zfdx-jbxx-strayxg");
		}
		model.addAttribute("areaInfo", areaInfo);
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		mav.addObject("res",res);
		if(backAllMenuId!=null&&!"".equals(backAllMenuId)){
			model.addAttribute("menuId", backAllMenuId);
		}else{
			model.addAttribute("menuId", menuId);
		}
		return mav;
	}

	// 全显页面批量删除
	@RequestMapping(value = "/zfdx-delete-select")
	public ModelAndView deleteAllSelectMethod(HttpServletRequest request,
			HttpServletResponse response, Model model,
			@RequestParam("ids") String[] ids) {
		zfdxManagerService.deleteByPrimaryKey(ids);
		ModelAndView mav = new ModelAndView("redirect:/zfdx/zfdx-all");
		return mav;
	}

	// 全部对象页面通过主键单独删除
	@RequestMapping("/zfdx-delete-primarykey")
	public ModelAndView deleteByPrimaryKey(HttpServletRequest request,
			HttpServletResponse response, Model model, String id) {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		String[] ids = new String[1];
		ids[0] = id;
		zfdxManagerService.deleteByPrimaryKey(ids);
		LawEnforceObjectWithBLOBs lawobj = zfdxManagerService.selectByPrimaryKey(id);
		LawobjOperaLog lawobjOperaLog = new LawobjOperaLog();
		lawobjOperaLog.setLawobjName(lawobj.getObjectName());
		String belongAreaId = lawobj.getBelongAreaId();
		Area levelArea = tAreaService.getLevelArea(belongAreaId);
		String AreaName = levelArea.getProvince()+levelArea.getCity()+levelArea.getCountry()+"";
		lawobjOperaLog.setOperaBelongarea(AreaName);
		
		lawobjOperaLog.setOperaDepartment(sysUsers.getOrgBelongDepartName());
		lawobjOperaLog.setOperaMan(sysUsers.getLoginname());
		lawobjOperaLog.setOperationType("删除");
		lawobjOperaLog.setOperDate(new Date());
		lawobjOperaLog.setAreaCode(belongAreaId);
		
		int a = lawobjOperaLogService.InsertLawobjOperaLog(lawobjOperaLog);
		System.out.println("单个删除操作日志插入状态："+a);
		ModelAndView mav = new ModelAndView("redirect:/zfdx/zfdx-all");
		return mav;
	}

	// 对象基本信息展示
	@RequestMapping("/pages/showInfo")
	public ModelAndView ShowQsyJbxxMethod(HttpServletRequest request,
			HttpServletResponse response, String id, String type) {

		LawEnforceObjectWithBLOBs bloBs = zfdxManagerService
				.selectByPrimaryKey(id);

		ModelAndView mav = new ModelAndView();
		mav.addObject("entity", bloBs);

		switch (type) {
		case "1":
			mav.setViewName("zfdx/pages/zfdx-jbxx-qsy");
			break;
		case "2":
			mav.setViewName("zfdx/pages/zfdx-jbxx-gr");
			break;
		case "3":
			mav.setViewName("zfdx/pages/zfdx-jbxx-qt");
			break;
		case "4":
			mav.setViewName("zfdx/pages/zfdx-jbxx-natureReserve");
			break;
		case "5":
			mav.setViewName("zfdx/pages/zfdx-jbxx-stray");
			break;
		}
		return mav;

	}

	// 跳转到企事业对象修改页面，并把当前对象的信息展出出来(包括模态框里的原始数据)
	@RequestMapping("/zfdx-jbxx-qsyxg")
	public ModelAndView ToChangeQsyJspMethod(String id, ZfdxQueryExample query) {
		LawEnforceObjectWithBLOBs bloBs = zfdxManagerService
				.selectByPrimaryKey(id);

		List<IndustryType> list = industryTypeService.
				selectAllIndustryType(query);
		PageBean<IndustryType> bean = new PageBean<IndustryType>(list);
		
		ModelAndView mav = new ModelAndView();
		mav.addObject("pageBean", bean);
		mav.addObject("entity", bloBs);
		mav.setViewName("zfdx/pages/zfdx-jbxx-qsyxg");
		return mav;
	}

	// 分页展示所有行业信息(页面不能刷新)
	@RequestMapping("/industryTypecheckList")
	@ResponseBody
	public PageBean IndustryTypecheckList(HttpServletRequest request,
			HttpServletResponse response, Model model,ZfdxQueryExample query) {
		
		query.setName(query.getName().trim());
		List<IndustryType> list = industryTypeService.selectAllIndustryType(query);
		for (IndustryType industryType : list) {
			if (industryType.getFourthClass() == null) {
				industryType.setFourthClass("");
			}
			if (industryType.getSecondClass() == null) {
				industryType.setSecondClass("");
			}
			if (industryType.getThirdClass() == null) {
				industryType.setThirdClass("");
			}
			if (industryType.getFirstCalss() == null) {
				industryType.setFirstCalss("");
			}
			if (industryType.getIndustryDesc() == null) {
				industryType.setIndustryDesc("");
			}
		}
		PageBean<IndustryType> bean = new PageBean<>(list);
		model.addAttribute("pageBean", bean);
		return bean;
	}

	// 保存修改的企事业对象
	/**
	 * 
	 * @param record
	 * @param bResult
	 * @param menuId
	 * @param startAndSavestatus 1保存并发起任务 
	 * @return
	 * @throws Exception
	 */
	//@CheckRepeatCommit
	@RequestMapping(value = "/zfdx-save-all")
	@CheckRepeatToken
	@SysLogPoint(businessType = businessType.ADD_LAW_OBJECT, dbOptType = dbType.ADD)
	@ResponseBody
	public ResponseJson saveChangeconfirmMethod(
			@Validated LawEnforceObjectWithBLOBs record, BindingResult bResult, String menuId, 
			String startAndSavestatus) throws Exception {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		ResponseJson json = new ResponseJson();
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		if(!StringUtils.isEmpty(record.getStartDateStr())){
			record.setStartDate(sdf.parse(record.getStartDateStr()));
		}
		if(!StringUtils.isEmpty(record.getEndDateStr())){
			record.setEndDate(sdf.parse(record.getEndDateStr()));
		}
		LawobjOperaLog lawobjOperaLog = new LawobjOperaLog();
		lawobjOperaLog.setLawobjName(record.getObjectName());
		//lawobjOperaLog.setOperaBelongarea(sysUsers.getBelongAreaName());
		String belongAreaCOde = record.getBelongAreaId();
		Area levelArea = tAreaService.getLevelArea(belongAreaCOde);
		System.out.println(bResult.getTarget().toString());
		String AreaName = levelArea.getProvince()+levelArea.getCity()+levelArea.getCountry()+"";
		lawobjOperaLog.setOperaBelongarea(AreaName);
		lawobjOperaLog.setOperaDepartment(sysUsers.getOrgBelongDepartName());
		lawobjOperaLog.setOperaMan(sysUsers.getLoginname());
		lawobjOperaLog.setOperDate(new Date());
		lawobjOperaLog.setAreaCode(belongAreaCOde);

		if (!bResult.hasErrors()) {
			setLawObjectCode(record);
			String id = record.getId();
			if ("".equals(id) || id == null) {
				// 查询名称是否
				String typeCode = record.getTypeCode();
				String belongAreaId = record.getBelongAreaId();
				String objectName = record.getObjectName();
				String objectNam = record.getSewageClassify();
				System.out.println(objectNam);

				String status = zfdxManagerService.chickObjectName(typeCode,belongAreaId, objectName);
				if ("0".equals(status)) {
					// 查重数据库中没有
					json.success("400", "400", "名称重复，请更换名称!", null, null);
				} else {
					json = zfdxManagerService.updateByPrimaryKeySelective(record, startAndSavestatus, null);
				}
				//保存操作日志
				lawobjOperaLog.setOperationType("新增");
				int a = lawobjOperaLogService.InsertLawobjOperaLog(lawobjOperaLog);
				System.err.println("新增操作日志插入："+a);
			} else {
				json = zfdxManagerService.updateByPrimaryKeySelective(record,startAndSavestatus, menuId);
				lawobjOperaLog.setOperationType("修改");
				int a = lawobjOperaLogService.InsertLawobjOperaLog(lawobjOperaLog);
				System.err.println("修改操作日志插入："+a);
			}
			return json;
		} else {
			json.success(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.SYSTEM_ERROR.toString(), bResult.getFieldError().getDefaultMessage(), null, null);
			return json;
		}
	}

	/**
	 * 企业事业单位首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-enterprise")
	 */
	@RequestMapping(value = "/zfdx-enterprise", method = RequestMethod.POST)
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	public ModelAndView zfdxEnterpriseForwardMethow(String back,String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-enterprise");
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
	
		mav.addObject("menuId",menuId);
		/*初始化企事业单位页面时初始化双随机属性*/
		List<RandomPollutionDatabase> randomAttr = randomPollutionSDBService.selectAllAttr(sysUsers.getBelongAreaId());
		mav.addObject("randomAttr",randomAttr);
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");

			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)
						&& !"randomProvince".equals(key)
						&& !"randomCity".equals(key)
						&& !"randomCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			linkedParams.put("randomProvince", paramsMap.get("randomProvince"));
			linkedParams.put("randomCity", paramsMap.get("randomCity"));
			linkedParams.put("randomCountry", paramsMap.get("randomCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-enterprise".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		return mav;
	}
	
	/**
	 * industry-type-page 行业类型加载框
	 */
	@RequestMapping(value = "/industry-type-page")
	public ModelAndView industryTypePage(Model model,HttpServletRequest request, HttpServletResponse response){
		ModelAndView view=new ModelAndView("model/industryTypeModel");
		return view;
	}
	
	/**
	 * 选择双随机属性的模态框
	 */
	@RequestMapping(value="randomAttrModal")
	public ModelAndView fileUploadPage(HttpServletRequest request, HttpServletResponse response, String ids, String typeCode){
		ModelAndView view = null;
		if("3".equals(typeCode)){
			view=new ModelAndView("zfdx/randomAttrModalSw");
		}else if("5".equals(typeCode)){
			view=new ModelAndView("zfdx/randomAttrModalNo");
		}else{
			view=new ModelAndView("zfdx/randomAttrModal");
		}
		 SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		 /*初始化企事业单位页面时初始化双随机属性*/
		List<RandomPollutionDatabase> randomAttr = randomPollutionSDBService.selectAllAttr(sysUsers.getBelongAreaId());
		Map map = new HashMap<String,Integer>();
		String[] objIds = ids.split(",");
		int chooseObjCount = objIds.length;
		for(RandomPollutionDatabase r:randomAttr){
			if(chooseObjCount==1){
				//选中一条执法对象时，若有（选中）1，若没有（未选中）0
				for(String id:objIds){
					List<EnterpriseRandomDatabase> enterpriseRandomAttrs = randomPollutionSDBService.getEnterpriseRandomAttrById(id);
					if(ChangnengUtil.isNull(enterpriseRandomAttrs)&& enterpriseRandomAttrs.size()==0){
						r.setCheckBoxStatus(0);//所有双随机属性都不选中
					}else{
						for(EnterpriseRandomDatabase e:enterpriseRandomAttrs){
							if(r.getAttrName().equals(e.getAttrName())){
								if(map.get(r.getAttrName())==null){
									map.put(r.getAttrName(), 1);
								}else{
									map.put(r.getAttrName(), Integer.valueOf(map.get(r.getAttrName()).toString())+1);
								}
							}
						}
					}
				}
			}else{//选中多条执法对象时
				for(String id:objIds){
					List<EnterpriseRandomDatabase> enterpriseRandomAttrs = randomPollutionSDBService.getEnterpriseRandomAttrById(id);
					if(ChangnengUtil.isNull(enterpriseRandomAttrs)&& enterpriseRandomAttrs.size()==0){
						r.setCheckBoxStatus(2);//有一个没查到，则必为半选中
					}else{
						for(EnterpriseRandomDatabase e:enterpriseRandomAttrs){
							if(r.getAttrName().equals(e.getAttrName())){
								if(map.get(r.getAttrName())==null){
									map.put(r.getAttrName(), 1);
								}else{
									map.put(r.getAttrName(), Integer.valueOf(map.get(r.getAttrName()).toString())+1);
								}
								
							}
						}
					}
				}
				
			}
			if(map.get(r.getAttrName())==null){
				r.setCheckBoxStatus(0);
			}else{
				if(Integer.valueOf(map.get(r.getAttrName()).toString())==chooseObjCount){
					r.setCheckBoxStatus(1);
				}else if(Integer.valueOf(map.get(r.getAttrName()).toString())==0){
					r.setCheckBoxStatus(0);
				}else{
					r.setCheckBoxStatus(2);
				}
			}
			
		}
		
		view.addObject("randomAttr",randomAttr);
		return view;
		
	} 

	/**
	 * 个人首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-individual")
	 */
	@RequestMapping(value = "/zfdx-individual", method = RequestMethod.POST)
	public ModelAndView zfdxIndividualForwardMethow(String back,String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-individual");
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session
					.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-individual".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		mav.addObject("menuId",menuId);
		return mav;
	}

	/**
	 * 个人，三无，小三产首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-individual-Three")
	 */
	@RequestMapping(value = "/zfdx-individual-Three", method = RequestMethod.POST)
	public ModelAndView zfdxIndividualThreeForwardMethow(String back,String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-individual-Three");
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session
					.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-individual-Three".equals(session
					.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		mav.addObject("menuId", menuId);
		return mav;
	}

	/**
	 * 自然保护区首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-natureReserve")
	 */
	@RequestMapping(value = "/zfdx-natureReserve", method = RequestMethod.POST)
	public ModelAndView zfdxNatureReserveForwardMethow(String back,String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-natureReserve");

		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-natureReserve".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		mav.addObject("menuId", menuId);
		return mav;
	}

	/**
	 * 无主对象首页管理
	 * 
	 * @param request
	 * @param response
	 * @return new ModelAndView("zfdx/zfdx-stray")
	 */
	@RequestMapping(value = "/zfdx-stray", method = RequestMethod.POST)
	public ModelAndView zfdxStrayForwardMethow(String back,String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-stray");

		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session
					.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/zfdx-stray".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		mav.addObject("menuId",menuId);
		return mav;
	}
	
	

	/**
	 * 跳转到执法对象查重页面
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/zfdx-add-enterprise")
	public ModelAndView toAddZfdxJspMethod(HttpServletRequest request,
			HttpServletResponse response) {
		ModelAndView mav = new ModelAndView("zfdx/zfdx-add-enterprise");
		return mav;
	}

	// 新增执法对象前的查询
	@RequestMapping("/zfdx-distinct-list")
	public ModelAndView toQueryDistinctMethod(HttpServletRequest request,
			HttpServletResponse response, ZfdxQueryExample query, Model model) {
		model.addAttribute("query", query);
		String flag = request.getParameter("flag");
		model.addAttribute("flagCHN", flag);
		List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.selectByLawEnforceObjectWithBLOBs(query);
		PageBean<LawEnforceObjectWithBLOBs> bean = new PageBean<>(list);
		ModelAndView mav = new ModelAndView();
		mav.addObject("pageBean", bean);
		mav.setViewName("zfdx/zfdx-add-enterprise");
		return mav;
	}

	// getIindustryType
	/**
	 * 根据门类信息查询
	 * 
	 * @param type
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/getIindustryType")
	@ResponseBody
	public List<IndustryType> getIindustryType(String industryLevel,
			String firstCalss) throws Exception {
		List<IndustryType> IndustryTypeList = zfdxManagerService.getIindustryType(industryLevel, firstCalss);
		return IndustryTypeList;
	}
	
	// getIindustryTypeById
	/**
	 * 根据id关联中类，小类
	 * 
	 * @param industryLevel
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/getIindustryTypeById")
	@ResponseBody
	public List<IndustryType> getIindustryTypeById(String industryLevel,
			String id) throws Exception {

		List<IndustryType> IndustryTypeList = zfdxManagerService.getIindustryTypeById(industryLevel, id);
		return IndustryTypeList;
	}

	/**
	 * 条件查询对象信息
	 * 
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @param seachObject
	 * @return
	 * @throws ParseException
	 */
	@RequestMapping(value = "/lawObjectList1", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<LawEnforceObjectWithBLOBs> lawObjectList1(
			HttpServletRequest request, HttpServletResponse response,
			String pageNum, String pageSize, SeachObjectBean seachObject,
			String status,String menuId) throws ParseException {
		seachObject.setObjectName(seachObject.getObjectName().trim());
		// 分页页数页数
		// 分页页数页数
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)) {
			pNum = Integer.parseInt(pageNum);
		}
		int pageSize11 = 10;
		if (pageSize != null && !"".equals(pageSize)) {
			pageSize11 = Integer.parseInt(pageSize);
		}

		// return new
		// PageBean<LawObjectJsonBean>(LawEnforceObjectMapper.lawListList(belongAreaId
		// ,arealevel,lawObjectType,code));
		//List<LawEnforceObjectWithBLOBs> jsonList = new ArrayList<LawEnforceObjectWithBLOBs>();

		PageBean<LawEnforceObjectWithBLOBs> pageBean = zfdxManagerService.lawListList(pNum, pageSize11, status, seachObject);
		List<LawEnforceObjectWithBLOBs> list = pageBean.getList();
		if (list != null && list.size() > 0) {
			for (LawEnforceObject lawEnforObject : list) {
				String city = lawEnforObject.getCity();
				String belongAreaId = lawEnforObject.getBelongAreaId();
				String belongAreaIdTemp = belongAreaId.substring(4);
			//	String arealevel = null;
			//	String code = belongAreaId.substring(0, 4);
//				if ("35000000".equals(belongAreaId)) {
//
//				}
//				if ("0000".equals(belongAreaIdTemp)
//						&& !"35000000".equals(belongAreaId)) {
//				}
				if (!"0000".equals(belongAreaIdTemp)
						&& !"35000000".equals(belongAreaId)) {
					String belongAreaName = lawEnforObject.getBelongAreaName();
					lawEnforObject.setBelongAreaName("" + city + belongAreaName);
				}
				if ("1".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
				} else if ("2".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setTypeCode("个人");

				} else if ("3".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setTypeCode("个体、三无、小三产");
				} else if ("4".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setTypeCode("自然保护区");
					
				} else if ("5".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setTypeCode("无主");
				}else if ("6".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setTypeCode("水源地");
				} /*else {
				}*/
/*				lawEnforObject.setObjectName("<a href ='#' style ='color:#6CCCF1;' onclick=checkObject(\'"
								+ lawEnforObject.getId()
								+ "\')> "
								+ lawEnforObject.getObjectName() + "</a>");
				lawEnforObject.setOperation("<a href ='#' style='color:#6CCCF1;cursor:pointer;' onclick=startTask(\'"
								+ lawEnforObject.getId()
								+ "\')><i class='fa fa-edit'></i>发起现场执法</a>");*/
			}
		}

		return pageBean;
	}

	/**
	 * 条件查询对象信息（所有）所有对象管理
	 * 
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @param seachObject
	 * @return
	 * @throws ParseException
	 */
	@RequestMapping(value = "/lawObjectList2", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<LawEnforceObjectWithBLOBs> lawObjectList2(
			HttpServletRequest request, HttpServletResponse response,
			String pageNum, String pageSize, SeachObjectBean seachObject,
			String status) throws ParseException {
		// 分页页数页数
		// 获取类型code 如果是企业类型，查询设置证件号为信用号
		System.out.println(seachObject);
		seachObject.setObjectName(seachObject.getObjectName().trim());
		String typeCode = seachObject.getTypeCode();
		if ("1".equals(typeCode)) {
			String cardNumber = seachObject.getCardNumber();
			if (cardNumber != null && !"".equals(cardNumber)) {
				seachObject.setSocialCreditCode(cardNumber);
				seachObject.setCardNumber(null);
			}
		}
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)) {
			pNum = Integer.parseInt(pageNum);
		}
		int pageSize11 = 10;
		if (pageSize != null && !"".equals(pageSize)) {
			pageSize11 = Integer.parseInt(pageSize);
		}
 
		List<LawObjectJsonBean> jsonList = new ArrayList<LawObjectJsonBean>();
		PageBean<LawEnforceObjectWithBLOBs> pageBean = zfdxManagerService.lawListList(pNum, pageSize11, status, seachObject);
		List<LawEnforceObjectWithBLOBs> list = pageBean.getList();
		if (list != null && list.size() > 0) {
			for (LawEnforceObject lawEnforObject : list) {
				String city = lawEnforObject.getCity();
				String belongAreaId = lawEnforObject.getBelongAreaId();
				
				String belongAreaIdTemp = belongAreaId.substring(4);
				String arealevel = null;
				String code = belongAreaId.substring(0, 4);
				if ("35000000".equals(belongAreaId)) {

				}
				if ("0000".equals(belongAreaIdTemp)
						&& !"35000000".equals(belongAreaId)) {
				}
				if (!"0000".equals(belongAreaIdTemp)
						&& !"35000000".equals(belongAreaId)) {
					String belongAreaName = lawEnforObject.getBelongAreaName();
					lawEnforObject.setBelongAreaName("" + city + belongAreaName);
				}
				if ("1".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardNumber(lawEnforObject.getSocialCreditCode());
					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
						lawEnforObject.setCardTypeName("统一社会信用代码");
					}
				} else if ("2".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardTypeName(lawEnforObject.getPersoncardTypeName());
				} else if ("3".equals(lawEnforObject.getTypeCode())) {
				} else if ("4".equals(lawEnforObject.getTypeCode())||"6".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardNumber(lawEnforObject.getOrgCode());
					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
						lawEnforObject.setCardTypeName("管理机构统一社会信用代码");
					}
				} else if ("5".equals(lawEnforObject.getTypeCode())) {
				} else {
				}
			}
			
		}
		return pageBean;
	}
	
	/**
	 * 失信被执行人列表集合
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @param seachObject
	 * @param status
	 * @return
	 * @throws ParseException
	 * <AUTHOR>
	 * @date 2018年8月22日-下午5:40:40
	 */
	@RequestMapping(value = "/law-supervise-list", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<LawEnforceObjectWithBLOBs> lawSuperviseList(HttpServletRequest request,HttpServletResponse response, String pageNum, String pageSize, SeachObjectBean seachObject, String status) 
			throws ParseException {
		// 分页页数页数
		// 获取类型code 如果是企业类型，查询设置证件号为信用号
		seachObject.setObjectName(seachObject.getObjectName().trim());
		String typeCode = seachObject.getTypeCode();
		if ("1".equals(typeCode)) {
			String cardNumber = seachObject.getCardNumber();
			if (cardNumber != null && !"".equals(cardNumber)) {
				seachObject.setSocialCreditCode(cardNumber);
				seachObject.setCardNumber(null);
			}
		}
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)) {
			pNum = Integer.parseInt(pageNum);
		}
		int pageSize11 = 10;
		if (pageSize != null && !"".equals(pageSize)) {
			pageSize11 = Integer.parseInt(pageSize);
		}
		List<LawObjectJsonBean> jsonList = new ArrayList<LawObjectJsonBean>();
		PageBean<LawEnforceObjectWithBLOBs> pageBean = zfdxManagerService.lawSuperviseList(pNum, pageSize11, status, seachObject);
		List<LawEnforceObjectWithBLOBs> list = pageBean.getList();
		if (list != null && list.size() > 0) {
			for (LawEnforceObject lawEnforObject : list) {
				String city = lawEnforObject.getCity();
				String belongAreaId = lawEnforObject.getBelongAreaId();
				String belongAreaIdTemp = belongAreaId.substring(4);
				String arealevel = null;
				String code = belongAreaId.substring(0, 4);
				if (!"0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					String belongAreaName = lawEnforObject.getBelongAreaName();
					lawEnforObject.setBelongAreaName(city + belongAreaName);
				}
				if ("1".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardNumber(lawEnforObject.getSocialCreditCode());
					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
						lawEnforObject.setCardTypeName("统一社会信用代码");
					}
				} else if ("2".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardTypeName(lawEnforObject.getPersoncardTypeName());
				} else if ("3".equals(lawEnforObject.getTypeCode())) {

				} else if ("4".equals(lawEnforObject.getTypeCode())||"6".equals(lawEnforObject.getTypeCode())) {
					lawEnforObject.setCardNumber(lawEnforObject.getOrgCode());
					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
						lawEnforObject.setCardTypeName("管理机构统一社会信用代码");
					}
				} else if ("5".equals(lawEnforObject.getTypeCode())) {

				} else {

				}
			}
		}
		return pageBean;
	}

	/**
	 * 失信历史表
	 * @param model
	 * @param lawId
	 * @param pageNum
	 * @param pageSize
	 * @param request
	 * @param response
	 * @return
	 * <AUTHOR>
	 * @date 2018年8月23日-上午11:28:32
	 */
	@RequestMapping(value = "/law-supervise-history", method = RequestMethod.POST)
	@ResponseBody
	public  PageBean<PPunishmentHistory>  historyFlow(Model model, String lawId,Integer pageNum,Integer pageSize, HttpServletRequest request,
			HttpServletResponse response){
		PageBean<PPunishmentHistory>   historyList  =  zfdxManagerService.lawSuperviseHistory(lawId, pageNum, pageSize);
		return historyList;
	}
 
	
	/**
	 * 单独一个失信信息
	 * @param model
	 * @param request
	 * @param response
	 * @param infoid
	 * @return
	 * <AUTHOR>
	 * @date 2018年8月23日-下午2:07:37
	 */
	@RequestMapping(value = "/law-punishment")
	public ModelAndView getOnePPunishmentHistory(Model model,HttpServletRequest request, HttpServletResponse response,String infoid){
		ModelAndView view=new ModelAndView("zfdx/model/punishmentModel");
		PPunishmentHistory   historyList  =  ppMapper.selectByPrimaryKey(infoid);
		view.addObject("pPunishmentOneBean",historyList);
		return view;
	}
	/**
	 * 根据id批量删除对象信息
	 * 
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	// delObjectByIds
	@RequestMapping(value = "/delObjectByIds")
	@ResponseBody
	public ResponseJson delObjectByIds(String ids) throws Exception {

		try {
			
			ResponseJson json = zfdxManagerService.delObjectByIds(ids);
			return json;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;

	}

	/**
	 * 根据id查询详细信息
	 * 
	 * @param request
	 * @param response
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping("/detailedInformationObject")
	public ModelAndView detailedInformationObject(HttpServletRequest request,
			HttpServletResponse response, String startTimes, String endTimes, String back,String menuId,
			String id, String typeCode, String menuIdAll,Model model,String case1,String standenterid) {
		ModelAndView mav = new ModelAndView();
		try {
			LawEnforceObjectWithBLOBs lawObject = new LawEnforceObjectWithBLOBs();
			List<EnvironmentChickTaskBean> taskList = null;
 			if(!ChangnengUtil.isNull(case1) && "FF".equals(case1)) {
				if(ChangnengUtil.isNull(standenterid)) {
					mav.setViewName("error/500");					
					return mav;
				}
				lawObject = lawEnforceObjectMapper.selectByStandenterid(standenterid);
				if(ChangnengUtil.isNull(lawObject)) {
					mav.setViewName("error/500");
					return mav;
				}else {
					typeCode = lawObject.getTypeCode();
				}
			}
 			if(!ChangnengUtil.isNull(case1) && "ZHJG".equals(case1)) {
				if(ChangnengUtil.isNull(standenterid)) {
					mav.setViewName("error/500");					
					return mav;
				}
				lawObject = lawEnforceObjectMapper.selectByPrimaryKey(standenterid);
				if(ChangnengUtil.isNull(lawObject)) {
					mav.setViewName("error/500");
					return mav;
				}else {
					typeCode = lawObject.getTypeCode();
				}
			}else {
				if (!"".equals(id) && id != null) {
					lawObject = zfdxManagerService.selectByPrimaryKey(id);
					// 根据对象的id条件查询任务信息
				}
			}
 			SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
 			if(lawObject.getStartDate()!=null)lawObject.setStartDateStr(sdf.format(lawObject.getStartDate()));
 			if(lawObject.getEndDate()!=null)lawObject.setEndDateStr(sdf.format(lawObject.getEndDate()));
			model.addAttribute("lawObject", lawObject);
			mav.addObject("randomAttrNames", randomPollutionSDBService.selectNamesByObjId(id));
			mav.addObject("pollutionSourceTypes", tcDictionaryMapper.getTcDictionaryListByCode("POLLUTION_SOURCE_TYPE", null));
			
			if ("1".equals(typeCode)) {
				// 企业
				mav.setViewName("zfdx/zfdx-jbxx-qsy");
			} else if ("2".equals(typeCode)) {
				// 个人
				mav.setViewName("zfdx/zfdx-jbxx-gr");
			} else if ("3".equals(typeCode)) {
				// 个人、三无、小三产
				mav.setViewName("zfdx/zfdx-jbxx-gt");
			} else if ("4".equals(typeCode)) {
				// 自然保护区
				mav.setViewName("zfdx/zfdx-jbxx-bhq");
			} else if ("5".equals(typeCode)) {
				// 无主对象
				mav.setViewName("zfdx/zfdx-jbxx-grwz");
			} else if("6".equals(typeCode)){
				//水源地
				mav.setViewName("zfdx/zfdx-jbxx-wyd");
			}
		
			// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
			if ("1".equals(back)) {
				HttpSession session = request.getSession();
				Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
				// ----------------------把行政区划按指定顺序传给前台------------------
				Map<String, String> linkedParams = new LinkedHashMap<>();
				for (String key : paramsMap.keySet()) {
					if (!"belongProvince".equals(key) && !"belongCity".equals(key)
							&& !"belongCountry".equals(key)
							&& !"powerProvince".equals(key)
							&& !"powerCity".equals(key)
							&& !"powerCountry".equals(key)) {
						linkedParams.put(key, paramsMap.get(key));
					}
				}
				linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
				linkedParams.put("belongCity", paramsMap.get("belongCity"));
				linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
				linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
				linkedParams.put("powerCity", paramsMap.get("powerCity"));
				linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
				// -------------------------结束---------------------------------------
				// 防止多开窗口param混乱
				if (!"/zfdx/zfdx-all".equals(session.getAttribute("preUrl"))) {
					return mav;
				}
				String params = JacksonUtils.toJsonString(linkedParams);
				System.out.println(linkedParams.toString());
				mav.addObject("params", params);
				session.removeAttribute("paramsInSession");
			}
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
			mav.addObject("res",res);
			mav.addObject("menuId",menuId);
			mav.addObject("menuIdAll",menuIdAll);
		} catch (Exception e) {
			e.printStackTrace();
		}
			System.out.println("-----------------------控制层跳转视图----------------------------------------------");
		return mav;
	}

	/**
	 * 查询对象信息和任务信息
	 * 
	 * @param request
	 * @param response
	 * @param startTimes
	 * @param endTimes
	 * @param id
	 * @return
	 */
	@RequestMapping("/lawObjectByTaskJosn")
	@ResponseBody
	public ChickObjectBean detailedInformationObject(
			HttpServletRequest request, HttpServletResponse response,
			String startTimes, String endTimes, String id) {
		LawEnforceObjectWithBLOBs lawObject = new LawEnforceObjectWithBLOBs();
		List<EnvironmentChickTaskBean> taskList = new ArrayList<EnvironmentChickTaskBean>();
		ChickObjectBean bean = new ChickObjectBean();
		if (!"".equals(id) && id != null) {
			// 根据对象的id条件查询任务信息IndustryTypeChoose4CE20D454EA27D54E055000000000001
			taskList = zfdxManagerService.getTaskList(id, startTimes, endTimes);
		}
		Set<String> yearList = new LinkedHashSet<String>();
		Set<String> mounthList = new LinkedHashSet<String>();
		if (taskList != null) {
			for (EnvironmentChickTaskBean list : taskList) {
				Date processingTime = list.getProcessingTime();
				String year = null;
				String month = null;
				Map map = new HashMap<String, String>();
				SimpleDateFormat formatData = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				if (processingTime != null) { 
					String format = DateUtil.format(processingTime);
					year = format.substring(0, 4);
					month = (String) format.subSequence(5, 7);
					list.setYear(year);
					yearList.add(year);
					list.setYearMonth(year + month);
					mounthList.add(year + month);
					String date = formatData.format(processingTime);
					list.setStartTime(date);
				} else {
					list.setStartTime("空");
				}
				Date endDate = list.getEndDate();
				if (endDate != null && !"".equals(endDate)) {
					String date = formatData.format(endDate);
					list.setEndTime(date);
				} else {
					list.setEndTime("空");
				}
				if (list.getTaskFromName() == null
						|| "".equals(list.getTaskFromName())) {
					list.setTaskFromName("空");
				}
				list.setYearList(yearList);
				list.setMounthList(mounthList);
				list.setStatus("0");
			}
		}
		bean.setMounthList(mounthList);
		bean.setYearList(yearList);
		bean.setTaskList(taskList);
		return bean;
	}

	/**
	 * 导出execl表
	 * 
	 * @param request
	 * @param workbook
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/downExcel")
	public ModelAndView downDanWeiExcel(HttpServletRequest request,SeachObjectBean seachObject,
			HSSFWorkbook workbook, String type, HttpServletResponse response) {
		try {
			ViewExcel viewExcel = new ViewExcel();
			Map<String, Object> model = new HashMap<String, Object>();
			// 表头
			if ("1".equals(seachObject.getTypeCode())) {
				// 企业类型
				String[] columnNames = {"对象类别","污染源编码","名称","执法对象所在行政区","地址","权属行政区","地理坐标","营业执照证件号","组织机构代码",  "统一社会信用代码","监管级别", "行业类型","污染源类别",
						"法人身份证号","法人电话",
						"法人代表","环保负责人","环保负责人身份证号","环保负责人电话","是否重点源","适用排污许可行业技术规范","是否属于“小散乱污”企业","是否在线监控企业","生产状态","是否废水产生单位","是否废气产生单位","是否危废产生单位","是否危废经营单位","是否注销","是否固废经营单位","是否固废生产单位","是否风险源","排污口是否规范化",
						"当事人性质","所属级别","是否上市公司","股票代码","是否所属集团公司","所属集团公司名称","所属集团公司组织结构代码","所属集团公司股票代码","对象介绍","双随机属性","所属流域代码","所属流域名称",
						};
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = {"typeCode","objectNumber","objectName", "belongAreaName","address","powerAreaName","gisCoordinateXY","licenseNo","orgCode","socialCreditCode", "levelName", "industryTypeName","pollutionSourceTypeName",
						"legalManIdCard","legalPhone",
						"legalPerson","chargePerson","chargeManIdCard","chargePersonPhone","isKeySourceStr","sypwxkhyjsgfName","xslw","isOnlineMonitorStr","productStateName","sffscsdw","sffqcsdw","sfwfcsdw","sfwfjydw","isCancelled","isSolidwasteOperunitStr","isSolidwasteCreateunitStr","isRiskSourceStr","isOutfallStandardStr",
						"personNatureName","belongLevelName","isListedCompanyStr","stockCode","isGroupCompanyStr","groupCompanyName","groupCompanyOrgcode","groupCompanyStockcode","objectDesc","randomAttrName","WSCD","WSNM"
						};
				model.put("dbColumnNames", dbColumnNames);

			   //List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				//PageBean<LawEnforceObjectWithBLOBs> pageBean = zfdxManagerService.lawListList(pNum, pageSize11, status, seachObject);
				
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						
						lawObjectList.setTypeCode("企事业单位");
						if(lawObjectList.getIsListedCompany()!=null){
							if(1==lawObjectList.getIsListedCompany()){
								//isListedCompanyStr
								lawObjectList.setIsListedCompanyStr("是");
							}else if(0==lawObjectList.getIsListedCompany()){
								lawObjectList.setIsListedCompanyStr("否");
							}
						}
						if(lawObjectList.getIsKeySource()!=null){
							if(1==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("是");
							}else if (0==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("否");
							}
						}
						if(lawObjectList.getIsOnlineMonitor()!=null){
							if(1==lawObjectList.getIsOnlineMonitor()){
								lawObjectList.setIsOnlineMonitorStr("是");
							}else if(0==lawObjectList.getIsOnlineMonitor()){
								lawObjectList.setIsOnlineMonitorStr("否");
							}
						}
						if(lawObjectList.getIsSolidwasteOperunit()!=null){
							if(1==lawObjectList.getIsSolidwasteOperunit()){
								lawObjectList.setIsSolidwasteOperunitStr("是");
							}else if(0==lawObjectList.getIsSolidwasteOperunit()){
								lawObjectList.setIsSolidwasteOperunitStr("否");
							}
						}
						if(lawObjectList.getIsSolidwasteCreateunit()!=null){
							if(lawObjectList.getIsSolidwasteCreateunit()==1){
								lawObjectList.setIsSolidwasteCreateunitStr("是");
							}else if(lawObjectList.getIsSolidwasteCreateunit()==0){
								lawObjectList.setIsSolidwasteCreateunitStr("否");
							}
						}
						if(lawObjectList.getIsGroupCompany()!=null){
							if(lawObjectList.getIsGroupCompany()==1){
								lawObjectList.setIsGroupCompanyStr("是");
							}else if(lawObjectList.getIsGroupCompany()==0){
								lawObjectList.setIsGroupCompanyStr("否");
							}
						}
						if(lawObjectList.getIsOutfallStandard()!=null){
							if(lawObjectList.getIsOutfallStandard()==1){
								lawObjectList.setIsOutfallStandardStr("是");
							}else if(lawObjectList.getIsOutfallStandard()==0){
								lawObjectList.setIsOutfallStandardStr("否");
							}
						}
						if(lawObjectList.getIsRiskSource()!=null){
							if(lawObjectList.getIsRiskSource()==1){
								lawObjectList.setIsRiskSourceStr("是");
							}else if(lawObjectList.getIsRiskSource()==0){
								lawObjectList.setIsRiskSourceStr("否");
							}
						}
						if(lawObjectList.getXslw()!=null){
							if("1".equals(lawObjectList.getXslw())){
								lawObjectList.setXslw("是");
							}else{
								lawObjectList.setXslw("否");
							}
						}
						if(lawObjectList.getSffscsdw()!=null){
							if("1".equals(lawObjectList.getSffscsdw())){
								lawObjectList.setSffscsdw("是");
							}else{
								lawObjectList.setSffscsdw("否");
							}
						}
						if(lawObjectList.getSffqcsdw()!=null){
							if("1".equals(lawObjectList.getSffqcsdw())){
								lawObjectList.setSffqcsdw("是");
							}else{
								lawObjectList.setSffqcsdw("否");
							}
						}
						if(lawObjectList.getSfwfcsdw()!=null){
							if("1".equals(lawObjectList.getSfwfcsdw())){
								lawObjectList.setSfwfcsdw("是");
							}else{
								lawObjectList.setSfwfcsdw("否");
							}
						}
						if(lawObjectList.getSfwfjydw()!=null){
							if("1".equals(lawObjectList.getSfwfjydw())){
								lawObjectList.setSfwfjydw("是");
							}else{
								lawObjectList.setSfwfjydw("否");
							}
						}
						if(lawObjectList.getIsCancelled()!=null){
							if("1".equals(lawObjectList.getIsCancelled())){
								lawObjectList.setIsCancelled("是");
							}else{
								lawObjectList.setIsCancelled("否");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");
					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "企事业单位.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1");
			} else if ("2".equals(seachObject.getTypeCode())) {
				String[] columnNames = {"对象类别","污染源编码" ,"姓名",  "性别","执法对象所在行政区",
						"地址","权属行政区","地理坐标","身份证类型","身份证号","联系人","联系方式","是否重点源","所属流域代码","所属流域名称",
						"污染源类别","适用排污许可行业技术规范","是否属于“小散乱污”企业","是否注销" };
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = { "typeCode","objectNumber","objectName","sexStr","belongAreaName",
						"address","powerAreaName","gisCoordinateXY","personcardTypeName","cardNumber","legalPerson","legalPhone","isKeySourceStr","WSCD","WSNM","pollutionSourceTypeName","sypwxkhyjsgfName","xslw","isCancelled"};
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				
				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						lawObjectList.setTypeCode("个人");
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						if(lawObjectList.getSex()!=null){
						if(lawObjectList.getSex() ==1){
							lawObjectList.setSexStr("男");
						}else if(lawObjectList.getSex() ==0){
							lawObjectList.setSexStr("女");
						}
						}
						if(lawObjectList.getIsKeySource()!=null){
							if(1==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("是");
							}else if (0==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("否");
							}
						}
						if(lawObjectList.getXslw()!=null){
							if("1".equals(lawObjectList.getXslw())){
								lawObjectList.setXslw("是");
							}else{
								lawObjectList.setXslw("否");
							}
						}
						if(lawObjectList.getIsCancelled()!=null){
							if("1".equals(lawObjectList.getIsCancelled())){
								lawObjectList.setIsCancelled("是");
							}else{
								lawObjectList.setIsCancelled("否");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "个人.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1");

				// 个体、三无、小三产
			} else if ("3".equals(seachObject.getTypeCode())) {
				String[] columnNames = {  "对象类别","污染源编码","名称",
						"执法对象所在行政区","地址","权属行政区","地理坐标","证件类型","证件号码","联系人","联系方式","是否重点源","双随机属性","所属流域代码","所属流域名称",
						"污染源类别","适用排污许可行业技术规范","是否属于“小散乱污”企业","是否注销"};
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = {  "typeCode","objectNumber","objectName",
						"belongAreaName","address","powerAreaName","gisCoordinateXY","cardTypeName","cardNumber","legalPerson","legalPhone","isKeySourceStr","randomAttrName","WSCD","WSNM",
						"pollutionSourceTypeName","sypwxkhyjsgfName","xslw","isCancelled"
					 };
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				
				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						lawObjectList.setTypeCode("个体、三无、小三产");
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						
						if(lawObjectList.getIsKeySource()!=null){
							if(1==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("是");
							}else if (0==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("否");
							}
						}
						if(lawObjectList.getXslw()!=null){
							if("1".equals(lawObjectList.getXslw())){
								lawObjectList.setXslw("是");
							}else{
								lawObjectList.setXslw("否");
							}
						}
						if(lawObjectList.getIsCancelled()!=null){
							if("1".equals(lawObjectList.getIsCancelled())){
								lawObjectList.setIsCancelled("是");
							}else{
								lawObjectList.setIsCancelled("否");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "个体、三无、小三产.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1");

				// 个体
			} else if ("4".equals(seachObject.getTypeCode())) {
				String[] columnNames = { "对象类别","污染源编码", "名称",
						"执法对象所在行政区","地址","权属行政区","地理坐标","保护区范围","主要保护对象","是否设立专门的管理机构","管理机构名称","管理机构组织机构代码","管理机构统一社会信用代码","管理机构负责人","管理机构负责人联系方式","联系人","联系方式","是否重点源","所属流域代码","所属流域名称","是否注销"
					};
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = {  "typeCode","objectNumber","objectName",
						"belongAreaName","address","powerAreaName","gisCoordinateXY","protectedAreaDesc","mainProtectObject","isSpecialOrgStr","manageOrgName","orgCode","socialCreditCode","chargePerson","chargePersonPhone","legalPerson","legalPhone","isKeySourceStr","WSCD","WSNM","isCancelled"
						 };
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				
				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						lawObjectList.setTypeCode("4".equals(seachObject.getTypeCode())?"自然保护区":"水源地");
						if(lawObjectList.getIsKeySource()!=null){
							if(1==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("是");
							}else if (0==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("否");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");					if(lawObjectList.getIsSpecialOrg()!=null){
							if(1==lawObjectList.getIsSpecialOrg()){
								lawObjectList.setIsSpecialOrgStr("是");
							}else if(0==lawObjectList.getIsSpecialOrg()){
								lawObjectList.setIsSpecialOrgStr("否");
							}
						}
						if(lawObjectList.getIsCancelled()!=null){
							if("1".equals(lawObjectList.getIsCancelled())){
								lawObjectList.setIsCancelled("是");
							}else{
								lawObjectList.setIsCancelled("否");
							}
						}
					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "4".equals(seachObject.getTypeCode())?"自然保护区.xls":"水源地.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1newLawObjectPage");

				// 无主
			} else if ("5".equals(seachObject.getTypeCode())) {
				String[] columnNames = {"对象类型","污染源编码", "名称", 
						"执法对象所在行政区","地址","权属行政区","地理坐标","证件类型","证件号码","联系人","联系方式","是否重点源","双随机属性","所属流域代码","所属流域名称",
						"污染源类别","适用排污许可行业技术规范","是否属于“小散乱污”企业","是否注销"};
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = { "typeCode","objectNumber","objectName",
						"belongAreaName","address","powerAreaName","gisCoordinateXY","cardTypeName","cardNumber","legalPerson","legalPhone","isKeySourceStr","randomAttrName","WSCD","WSNM",
						"pollutionSourceTypeName","sypwxkhyjsgfName","xslw","isCancelled"
						 };
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						lawObjectList.setTypeCode("无主");
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						if(lawObjectList.getIsKeySource()!=null){
							if(1==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("是");
							}else if (0==lawObjectList.getIsKeySource()){
								lawObjectList.setIsKeySourceStr("否");
							}
						}
						if(lawObjectList.getXslw()!=null){
							if("1".equals(lawObjectList.getXslw())){
								lawObjectList.setXslw("是");
							}else{
								lawObjectList.setXslw("否");
							}
						}
						if(lawObjectList.getIsCancelled()!=null){
							if("1".equals(lawObjectList.getIsCancelled())){
								lawObjectList.setIsCancelled("是");
							}else{
								lawObjectList.setIsCancelled("否");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "无主对象.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1");

				// 无主
			}else if ("6".equals(seachObject.getTypeCode())) {
				String[] columnNames = { "对象类别","污染源编码", "名称",
						"执法对象所在行政区","地址","权属行政区","地理坐标","水源地编码","水源地级别","水源地类别","实际取水量（吨/日）","实际供水人口（人）","是否划定保护区","划定批复文件文号","是否设立界标标志","责任单位","责任人","联系电话","信息公开网址","备注"
				};
				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = {  "typeCode","objectNumber","objectName",
						"belongAreaName","address","powerAreaName","gisCoordinateXY","waterSourceCode","waterSourceLevel","waterSourceCategory","saterIntake","waterSupply","isReserve","delimitFileCode","isLandmark","manageOrgName","chargePerson","legalPhone","website","remarks"
				};
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);

				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						lawObjectList.setTypeCode("4".equals(seachObject.getTypeCode())?"自然保护区":"水源地");
						if(lawObjectList.getWaterSourceLevel()!=null){
							if("1".equals(lawObjectList.getWaterSourceLevel())){
								lawObjectList.setWaterSourceLevel("千吨万人");
							}else if("2".equals(lawObjectList.getWaterSourceLevel())){
								lawObjectList.setWaterSourceLevel("千人以上");
							}else if("3".equals(lawObjectList.getWaterSourceLevel())){
								lawObjectList.setWaterSourceLevel("20人(含)至千人");
							}else if("4".equals(lawObjectList.getWaterSourceLevel())){
								lawObjectList.setWaterSourceLevel("20人以下");
							}
						}
						lawObjectList.setGisCoordinateXY(lawObjectList.getGisCoordinateX()+"E "+lawObjectList.getGisCoordinateY()+"N");
						if(lawObjectList.getWaterSourceCategory()!=null){
							if("1".equals(lawObjectList.getWaterSourceCategory())){
								lawObjectList.setWaterSourceCategory("湖库型");
							}else if("2".equals(lawObjectList.getWaterSourceCategory())){
								lawObjectList.setWaterSourceCategory("河流型");
							}else if("3".equals(lawObjectList.getWaterSourceCategory())){
								lawObjectList.setWaterSourceCategory("水库型");
							}else if("4".equals(lawObjectList.getWaterSourceCategory())){
								lawObjectList.setWaterSourceCategory("地下水型");
							}
						}
						if(lawObjectList.getIsReserve()!=null){
							if("1".equals(lawObjectList.getIsReserve())){
								lawObjectList.setIsReserve("一级");
							}else if("2".equals(lawObjectList.getIsReserve())){
								lawObjectList.setIsReserve("二级");
							}else if("3".equals(lawObjectList.getIsReserve())){
								lawObjectList.setIsReserve("一级/二级");
							}else if("4".equals(lawObjectList.getIsReserve())){
								lawObjectList.setIsReserve("保护范围");
							}else if("5".equals(lawObjectList.getIsReserve())){
								lawObjectList.setIsReserve("未划定");
							}
						}
						if(lawObjectList.getIsLandmark()!=null){
							if("1".equals(lawObjectList.getIsLandmark())){
								lawObjectList.setIsLandmark("是");
							}else if("0".equals(lawObjectList.getIsLandmark())){
								lawObjectList.setIsLandmark("否");
							}
						}
					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "4".equals(seachObject.getTypeCode())?"自然保护区.xls":"水源地.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1newLawObjectPage");

				// 无主
			} else {
				// String[] columnNames =
				// {"名称","类型","证件号","监管级别","联系人","联系方式","所在行政区","权属行政区"};
				String[] columnNames = { "名称", "类型","证照名称", "证照号", "联系人", "联系方式",
						"所在行政区", "权属行政区","双随机属性","所属流域代码","所属流域名称", };

				model.put("columnNames", columnNames);
				// list集合对应的值
				String[] dbColumnNames = { "objectName", "typeCode","cardTypeName",
						"cardNumber", "legalPerson", "legalPhone",
						"belongAreaName", "powerAreaName","randomAttrName","WSCD","WSNM" };
				// String[] dbColumnNames = {"objectName",
				// "typeCode","cardNumber","levelName","legalPerson","legalPhone","belongAreaName","powerAreaName"};
				model.put("dbColumnNames", dbColumnNames);
				List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.dowmExeclBySerach(seachObject);
				
				//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
				if (list != null && list.size() > 0) {
					for (LawEnforceObject lawObjectList : list) {
						String city = lawObjectList.getCity();
						String belongAreaId = lawObjectList.getBelongAreaId();
						if(!ChangnengUtil.isNull(belongAreaId)){
							String belongAreaIdTemp = belongAreaId.substring(4);
							String arealevel = null;
							String code = belongAreaId.substring(0, 4);
							if (!"0000".equals(belongAreaIdTemp)
									&& !"35000000".equals(belongAreaId)) {
								String belongAreaName = lawObjectList.getBelongAreaName();
								lawObjectList.setBelongAreaName("" + city + belongAreaName);
							}
						}
						if ("1".equals(lawObjectList.getTypeCode())) {
							// 企业
							lawObjectList.setTypeCode("企事业单位");
							if(!ChangnengUtil.isNull(lawObjectList.getSocialCreditCode())){
								lawObjectList.setCardTypeName("统一社会信用代码");
								lawObjectList.setCardNumber(lawObjectList.getSocialCreditCode());
							}
							lawObjectList.setLegalPerson(lawObjectList.getChargePerson());
							lawObjectList.setLegalPhone(lawObjectList.getChargePersonPhone());
						} else if ("2".equals(lawObjectList.getTypeCode())) {
							// 个人
							lawObjectList.setCardTypeName(lawObjectList.getPersoncardTypeName());
							lawObjectList.setTypeCode("个人");
						} else if ("3".equals(lawObjectList.getTypeCode())) {
							// 个体
							lawObjectList.setTypeCode("个体、三无、小三产");
						} else if ("4".equals(lawObjectList.getTypeCode())) {
							// 自然保护区
							if(!ChangnengUtil.isNull(lawObjectList.getCardNumber())){
								lawObjectList.setTypeCode("自然保护区");
							}
							if(!ChangnengUtil.isNull(lawObjectList.getSocialCreditCode())){
								lawObjectList.setCardTypeName("管理机构统一社会信用代码");
								lawObjectList.setCardNumber(lawObjectList.getSocialCreditCode());
							}
						} else if ("5".equals(lawObjectList.getTypeCode())) {
							// 无主
							lawObjectList.setTypeCode("无主");
						}else if("6".equals(lawObjectList.getTypeCode())){
							lawObjectList.setTypeCode("水源地");
						}
					}
				}
				model.put("list", list);
				// excel文件的名称
				model.put("excelName", "所有对象列表.xls");
				// excel 文件的sheet
				model.put("sheetName", "sheet1");
			}
			// 标记序号
			model.put("flag", true);
			viewExcel.buildExcelDocument(model, workbook, request, response);
			return new ModelAndView(new ViewExcel(), model);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/downLoseCreditExcel")
	public ModelAndView downLoseCreditExcel(HttpServletRequest request,SeachObjectBean seachObject,
			HSSFWorkbook workbook, String type, HttpServletResponse response) {
		try {
			ViewExcel viewExcel = new ViewExcel();
			Map<String, Object> model = new HashMap<String, Object>();
			String[] columnNames = { "名称", "类型","证照名称", "证照号", "所在行政区", "权属行政区","双随机属性" ,"纳入重点监管日期"};

			model.put("columnNames", columnNames);
			// list集合对应的值
			String[] dbColumnNames = { "objectName", "typeCode","cardTypeName",
					"cardNumber", "belongAreaName", "powerAreaName","randomAttrName","superviseDate"};
			model.put("dbColumnNames", dbColumnNames);
			// 获取类型code 如果是企业类型，查询设置证件号为信用号
			seachObject.setObjectName(seachObject.getObjectName().trim());
			String typeCode = seachObject.getTypeCode();
			if ("1".equals(typeCode)) {
				String cardNumber = seachObject.getCardNumber();
				if (cardNumber != null && !"".equals(cardNumber)) {
					seachObject.setSocialCreditCode(cardNumber);
					seachObject.setCardNumber(null);
				}
			}
			List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downLoseCreditSearch(seachObject);
			
			//List<LawEnforceObjectWithBLOBs> list = zfdxManagerService.downExcelByCodeType(type);
			if (list != null && list.size() > 0) {
				for (LawEnforceObject lawObjectList : list) {
					String city = lawObjectList.getCity();
					String belongAreaId = lawObjectList.getBelongAreaId();
					if(!ChangnengUtil.isNull(belongAreaId)){
						String belongAreaIdTemp = belongAreaId.substring(4);
						String arealevel = null;
						String code = belongAreaId.substring(0, 4);
						if (!"0000".equals(belongAreaIdTemp)
								&& !"35000000".equals(belongAreaId)) {
							String belongAreaName = lawObjectList.getBelongAreaName();
							lawObjectList.setBelongAreaName("" + city + belongAreaName);
						}
					}
					if ("1".equals(lawObjectList.getTypeCode())) {
						// 企业
						lawObjectList.setTypeCode("企事业单位");
						if(!ChangnengUtil.isNull(lawObjectList.getSocialCreditCode())){
							lawObjectList.setCardTypeName("统一社会信用代码");
							lawObjectList.setCardNumber(lawObjectList.getSocialCreditCode());
						}
						lawObjectList.setLegalPerson(lawObjectList.getChargePerson());
						lawObjectList.setLegalPhone(lawObjectList.getChargePersonPhone());
					} else if ("2".equals(lawObjectList.getTypeCode())) {
						// 个人
						lawObjectList.setCardTypeName(lawObjectList.getPersoncardTypeName());
						lawObjectList.setTypeCode("个人");
					} else if ("3".equals(lawObjectList.getTypeCode())) {
						// 个体
						lawObjectList.setTypeCode("个体、三无、小三产");
					} else if ("4".equals(lawObjectList.getTypeCode())) {
						// 自然保护区
						if(!ChangnengUtil.isNull(lawObjectList.getCardNumber())){
							lawObjectList.setTypeCode("自然保护区");
						}
						if(!ChangnengUtil.isNull(lawObjectList.getSocialCreditCode())){
							lawObjectList.setCardTypeName("管理机构统一社会信用代码");
							lawObjectList.setCardNumber(lawObjectList.getSocialCreditCode());
						}
					} else if ("5".equals(lawObjectList.getTypeCode())) {
						// 无主
						lawObjectList.setTypeCode("无主");
					}else if("6".equals(lawObjectList.getTypeCode())){
						lawObjectList.setTypeCode("水源地");
					}
				}
			}
			model.put("list", list);
			// excel文件的名称
			model.put("excelName", "失信被执行人清单.xls");
			// excel 文件的sheet
			model.put("sheetName", "sheet1");
			// 标记序号
			model.put("flag", true);
			viewExcel.buildExcelDocument(model, workbook, request, response);
			return new ModelAndView(new ViewExcel(), model);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return null;
		
		
	}
	private void setLawObjectCode(LawEnforceObjectWithBLOBs record) {
		if (record != null) {
			// 监管级别: levelCode
			String levelCode = record.getLevelCode();
			if (levelCode != null && !"".equals(levelCode)) {
				if ("1".equals(levelCode)) {
					record.setLevelName("国控");
				} else if ("2".equals(levelCode)) {
					record.setLevelName("省控");
				} else if ("3".equals(levelCode)) {
					record.setLevelName("市控");
				} else if ("4".equals(levelCode)) {
					record.setLevelName("县控");
				} else if ("5".equals(levelCode)) {
					record.setLevelName("非控");
				}
			}
			// belongLevelCode所属级别
			String belongLevelCode = record.getBelongLevelCode();
			if (belongLevelCode != null && !"".equals(belongLevelCode)) {
				if ("1".equals(belongLevelCode)) {
					record.setBelongLevelName("央属");
				} else if ("2".equals(belongLevelCode)) {
					record.setBelongLevelName("省属");
				} else if ("3".equals(belongLevelCode)) {
					record.setBelongLevelName("市属");
				}
			}
			// 生产状态: productStateCode
			String productStateCode = record.getProductStateCode();
			if (productStateCode != null && !"".equals(productStateCode)) {
				if ("1".equals(productStateCode)) {
					record.setProductStateName("在建未投产");
				} else if ("2".equals(productStateCode)) {
					record.setProductStateName("正常生产");
				} else if ("3".equals(productStateCode)) {
					record.setProductStateName("长时间停产");
				}else if ("4".equals(productStateCode)) {
					record.setProductStateName("已关停");
				}else if ("5".equals(productStateCode)) {
					record.setProductStateName("不存在");
				}
				else if ("6".equals(productStateCode)) {
					record.setProductStateName("重复企业");
				}
				else if ("7".equals(productStateCode)) {
					record.setProductStateName("临时性停产");
				}else if("8".equals(productStateCode)){
					record.setProductStateName("被限制生产");
				}
			}
			// 当事人性质personNatureCode
			String personNatureCode = record.getPersonNatureCode();
			if (personNatureCode != null && !"".equals(personNatureCode)) {
				if ("1".equals(personNatureCode)) {
					record.setPersonNatureName("国有企业");
				} else if ("2".equals(personNatureCode)) {
					record.setPersonNatureName("国有独资公司");
				} else if ("3".equals(personNatureCode)) {
					record.setPersonNatureName("其他");
				} else if ("4".equals(personNatureCode)) {
					record.setPersonNatureName("未了解");
				}
			}
			// personcardTypeCode身份证类型:
			String personcardTypeCode = record.getPersoncardTypeCode();
			if (personcardTypeCode != null && !"".equals(personcardTypeCode)) {
				if ("0".equals(personcardTypeCode)) {
					record.setPersoncardTypeName("未取证");
				} else if ("1".equals(personcardTypeCode)) {
					record.setPersoncardTypeName("二代证");
				} else if ("2".equals(personcardTypeCode)) {
					record.setPersoncardTypeName("一代证");
				} else if ("3".equals(personcardTypeCode)) {
					record.setPersoncardTypeName("其他");
				}
			}
			// 证件类型:cardTypeCode
			String cardTypeCode = record.getCardTypeCode();
			if (cardTypeCode != null && !"".equals(cardTypeCode)) {
				if ("1".equals(cardTypeCode)) {
					record.setCardTypeName("营业执照");
				} else if ("2".equals(cardTypeCode)) {
					record.setCardTypeName("法人代码");
				} else if ("3".equals(cardTypeCode)) {
					record.setCardTypeName("统一社会信用代码");
				} else if ("4".equals(cardTypeCode)) {
					record.setCardTypeName("负责人身份证");
				} else if ("5".equals(cardTypeCode)) {
					record.setCardTypeName("户口");
				} else if ("6".equals(cardTypeCode)) {
					record.setCardTypeName("军人证");
				} else if ("7".equals(cardTypeCode)) {
					record.setCardTypeName("台胞证");
				} else if ("8".equals(cardTypeCode)) {
					record.setCardTypeName("港澳通行证");
				} else if ("9".equals(cardTypeCode)) {
					record.setCardTypeName("护照");
				} else if ("10".equals(cardTypeCode)) {
					record.setCardTypeName("武警证");
				}
			}

			// 是否正面企业
			String Ispositive = record.getIspositive();
			if (Ispositive != null && !"".equals(Ispositive)){
				if ("1".equals(Ispositive)){
					//...
				}
			}



		}
	}
	/**
	 * industry-type-page (企事业单位)行业类型加载框
	 */
	@RequestMapping(value = "/enterprises-industry-type-page")
	public ModelAndView enterprisesIndustryTypePage(Model model,HttpServletRequest request, HttpServletResponse response){
		ModelAndView view=new ModelAndView("zfdx/model/industryTypeModel");
		return view;
	}
	
	/**
	 * (企事业单位)双随机类型加载框
	 */
	@RequestMapping(value = "/enterprisesDoubleRandomModel")
	public ModelAndView enterprisesDoubleRandomTypePage(Model model,HttpServletRequest request, HttpServletResponse response){
		ModelAndView view=new ModelAndView("zfdx/model/doubleRandomModel");
		return view;
	}
	
	
	/**
	 * (企事业单位)双随机属性列表
	 */
	@RequestMapping(value ="/enterprisesDoubleRandomModelList")
	@ResponseBody 
	public  PageBean enterprisesDoubleRandomModelList(
			@RequestParam(value = "belongCity", required = false)String belongCity,
			@RequestParam(value = "belongCountry", required = false)String belongCountry,
			@RequestParam(value = "selectThis", required = false)String selectThis,
			@RequestParam(value = "pageNumber", required = false)String pageNumber, 
			@RequestParam(value = "pageSize", required = false)String pageSize,
			Model model,HttpServletRequest request, HttpServletResponse response) throws ParseException {
		System.out.println("进到了获取双随机属性列表");
		PageBean<DoubleRandomType> list=null;
		try {
			SeachObjectBean seachObject =new SeachObjectBean();
			seachObject.setBelongCity(belongCity);
			seachObject.setBelongCountry(belongCountry);
			seachObject.setSelectThis(selectThis);
			list = zfdxManagerService.doubleRandomModelList(seachObject, pageNumber, pageSize);
			System.out.println("-------"+seachObject.getBelongCity()+"------------"+seachObject.getBelongCountry()+"-------------"+seachObject.getSelectThis());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		System.out.println("-------------"+pageNumber+"----------"+pageSize+"--------------");
		System.out.println("走出了获取双随机属性列表");
		return list;
	}
	@RequestMapping(value="/changeLawObjectType")
	@ResponseBody
	public ResponseJson changeLawObjectType(String lawObjectId){
		ResponseJson result = zfdxManagerService.changeLawObjectType(lawObjectId);
		return result;
	}
	@RequestMapping(value="/showLawObjType")
	public ModelAndView showLawObjType(String lawObjType,String lawObjId){
		ModelAndView modelAndView = new ModelAndView("zfdx/showLawObjTypeModal");
		modelAndView.addObject("lawObjType", lawObjType);
		modelAndView.addObject("lawObjId", lawObjId);
		return modelAndView;
	}
	
	@RequestMapping(value = "/init84")
	@ResponseBody
	public ResponseJson init84(HttpServletRequest request,HttpServletResponse response,String filename,String url){
 
		try {
			init84Service.init();
			return new ResponseJson().success("200", "200", "初始化执法对象84坐标完成了", "-----成功了---", "打完收工");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return new ResponseJson().success("200", "200", "初始化执法对象84坐标完成了", "-----失败了---", "打完收工");
	}
	
	@RequestMapping("/getObjectCommentTreeData")
	@ResponseBody
	public ObjectCommentResult getObjectCommentTreeData(
			HttpServletRequest request, HttpServletResponse response,String lawObjectId) {
		ObjectCommentResult result = new ObjectCommentResult();
		List<LawCircle> dataList = lawCircleMapper.selectLawCirclesByLawObjectId(lawObjectId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		Map<String, List<LawCircle>> map = new HashMap<String, List<LawCircle>>();
		Set<String> yearSet = new HashSet<String>();
		Set<String> monthSet = new HashSet<String>();
		if(!ChangnengUtil.isNull(dataList)&&dataList.size()>0){
			String yearMonth = "";
			String year = "";
			String month = "";
			for(LawCircle lawCircle:dataList){
				List<LawCircleFiles> files = lawCircleFilesMapper.selectByLawCircleId(lawCircle.getId());
				lawCircle.setLawCircleFiles(files);
				yearMonth = sdf.format(lawCircle.getCreatDate());
				year = yearMonth.substring(0,4);//2019-04-03
				month = yearMonth.substring(5);
				yearSet.add(year);
				monthSet.add(year+month);
				List<LawCircle> lawCircleList= map.get(year+month);
				if(lawCircleList==null||lawCircleList.size()==0){
					List<LawCircle> temp = new ArrayList<LawCircle>();
					temp.add(lawCircle);
					map.put(year+month, temp);
				}else{
					lawCircleList.add(lawCircle);
					map.put(year+month, lawCircleList);
				}
			}
		}
		List yearList = new ArrayList<>(yearSet);
		List monthList = new ArrayList<>(monthSet);
		ListSort(yearList);
		ListSort(monthList);
		result.setYear(yearList);
		result.setMonth(monthList);
		result.setDatas(map);
		return result;
	}
	public void ListSort(List<String> list) {
		Collections.sort(list, new Comparator<String>() {
		        public int compare(String o1, String o2) {
		            try {
		            	int i = Integer.parseInt(o1);
		            	int j = Integer.parseInt(o2);
		            	if(i<j){
		            		return 1;
		            	}else if(i>j){
		            		return -1;
		            	}else{
		            		return 0;
		            	}
		             } catch (Exception e) {
		                e.printStackTrace();
		            }
		             return 0;
		         }
		});
	}
	@RequestMapping(value = "/showImageModal", method = RequestMethod.POST)
	@ResponseBody
	public List<LawCircleFiles> showImageModal(HttpServletRequest request,
			HttpServletResponse response,String lawCircleId,String currentImageOrgId) {
		List<LawCircleFiles> orginalFiles = lawCircleFilesMapper.selectSrcFilesInfoByLawCircleId(lawCircleId);
		for(LawCircleFiles file:orginalFiles){
			if(file.getId().equals(currentImageOrgId)){
				file.setSelected("1");
				break;
			}
		}
		return orginalFiles;
	}
	@RequestMapping(value = "/waterSource")
	public ModelAndView waterSourcePage(String back,String menuId,HttpServletRequest request, HttpServletResponse response){
		ModelAndView mav=new ModelAndView("zfdx/waterSource");
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		HttpSession session = request.getSession();
		session.setAttribute("preUrl","/zfdx/waterSource");
		if ("1".equals(back)) {
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)
						&& !"powerProvince".equals(key)
						&& !"powerCity".equals(key)
						&& !"powerCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			linkedParams.put("powerProvince", paramsMap.get("powerProvince"));
			linkedParams.put("powerCity", paramsMap.get("powerCity"));
			linkedParams.put("powerCountry", paramsMap.get("powerCountry"));
			// -------------------------结束---------------------------------------
			// 防止多开窗口param混乱
			if (!"/zfdx/waterSource".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			System.out.println(linkedParams.toString());
			mav.addObject("params", params);
			session.removeAttribute("paramsInSession");
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		/**控制页面按钮权限*/
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		if (!res.isEmpty() && res.size()>0) {
			for (int i = 0; i < res.size(); i++) {
				if (!ChangnengUtil.isNull(res.get(i).getResourceDesc())) {
					mav.addObject(res.get(i).getResourceDesc(), true);
				}
			}
		}
		mav.addObject("menuId", menuId);
		return mav;
	}
}
