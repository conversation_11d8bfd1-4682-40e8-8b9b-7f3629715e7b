package org.changneng.framework.frameworkweb.controller;

import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.changneng.framework.frameworkbusiness.entity.OopenInfo;
import org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.RandomTaskBean;
import org.changneng.framework.frameworkbusiness.entity.RandomTaskOpenInfoSearch;
import org.changneng.framework.frameworkbusiness.entity.SysResources;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkbusiness.service.RandomTaskOpenInfoService;
import org.changneng.framework.frameworkcore.utils.ExcelUtiles;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkweb.controller.filecase.CaseAccountsController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 双随机网站公开审查
 * <AUTHOR> 2018-07-30
 *
 */
@Controller
@RequestMapping("openReview")
public class RandomTaskOpenReviewController {
	private Logger logger = LogManager.getLogger(RandomTaskGenerateController.class);
	@Autowired
	private ICommonService commonService;

	@Autowired
	private RandomTaskOpenInfoService openInfoService;
	
	@RequestMapping(value="open",method=RequestMethod.POST)
	public ModelAndView open(RandomTaskBean randomTaskBean,String back, Model model,
			HttpServletRequest request,HttpServletResponse response) {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		randomTaskBean.setSelectToggle(3);
		
		model.addAttribute("randomTaskBean", randomTaskBean);
		List<SysResources> listSysResource=commonService.queryRolesSysResources(sysUsers.getUsername(), randomTaskBean.getMenuId());
		model.addAttribute("listSysResource",listSysResource);
		ModelAndView mav = new ModelAndView("randomTaskOpen/randomTaskOpen");
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			// 防止多开窗口param混乱
			if (!"/openReview/open".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			mav.addObject("params", params);
		}
		return mav;
	}
	
	@RequestMapping(value = "/show_list", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<OopenInfoWithBLOBs> show_list(@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize,
			RandomTaskOpenInfoSearch openInfoSearch,HttpServletRequest request, HttpServletResponse response){
		PageBean<OopenInfoWithBLOBs> info = openInfoService.getOpenInfoList(openInfoSearch);
		System.out.print(info);
		return info;
	}

	/**
	 * 删除单个
	 * @param id
	 * @return
	 */     
	
	@RequestMapping(value="deleteItem",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson deleteItem(OopenInfoWithBLOBs oInfo){
		ResponseJson json = new ResponseJson();
		try {
			json = openInfoService.deleteRandomOpenInfoById(oInfo);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	
		return json;
	}
	
	@RequestMapping(value="propellById",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson propellById(OopenInfoWithBLOBs oInfo){
		ResponseJson json = new ResponseJson();
		try {
		 json = openInfoService.propellById(oInfo);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return json;
	}
	
	@RequestMapping(value="returns",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson returns(OopenInfoWithBLOBs oInfo){
		ResponseJson json = new ResponseJson();
		try {
		 json = openInfoService.returnsById(oInfo);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return json;
	}
	
	
	/**
	 * 发起双随任务批量操作
	 */
	@RequestMapping(value = "/batchSubmit")
    @ResponseBody
	public ResponseJson batchSubmit(HttpServletRequest request,HttpServletResponse response,
		String ids
			) throws Exception {
		ResponseJson json = new ResponseJson();
		try {
		 json =  openInfoService.batchSubmit(ids);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
	}
	
	/**
	 * 导出属性转换
	 * 
	 * @param resultList
	 * @return
	 */
	private List<OopenInfoWithBLOBs> transferAttr(List<OopenInfoWithBLOBs> resultList){
		for (int i = 0; i < resultList.size(); i++) {
			OopenInfoWithBLOBs info = resultList.get(i);
			if ("0".equals(info.getState())) {
				info.setState("未提交");
			} else if ("1".equals(info.getState())){
				info.setState("已提交");
			}else if ("2".equals(info.getState())){
				info.setState("被退回");
			}else{
				info.setState("已公开");
			}
			if ("0".equals(info.getPublicState())) {
				info.setPublicState("未确认 ");
			} else{
				info.setPublicState("已确认 ");
				
			}
			if (info.getSubmitDate()!= null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy年 MM月dd日  HH:mm");
				info.setSubmitDateTr(sdf.format(info.getSubmitDate()));
			}
			if (info.getPropellDate() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy年 MM月dd日 HH:mm");
				info.setPropellDateTr(sdf.format(info.getPropellDate()));
			}
		}
		
		return resultList;
	}
	
	@RequestMapping("/download-MainCase-execl")
	public void downloadMainCaseExecl(RandomTaskOpenInfoSearch openInfoSearch, Model model,HttpServletRequest request, HttpServletResponse response) {
		// 1.获取用户信息，判断用户权限
		
		try {
			List<OopenInfoWithBLOBs> infoList = openInfoService.getOpenInfoLists(openInfoSearch);
			
			if (infoList != null && infoList.size() != 0) {
				// 数据展示转换
				infoList = transferAttr(infoList);
			}
			if("2".equals(openInfoSearch.getOptions())){
				String name = "提交公开审查.xls";
				String path = CaseAccountsController.class.getClassLoader().getResource("excel/" + name).getPath();
				ExcelUtiles.downExcel(path, name, "提交公开审查", infoList, response);		
			}else{
				String name = "推送网站公开.xls";
				String path = CaseAccountsController.class.getClassLoader().getResource("excel/" + name).getPath();
				ExcelUtiles.downExcel(path, name, "推送网站公开", infoList, response);		
			}
				
		} catch (Exception e) {
			logger.error(e);
		}
	}
}
