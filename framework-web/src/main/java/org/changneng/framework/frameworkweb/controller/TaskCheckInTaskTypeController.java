package org.changneng.framework.frameworkweb.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.CircleDictionary;
import org.changneng.framework.frameworkbusiness.entity.CircleDictionarySeach;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.service.CircleDictionaryService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * 执法签到标签
 * <AUTHOR>
 *
 */

@Controller
@RequestMapping(value="taskCheckInTaskType")
public class TaskCheckInTaskTypeController {
	@Autowired
	private CircleDictionaryService circleDictionaryService;
	/**
	 * 执法签到标签页面
	 * @param reqeust
	 * @param response
	 * @param m
	 * @return
	 */
	@RequestMapping(value="/toPage", method = RequestMethod.POST)
	public ModelAndView toPage(HttpServletRequest reqeust,HttpServletResponse response,Model m) {
		ModelAndView mav = new ModelAndView("taskManager/taskCheckInTaskType/toPage");
		return mav;
	}
	/**
	 * 执法签到标签列表
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "taskCheckInList", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson taskCheckInList(HttpServletRequest request,HttpServletResponse response,
			CircleDictionarySeach seachBean) {
		try {
			PageBean<CircleDictionary> circleDictionaryList = circleDictionaryService.selectBySeachBean(seachBean);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功", "查询执法签到标签列表成功！", circleDictionaryList);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败", e.getMessage(), null);
		}
	}
	/**
	 * 执法签到标签新增
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "addTaskCheckIn", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson addTaskCheckIn(HttpServletRequest request,HttpServletResponse response,
			CircleDictionary circleDictionary) {
		try {
			String info = circleDictionaryService.addOrUpdateCircleDictionary(circleDictionary);
			if(ChangnengUtil.isNull(circleDictionary.getId())) {
				return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), info+"成功", info+"执法签到标签成功！", null);				
			}else {
				return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.UPDATE_SUCCESS.toString(), info+"成功", info+"执法签到标签成功！", null);								
			}
		} catch (Exception e) {
			e.printStackTrace();
			if(ChangnengUtil.isNull(circleDictionary.getId())) {
				return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "新增失败", e.getMessage(), null);
			}else {
				return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.UPDATE_FAILURE.toString(), "修改失败", e.getMessage(), null);				
			}
		}
	}

	/**
	 * 执法签到标签删除
	 * 
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "delTaskCheckIn", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson delkTaskCheckIn(HttpServletRequest request, HttpServletResponse response,
			String id) {
		try {
			circleDictionaryService.delCircleDictionary(id);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.DELETE_SUCCESS.toString(),
					"删除成功！", "删除执法签到标签成功！", null);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
					SystemStatusCode.DELETE_ERROR.toString(), "删除失败！", e.getMessage(), null);
		}
	}
}
