package org.changneng.framework.frameworkweb.controller.gyyq;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.changneng.framework.frameworkbusiness.dao.AreaMapper;
import org.changneng.framework.frameworkbusiness.dao.LawEnforceObjectMapper;
import org.changneng.framework.frameworkbusiness.dao.ParkGridMapper;
import org.changneng.framework.frameworkbusiness.dao.ParkLawMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.dao.TcDictionaryMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AdministrativeDetentionMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.ApplyForceMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AtvSanctionCaseMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CaseBaseInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.ExecutiveOrderMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.LimitStopProductMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.OtherTransferMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.PenaltyDayMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.PollutionCrimeMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.SequestrationInfoMapper;
import org.changneng.framework.frameworkbusiness.entity.Area;
import org.changneng.framework.frameworkbusiness.entity.ChickObjectBean;
import org.changneng.framework.frameworkbusiness.entity.IndustrialParkWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.ParkGrid;
import org.changneng.framework.frameworkbusiness.entity.ParkLaw;
import org.changneng.framework.frameworkbusiness.entity.ParkListBean;
import org.changneng.framework.frameworkbusiness.entity.ParkSearchBean;
import org.changneng.framework.frameworkbusiness.entity.PicEvidence;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.Task;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.entity.filecase.AdministrativeDetentionWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.ApplyForceWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.AtvSanctionCaseWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo;
import org.changneng.framework.frameworkbusiness.entity.filecase.ExecutiveOrderWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.LimitStopProductWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.OtherTransferWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDay;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDayWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.PollutionCrimeWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.SequestrationInfoWithBLOBs;
import org.changneng.framework.frameworkbusiness.service.ParkArchivesService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.changneng.framework.frameworkcore.utils.ViewExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;


/** 
* <AUTHOR> 
* @version 2019年5月20日 下午3:53:13 
* 类说明 (一园一档 菜单       controller)
*/
@Controller
@RequestMapping(value = "parkArchives")
public class ParkArchivesController {
	@Autowired
	private TcDictionaryMapper tcDictionaryMapper;
	@Autowired
	private ParkArchivesService parkArchivesService;
	@Autowired
	private ParkLawMapper parkLawMapper;
	@Autowired
	private AtvSanctionCaseMapper atvSanctionCaseMapper;
	@Autowired
	private SysFilesMapper sysFilesMapper;
	@Autowired
	private ExecutiveOrderMapper executiveOrderMapper;
	@Autowired
	private SequestrationInfoMapper sequestrationInfoMapper;
	@Autowired
	private LimitStopProductMapper limitStopProductMapper;
	@Autowired
	private AdministrativeDetentionMapper administrativeDetentionMapper;
	@Autowired
	private PollutionCrimeMapper pollutionCrimeMapper;
	@Autowired
	private OtherTransferMapper otherTransferMapper;
	@Autowired
	private PenaltyDayMapper penaltyDayMapper;
	@Autowired
	private ApplyForceMapper applyForceMapper;
	@Autowired
	private ParkGridMapper parkGridMapper;
	@Autowired
	private SysUsersMapper sysUsersMapper;
	@Autowired
	private AreaMapper areaMapper;
	@Autowired
	private TaskMapper taskMapper;
	@Autowired
	private CaseBaseInfoMapper caseBaseInfoMapper;
	@Autowired
	private LawEnforceObjectMapper lawEnforceObjectMapper;
	/**
	 * 一园一档主页
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "toMainPage")
	public ModelAndView toMainPage(HttpServletRequest request, HttpServletResponse response ,ParkSearchBean seachBean) {
		ModelAndView mav = new ModelAndView("gyjjq/parkArchives/mainPage");
		try {
			// 工业园区级别
			List<TcDictionary> parkLevel = tcDictionaryMapper.getTcByTempOrderByCode("I_PARK_LEVEL");
			mav.addObject("parkLevel", parkLevel);
			// 工业园区类别
			List<TcDictionary> parkType = tcDictionaryMapper.getTcByTempOrderByCode("I_PARK_TYPE");
			mav.addObject("parkType", parkType);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 获取首页数据
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "getListBean")
	@ResponseBody
	public ParkListBean getListBean(HttpServletRequest request, HttpServletResponse response ,ParkSearchBean seachBean) {
		try{
			ParkListBean listBean = parkArchivesService.listParkBySearch(seachBean);
			return listBean;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}
	/**
	 * 园区基本信息
	 * @param request
	 * @param response
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "getParkDetailMain")
	public ModelAndView getParkDetailMain(HttpServletRequest request, HttpServletResponse response ,String id) {
		ModelAndView mav = new ModelAndView("gyjjq/parkArchives/parkDetailMain");
		try {
			ParkSearchBean seachBean = new ParkSearchBean();
			seachBean.setParkId(id);
			IndustrialParkWithBLOBs  park = parkArchivesService.getParkDetailById(id);
			//监管对象总数
			Integer lawCount = parkLawMapper.selectCountById(id);
			//执法总数
			Integer taskCount = taskMapper.getListTaskBean(seachBean).size();
			//案件总数
			Integer caseCount = caseBaseInfoMapper.getCaseListBean(seachBean).size();
			//队伍信息中的执法人员数量
			String code = park.getBelongAreaId();//县级
    		if("000000".equals(park.getBelongAreaId().substring(2))){
    			code = park.getBelongAreaId().substring(0,2);//省级
    		}else if("0000".equals(park.getBelongAreaId().substring(4))){
    			code =  park.getBelongAreaId().substring(0,4);//市级
    		}
			List<SysUsers> list = sysUsersMapper.getLawPerson(code);
			mav.addObject("park",park );
			mav.addObject("lawCount", lawCount);
			mav.addObject("taskCount", taskCount);
			mav.addObject("caseCount", caseCount);
			mav.addObject("lawNum", list.size());
			System.out.println("*************"+park);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 园区监管对象
	 * @param seachBean
	 * @return
	 */
	@RequestMapping(value = "listParkBySearch",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson listParkBySearch(ParkSearchBean seachBean) {
		try {
			PageBean<LawEnforceObjectWithBLOBs> listBean = parkArchivesService.getListBeanById(seachBean);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功", "查询监管对象成功！", listBean);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败", e.getMessage(), null);
		}
	}
	/**
	 * 园区  环境执法
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping("/getTaskListBean")
	@ResponseBody
	public ChickObjectBean getTaskListBean(HttpServletRequest request, HttpServletResponse response,ParkSearchBean seachBean){
		try{
			ChickObjectBean listBean = new ChickObjectBean();
			List<Task> taskBean = new ArrayList<Task>();
			if(ChangnengUtil.isNull(seachBean.getPageSize())){
				seachBean.setPageSize(20);
			}
			if(ChangnengUtil.isNull(seachBean.getPageNumber())){
				seachBean.setPageNumber(1); 
			}
			if(!"".equals(seachBean.getParkId()) && seachBean.getParkId() != null){
				taskBean = parkArchivesService.getListTask(seachBean);
			}
			Set<String> yearList = new LinkedHashSet<String>();
			Set<String> mounthList = new LinkedHashSet<String>();
			if(taskBean != null){
				for(Task task : taskBean){
					Date processingTime = task.getCreatDate();
					String year = null;
					String month = null;
					if (processingTime != null) { 
						String format = DateUtil.format(processingTime);
						year = format.substring(0, 4);
						month = (String) format.subSequence(5, 7);
						task.setYear(year);
						yearList.add(year);
						task.setYearMonth(year + month);
						mounthList.add(year + month);
						
					} 
					task.setYearList(yearList);
					task.setMounthList(mounthList);
					if(!ChangnengUtil.isNull(task.getLawEnforcementStartTime())){
						task.setLawEnforcementStartTimeStr(DateUtil.cFormat(task.getLawEnforcementStartTime()));
					}else{
						task.setLawEnforcementStartTimeStr("-");
					}
					if(!ChangnengUtil.isNull(task.getLawEnforcementEndTime())){
						task.setLawEnforcementEndTimeStr(DateUtil.cFormat(task.getLawEnforcementEndTime()));
					}else{
						task.setLawEnforcementEndTimeStr("-");
					}
					//处理页面展示 null
					if(ChangnengUtil.isNull(task.getCheckSummary())){
						task.setCheckSummary("空");
					}
					//拼接執法人員和执法证号
					String[] arrayName = task.getChecUserNames().split(",");
					String[] arrayCard = task.getLawEnforcIds().split(",");
					String lawStr = "";
					//若数据正常则  name  和   card 的长度应该是相等的，为了防止数组下标越界所以使用了下边的判断
					int length = (arrayName.length > arrayCard.length) ? arrayCard.length : arrayName.length;
					for(int i = 0 ;i < length;i++){
						lawStr += arrayName[i]+","+arrayCard[i]+";";
					}
					task.setChecUserNames(lawStr);
					//执法对应的所有附件
					List<PicEvidence> fileList = parkArchivesService.getTaskFile(task.getId());
					task.setFileList(fileList);
				}
			}
			listBean.setMounthList(mounthList);
			listBean.setYearList(yearList);
			listBean.setListTaskBean(taskBean);
			listBean.setPageNumber(seachBean.getPageNumber());
			listBean.setPageSize(seachBean.getPageSize());
			return listBean;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}
	/**
	 * 园区  案件查办
	 * @param request
	 * @param response
	 * @param seachBean
	 * @return
	 */
	@RequestMapping("/getCaseListBean")
	@ResponseBody
	public ChickObjectBean getCaseListBean(HttpServletRequest request, HttpServletResponse response,ParkSearchBean seachBean){
		try{
			ChickObjectBean listBean = new ChickObjectBean();
			List<CaseBaseInfo> caseBean = new ArrayList<CaseBaseInfo>();
			if(ChangnengUtil.isNull(seachBean.getPageSize())){
				seachBean.setPageSize(20);
			}
			if(ChangnengUtil.isNull(seachBean.getPageNumber())){
				seachBean.setPageNumber(1); 
			}
			if(!"".equals(seachBean.getParkId()) && seachBean.getParkId() != null){
				caseBean = parkArchivesService.getListCase(seachBean);
			}
			Set<String> yearList = new LinkedHashSet<String>();
			Set<String> mounthList = new LinkedHashSet<String>();
			if(caseBean != null){
				for(CaseBaseInfo caseInfo : caseBean){
					if("0".equals(caseInfo.getCaseStatus().toString())){
						caseInfo.setCaseStatusName("进行中");
					}else{
						caseInfo.setCaseStatusName("已办结");
					}
					Date processingTime = caseInfo.getCreateTime();
					String year = null;
					String month = null;
					if (processingTime != null) { 
						String format = DateUtil.format(processingTime);
						year = format.substring(0, 4);
						month = (String) format.subSequence(5, 7);
						caseInfo.setYear(year);
						yearList.add(year);
						caseInfo.setYearMonth(year + month);
						mounthList.add(year + month);
						
					} 
					caseInfo.setYearList(yearList);
					caseInfo.setMounthList(mounthList);
				}
			}
			listBean.setMounthList(mounthList);
			listBean.setYearList(yearList);
			listBean.setListCaseBean(caseBean);
			listBean.setPageNumber(seachBean.getPageNumber());
			listBean.setPageSize(seachBean.getPageSize());
			return listBean;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}
	
	@RequestMapping(value = "/caseCheck")
	@ResponseBody
	public ModelAndView caseCheckMain(String caseId, HttpServletRequest request,HttpServletResponse response,String caseExtractSource) {
		System.out.println("anjuan的id"+caseId);
		ModelAndView view = new ModelAndView("gyjjq/parkArchives/caseCheck");
		try{
			//根据大案件id查询其包含的附件
			String ip = PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
			//行政处罚	
			AtvSanctionCaseWithBLOBs atvSanctionCase = atvSanctionCaseMapper.getBeanByCaseId(caseId);
			SysFiles atvSanctionCaseJYSysFiles = null;//简易行政处罚
			SysFiles atvSanctionCaseYBSysFiles = null;//一般行政处罚
			if(!ChangnengUtil.isNull(atvSanctionCase) && !ChangnengUtil.isNull(atvSanctionCase.getCaseType())){
				if(atvSanctionCase.getCaseType().toString().equals("1")){//简易程序：1，一般行政处罚：2
					if(!ChangnengUtil.isNull(atvSanctionCase.getRecordsUrlUp())){
						atvSanctionCaseJYSysFiles = sysFilesMapper.selectByPrimaryKey(atvSanctionCase.getRecordsUrlUp());
					}
					if(!ChangnengUtil.isNull(atvSanctionCase.getRecordsUrlDown())){//下部分不为空取下部分，下部分为空取上部分
						atvSanctionCaseJYSysFiles = sysFilesMapper.selectByPrimaryKey(atvSanctionCase.getRecordsUrlDown());
					}
				}else if(atvSanctionCase.getCaseType().toString().equals("2")){
					if(!ChangnengUtil.isNull(atvSanctionCase.getRecordsUrlUp())){
						atvSanctionCaseYBSysFiles = sysFilesMapper.selectByPrimaryKey(atvSanctionCase.getRecordsUrlUp());
					}
					if(!ChangnengUtil.isNull(atvSanctionCase.getRecordsUrlDown())){//下部分不为空取下部分，下部分为空取上部分
						atvSanctionCaseYBSysFiles = sysFilesMapper.selectByPrimaryKey(atvSanctionCase.getRecordsUrlDown());
					}
				}
				if(!ChangnengUtil.isNull(atvSanctionCaseJYSysFiles)){
					atvSanctionCaseJYSysFiles.setFileUrl(ip+atvSanctionCaseJYSysFiles.getFileUrl());//拼接PDF地址
				}
				if(!ChangnengUtil.isNull(atvSanctionCaseYBSysFiles)){
					atvSanctionCaseYBSysFiles.setFileUrl(ip+atvSanctionCaseYBSysFiles.getFileUrl());
				}
			}
			//行政命令
			ExecutiveOrderWithBLOBs executiveOrderWithBLOBs = executiveOrderMapper.selectByCaseId(caseId);
			SysFiles executiveOrderFile = null;
			if(!ChangnengUtil.isNull(executiveOrderWithBLOBs) && !ChangnengUtil.isNull(executiveOrderWithBLOBs.getRecordsUrl())){
				executiveOrderFile = sysFilesMapper.selectByPrimaryKey(executiveOrderWithBLOBs.getRecordsUrl());
			}
			if(!ChangnengUtil.isNull(executiveOrderFile)){
				executiveOrderFile.setFileUrl(ip+executiveOrderFile.getFileUrl());
			}
			//查封扣押
			SequestrationInfoWithBLOBs sequestrationInfoWithBLOBs = sequestrationInfoMapper.selectInfoByCaseId(caseId);
			SysFiles sequestrationInfoFile = null;
			if(!ChangnengUtil.isNull(sequestrationInfoWithBLOBs)){
				if(!ChangnengUtil.isNull(sequestrationInfoWithBLOBs.getRecordsUrlUp())){//下部分不为空取下部分，下部分为空取上部分
					sequestrationInfoFile = sysFilesMapper.selectByPrimaryKey(sequestrationInfoWithBLOBs.getRecordsUrlUp());
				}
				if(!ChangnengUtil.isNull(sequestrationInfoWithBLOBs.getRecordsUrlDown())){//下部分不为空取下部分，下部分为空取上部分
					sequestrationInfoFile = sysFilesMapper.selectByPrimaryKey(sequestrationInfoWithBLOBs.getRecordsUrlDown());
				}
			}
			if(!ChangnengUtil.isNull(sequestrationInfoFile)){
				sequestrationInfoFile.setFileUrl(ip+sequestrationInfoFile.getFileUrl());
			}
			//限产停产
			LimitStopProductWithBLOBs limitStopProductWithBLOBs = limitStopProductMapper.selectByCaseId(caseId);
			SysFiles limitStopProductFile = null;
			if(!ChangnengUtil.isNull(limitStopProductWithBLOBs)){
				if(!ChangnengUtil.isNull(limitStopProductWithBLOBs.getRecordsUrlUp())){//下部分不为空取下部分，下部分为空取上部分
					limitStopProductFile = sysFilesMapper.selectByPrimaryKey(limitStopProductWithBLOBs.getRecordsUrlUp());
				}
				if(!ChangnengUtil.isNull(limitStopProductWithBLOBs.getRecordsUrlDown())){//下部分不为空取下部分，下部分为空取上部分
					limitStopProductFile = sysFilesMapper.selectByPrimaryKey(limitStopProductWithBLOBs.getRecordsUrlDown());
				}
			}
			if(!ChangnengUtil.isNull(limitStopProductFile)){
				limitStopProductFile.setFileUrl(ip+limitStopProductFile.getFileUrl());
			}
			//行政拘留
			AdministrativeDetentionWithBLOBs administrativeDetentionWithBLOBs = administrativeDetentionMapper.selectByCaseId(caseId);
			SysFiles administrativeDetentionFile = null;
			if(!ChangnengUtil.isNull(administrativeDetentionWithBLOBs)){
				if(!ChangnengUtil.isNull(administrativeDetentionWithBLOBs.getRecordsUrlUp())){//下部分不为空取下部分，下部分为空取上部分
					administrativeDetentionFile = sysFilesMapper.selectByPrimaryKey(administrativeDetentionWithBLOBs.getRecordsUrlUp());
				}
				if(!ChangnengUtil.isNull(administrativeDetentionWithBLOBs.getRecordsUrlDown())){//下部分不为空取下部分，下部分为空取上部分
					administrativeDetentionFile = sysFilesMapper.selectByPrimaryKey(administrativeDetentionWithBLOBs.getRecordsUrlDown());
				}
			}
			if(!ChangnengUtil.isNull(administrativeDetentionFile)){
				administrativeDetentionFile.setFileUrl(ip+administrativeDetentionFile.getFileUrl());
			}
			//环境污染犯罪
			PollutionCrimeWithBLOBs pollutionCrimeWithBLOBs = pollutionCrimeMapper.selectByCaseId(caseId);
			SysFiles pollutionCrimeFile = null;
			if(!ChangnengUtil.isNull(pollutionCrimeWithBLOBs)){
				if(!ChangnengUtil.isNull(pollutionCrimeWithBLOBs.getFirstPartUrl())){//下部分不为空取下部分，下部分为空取上部分
					pollutionCrimeFile = sysFilesMapper.selectByPrimaryKey(pollutionCrimeWithBLOBs.getFirstPartUrl());
				}
				if(!ChangnengUtil.isNull(pollutionCrimeWithBLOBs.getSecondPartUrl())){//下部分不为空取下部分，下部分为空取上部分
					pollutionCrimeFile = sysFilesMapper.selectByPrimaryKey(pollutionCrimeWithBLOBs.getSecondPartUrl());
				}
			}
			if(!ChangnengUtil.isNull(pollutionCrimeFile)){
				pollutionCrimeFile.setFileUrl(ip+pollutionCrimeFile.getFileUrl());
			}
			//申请法院强制执行
			ApplyForceWithBLOBs applyForce = applyForceMapper.selectByCaseId(caseId);
			SysFiles applyForceFile = null;
			if(!ChangnengUtil.isNull(applyForce)){
				if(!ChangnengUtil.isNull(applyForce.getOnekeyUrl())){//下部分不为空取下部分，下部分为空取上部分
					applyForceFile = sysFilesMapper.selectByPrimaryKey(applyForce.getOnekeyUrl());
				}
			}
			if(!ChangnengUtil.isNull(applyForceFile)){
				applyForceFile.setFileUrl(ip+pollutionCrimeFile.getFileUrl());
			}
			//其他移送
			OtherTransferWithBLOBs otherTransferWithBLOBs = otherTransferMapper.selectOtherTransferByCaseId(caseId);
			SysFiles otherTransferFile = null;
			if(!ChangnengUtil.isNull(otherTransferWithBLOBs)){
				if(!ChangnengUtil.isNull(otherTransferWithBLOBs.getRecordsUrl())){
					otherTransferFile = sysFilesMapper.selectByPrimaryKey(otherTransferWithBLOBs.getRecordsUrl());
				}
			}
			if(!ChangnengUtil.isNull(otherTransferFile)){
				otherTransferFile.setFileUrl(ip+otherTransferFile.getFileUrl());
			}
			//按日记罚
			List<PenaltyDayWithBLOBs> penaltyDayList = penaltyDayMapper.selectByCaseId(caseId);
			List<SysFiles> penaltyDayFiles = null;
			String sqlId = "";
			if(!ChangnengUtil.isNull(penaltyDayList)){
				for(PenaltyDay pd : penaltyDayList){
					if(!ChangnengUtil.isNull(pd.getSecondOnekeyUrl())){//下部分不为空取下部分，下部分为空取上部分
						sqlId = sqlId +"'"+pd.getSecondOnekeyUrl()+"',";
					}else{
						if(!ChangnengUtil.isNull(pd.getFirstOnekeyUrl())){
							sqlId = sqlId +"'"+pd.getFirstOnekeyUrl()+"',";
						}
					}
				}
				System.out.println("sql条件**********"+sqlId);
				if(!ChangnengUtil.isNull(sqlId)){
					penaltyDayFiles = sysFilesMapper.selectDataBySql(sqlId.substring(0, sqlId.length()-1));
				}
				if(!ChangnengUtil.isNull(penaltyDayFiles) && penaltyDayFiles.size() > 0){
					for(SysFiles sysFiles : penaltyDayFiles){
						if(!ChangnengUtil.isNull(sysFiles)){
							sysFiles.setFileUrl(ip+sysFiles.getFileUrl());
						}
					}
				}
			}
			view.addObject("XZCF_JY", atvSanctionCaseJYSysFiles);//简易行政处罚
			view.addObject("XZCF_YB", atvSanctionCaseYBSysFiles);//一般行政处罚
			view.addObject("XZML", executiveOrderFile);//行政命令
			view.addObject("CFKY", sequestrationInfoFile);//查封扣押
			view.addObject("XCTC", limitStopProductFile);//限产停产
			view.addObject("XZJL", administrativeDetentionFile);//行政拘留
			view.addObject("HJWRFZ",pollutionCrimeFile);//环境污染犯罪
			view.addObject("SQFYQZZX",applyForceFile);//申请法院强制执行
			view.addObject("QTYS",otherTransferFile);//其他移送
			view.addObject("ARJF", penaltyDayFiles);//按日记罚	*/
			if(!ChangnengUtil.isNull(atvSanctionCaseYBSysFiles) || !ChangnengUtil.isNull(atvSanctionCaseJYSysFiles)){
				view.addObject("XZCF","YES");
			}else{
				view.addObject("XZCF","NO");
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return view;
		}
		/**
		 * 跳转图片轮播modal
		 * @param id
		 * @param num
		 * @param request
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/picinfoPage")
		public ModelAndView picinfoPage(String id, String num, HttpServletRequest request,HttpServletResponse response) {
			ModelAndView mav = new ModelAndView("gyjjq/parkArchives/picinfoPage");
			System.out.println("**********************"+id + num +"***********************");
			mav.addObject("taskId", id);
			mav.addObject("picNum", num);
			return mav;
		}
		/**
		 * 图片轮播的数据
		 * @param taskId
		 * @param request
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/getPictureData")
		@ResponseBody
		public List<PicEvidence> getPictureData(String taskId, HttpServletRequest request,HttpServletResponse response){
			List<PicEvidence> fileList = null;
			String ip = PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
			try {
				fileList = parkArchivesService.getTaskFile(taskId);
				for(PicEvidence f : fileList){
					f.setPicUrl(ip+"/"+f.getPicUrl());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return fileList;
		}
		/**
		 * 调用网格数据要给返回的参数
		 * @param request
		 * @param response
		 * @param parkId
		 * @return
		 */
		@RequestMapping(value = "/getParameterBySource")
		@ResponseBody
		public String getParameter(HttpServletRequest request,HttpServletResponse response,String parkId){
			String parameter = "";
			try{
				List<ParkGrid> listBean =  parkGridMapper.selectByParkId(parkId);
				for(ParkGrid pg : listBean){
					parameter += "{areaCode:"+"'"+pg.getGridCode()+"'"+",areaLeavel:"+"'"+pg.getGridLevel()+"'"+"},";
				}
				if(parameter.length() > 0){
					parameter = "["+parameter.substring(0, parameter.length()-1)+"]";
				}
			}catch(Exception e){
				e.printStackTrace();
				return parameter;
			}
			return parameter;
		}
		/**
		 * 2019-6-12 一园一档   队伍信息中执法人员来自执法系统
		 * @param request
		 * @param response
		 * @param belongAreaId  所属行政区code
		 * @return
		 */
		@RequestMapping(value = "/getLawPerson",method=RequestMethod.POST)
		@ResponseBody
	    public ResponseJson getLawPerson(HttpServletRequest request,HttpServletResponse response,String belongAreaId){
	    	List<SysUsers> list = null;
	    	try{
	    		String code = belongAreaId;//县级
	    		if("000000".equals(belongAreaId.substring(2))){
	    			code = belongAreaId.substring(0,2);//省级
	    		}else if("0000".equals(belongAreaId.substring(4))){
	    			code =  belongAreaId.substring(0,4);//市级
	    		}
	    		list = sysUsersMapper.getLawPerson(code);
	    		if(!ChangnengUtil.isNull(list)){
	    			for(SysUsers sy : list){
	    				Area area= areaMapper.queryAreaByAreacode(sy.getBelongAreaId());
	    				if(!ChangnengUtil.isNull(area)){
	    					sy.setBelongAreaName(area.getName());
	    				}
	    			}
	    		}
	    	}catch(Exception e){
	    		e.printStackTrace();
	    		return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "失败", e.getMessage(), null);
	    	}
	    	return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "成功", "成功！", list);
	    }
		/**
		 *导出执法人员Excel
		 * @param belongAreaId
		 * @param request
		 * @param workbook
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/exportZfUserExcel")
	    public ModelAndView exportZfUserExcel(String belongAreaId,HttpServletRequest request,HSSFWorkbook workbook,HttpServletResponse response){
	    	try {
	    		ViewExcel viewExcel = new ViewExcel();
	    		Map<String, Object> model = new  HashMap<String, Object>();
	    		//表头
	    		String[] columnNames  = {"执法员姓名","所属行政区","所属部门","职务","手机号"};
	    		model.put("columnNames", columnNames);
	    		//list集合对应的值
	    		String[] dbColumnNames  = {"loginname","belongAreaName","belongDepartmentName","jobName","phone"};
	    		model.put("dbColumnNames",dbColumnNames );
	    		//执法人员信息
	    		String code = belongAreaId;//县级
	    		if("000000".equals(belongAreaId.substring(2))){
	    			code = belongAreaId.substring(0,2);//省级
	    		}else if("0000".equals(belongAreaId.substring(4))){
	    			code =  belongAreaId.substring(0,4);//市级
	    		}
	    		List<SysUsers> list = sysUsersMapper.getLawPerson(code);
	    		if(!ChangnengUtil.isNull(list)){
	    			for(SysUsers sy : list){
	    				Area area= areaMapper.queryAreaByAreacode(sy.getBelongAreaId());
	    				if(!ChangnengUtil.isNull(area)){
	    					sy.setBelongAreaName(area.getName());
	    				}
	    			}
	    		}
	    		model.put("list",list );
	    		//excel文件的名称
	    		model.put("excelName", "一园一档执法员信息"+DateUtil.getDateTime("yyyyMMddHHmmss")+".xls");
	    		//excel 文件的sheet
	    		model.put("sheetName", "sheet1");
	    		//标记序号
	    		model.put("flag", true);
				viewExcel.buildExcelDocument(model , workbook, request, response);
				return new ModelAndView(new ViewExcel(), model);
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
		/**
		 * 导出监管对象Excel
		 * @param seachBean
		 * @param request
		 * @param workbook
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/exportParkExcel")
	    public ModelAndView exportParkExcel(ParkSearchBean seachBean,HttpServletRequest request,HSSFWorkbook workbook,HttpServletResponse response){
	    	try {
	    		ViewExcel viewExcel = new ViewExcel();
	    		Map<String, Object> model = new  HashMap<String, Object>();
	    		//表头
	    		String[] columnNames  = {"对象名称","类型","证照名称","证照号","联系人","联系方式"};
	    		model.put("columnNames", columnNames);
	    		//list集合对应的值
	    		String[] dbColumnNames  = {"objectName","typeName","socialCreditCode","licenseNo","legalPerson","legalPhone"};
	    		model.put("dbColumnNames",dbColumnNames );
	    		//监管对象信息
	    		List<LawEnforceObjectWithBLOBs> list = lawEnforceObjectMapper.selectDataBySearchBean(seachBean);
	    		if (list != null && list.size() > 0) {
	    			for (LawEnforceObject lawEnforObject : list) {
	    				if ("1".equals(lawEnforObject.getTypeCode())) {
	    					lawEnforObject.setLicenseNo(lawEnforObject.getSocialCreditCode());
	    					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
	    					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
	    					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
	    						lawEnforObject.setSocialCreditCode("统一社会信用代码");
	    					}
	    				} else if ("2".equals(lawEnforObject.getTypeCode())) {
	    					lawEnforObject.setSocialCreditCode(lawEnforObject.getPersoncardTypeName());
	    					lawEnforObject.setLicenseNo(lawEnforObject.getCardNumber());
	    				} else if ("3".equals(lawEnforObject.getTypeCode())) {
	    					lawEnforObject.setLicenseNo(lawEnforObject.getCardNumber());
	    				} else if ("4".equals(lawEnforObject.getTypeCode())||"6".equals(lawEnforObject.getTypeCode())) {
	    					lawEnforObject.setLicenseNo(lawEnforObject.getOrgCode());
	    					lawEnforObject.setLegalPerson(lawEnforObject.getChargePerson());
	    					lawEnforObject.setLegalPhone(lawEnforObject.getChargePersonPhone());
	    					if (!ChangnengUtil.isNull(lawEnforObject.getSocialCreditCode())) {
	    						lawEnforObject.setSocialCreditCode("管理机构统一社会信用代码");
	    					}
	    				} else if ("5".equals(lawEnforObject.getTypeCode())) {
	    					lawEnforObject.setLicenseNo(lawEnforObject.getCardNumber());
	    				} else {
	    					lawEnforObject.setLicenseNo(lawEnforObject.getCardNumber());
	    				}
	    			}
	    		}
	    		model.put("list",list );
	    		//excel文件的名称
	    		model.put("excelName", "一园一档监管对象信息"+DateUtil.getDateTime("yyyyMMddHHmmss")+".xls");
	    		//excel 文件的sheet
	    		model.put("sheetName", "sheet1");
	    		//标记序号
	    		model.put("flag", true);
				viewExcel.buildExcelDocument(model , workbook, request, response);
				return new ModelAndView(new ViewExcel(), model);
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
		/**
		 * 导出环境执法Excel
		 * @param seachBean
		 * @param request
		 * @param workbook
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/exportHjzfExcel")
	    public ModelAndView exportHjzfExcel(ParkSearchBean seachBean,HttpServletRequest request,HSSFWorkbook workbook,HttpServletResponse response){
	    	try {
	    		ViewExcel viewExcel = new ViewExcel();
	    		Map<String, Object> model = new  HashMap<String, Object>();
	    		//表头
	    		String[] columnNames  = {"执法编号","执法状态","执法对象名称","执法人员及执法证件编号","执法时间","执法小结"};
	    		model.put("columnNames", columnNames);
	    		//list集合对应的值
	    		String[] dbColumnNames  = {"taskId","taskStateName","lawObjectName","checUserNames","lawEnforcementStartTimeStr","checkSummary"};
	    		model.put("dbColumnNames",dbColumnNames );
	    		//环境执法信息
	    		List<Task> taskBean = taskMapper.getListTaskBean(seachBean);
	    		if(taskBean != null){
					for(Task task : taskBean){
						if(!ChangnengUtil.isNull(task.getLawEnforcementStartTime())){
							task.setLawEnforcementStartTimeStr(DateUtil.cFormat(task.getLawEnforcementStartTime()));
						}else{
							task.setLawEnforcementStartTimeStr("-");
						}
						if(!ChangnengUtil.isNull(task.getLawEnforcementEndTime())){
							task.setLawEnforcementEndTimeStr(DateUtil.cFormat(task.getLawEnforcementEndTime()));
						}else{
							task.setLawEnforcementEndTimeStr("-");
						}
						task.setLawEnforcementStartTimeStr(task.getLawEnforcementStartTimeStr()+"——"+task.getLawEnforcementEndTimeStr());
						//处理页面展示 null
						if(ChangnengUtil.isNull(task.getCheckSummary())){
							task.setCheckSummary("空");
						}
						//拼接執法人員和执法证号
						String[] arrayName = task.getChecUserNames().split(",");
						String[] arrayCard = task.getLawEnforcIds().split(",");
						String lawStr = "";
						//若数据正常则  name  和   card 的长度应该是相等的，为了防止数组下标越界所以使用了下边的判断
						int length = (arrayName.length > arrayCard.length) ? arrayCard.length : arrayName.length;
						for(int i = 0 ;i < length;i++){
							lawStr += arrayName[i]+","+arrayCard[i]+";";
						}
						task.setChecUserNames(lawStr);
					}
				}
	    		model.put("list",taskBean);
	    		//excel文件的名称
	    		model.put("excelName", "一园一档环境执法信息"+DateUtil.getDateTime("yyyyMMddHHmmss")+".xls");
	    		//excel 文件的sheet
	    		model.put("sheetName", "sheet1");
	    		//标记序号
	    		model.put("flag", true);
				viewExcel.buildExcelDocument(model , workbook, request, response);
				return new ModelAndView(new ViewExcel(), model);
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
		/**
		 * 导出案件查看Excel
		 * @param seachBean
		 * @param request
		 * @param workbook
		 * @param response
		 * @return
		 */
		@RequestMapping(value = "/exportAjckExcel")
	    public ModelAndView exportAjckExcel(ParkSearchBean seachBean,HttpServletRequest request,HSSFWorkbook workbook,HttpServletResponse response){
	    	try {
	    		ViewExcel viewExcel = new ViewExcel();
	    		Map<String, Object> model = new  HashMap<String, Object>();
	    		//表头
	    		String[] columnNames  = {"案件编号","案件状态","案件名称","处罚主体","调查机构"};
	    		model.put("columnNames", columnNames);
	    		//list集合对应的值
	    		String[] dbColumnNames  = {"caseNumber","caseStatusName","caseName","punishSubject","researchOrgName"};
	    		model.put("dbColumnNames",dbColumnNames );
	    		//案件信息
	    		List<CaseBaseInfo> caseBean = caseBaseInfoMapper.getCaseListBean(seachBean);
	    		if(caseBean != null){
					for(CaseBaseInfo caseInfo : caseBean){
						if("0".equals(caseInfo.getCaseStatus().toString())){
							caseInfo.setCaseStatusName("进行中");
						}else{
							caseInfo.setCaseStatusName("已办结");
						}
					}
				}
	    		model.put("list",caseBean);
	    		//excel文件的名称
	    		model.put("excelName", "一园一档案件查看信息"+DateUtil.getDateTime("yyyyMMddHHmmss")+".xls");
	    		//excel 文件的sheet
	    		model.put("sheetName", "sheet1");
	    		//标记序号
	    		model.put("flag", true);
				viewExcel.buildExcelDocument(model , workbook, request, response);
				return new ModelAndView(new ViewExcel(), model);
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
	    }
		/**
		 * 根据查询获取监管对象气泡数
		 * @param request
		 * @param response
		 * @param seachBean
		 * @return
		 */
		@RequestMapping(value = "/getParkNum",method=RequestMethod.POST)
		@ResponseBody
	    public ResponseJson getParkNum(HttpServletRequest request,HttpServletResponse response,ParkSearchBean seachBean){
			List<LawEnforceObjectWithBLOBs> list = null;
	    	try{
	    		 list = lawEnforceObjectMapper.selectDataBySearchBean(seachBean);
	    	}catch(Exception e){
	    		e.printStackTrace();
	    		return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "失败", e.getMessage(), null);
	    	}
	    	return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "成功", "成功！", list.size());
	    }
		/**
		 * 根据查询条件获取环境执法气泡数
		 * @param request
		 * @param response
		 * @param seachBean
		 * @return
		 */
		@RequestMapping(value = "/getHjzfNum",method=RequestMethod.POST)
		@ResponseBody
	    public ResponseJson getHjzfNum(HttpServletRequest request,HttpServletResponse response,ParkSearchBean seachBean){
			List<Task> list = null;
	    	try{
	    		list = taskMapper.getListTaskBean(seachBean);
	    	}catch(Exception e){
	    		e.printStackTrace();
	    		return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "失败", e.getMessage(), null);
	    	}
	    	return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "成功", "成功！", list.size());
	    }
		/**
		 * 根据查询条件获取案件查办气泡数
		 * @param request
		 * @param response
		 * @param seachBean
		 * @return
		 */
		@RequestMapping(value = "/getAjcbNum",method=RequestMethod.POST)
		@ResponseBody
	    public ResponseJson getAjcbNum(HttpServletRequest request,HttpServletResponse response,ParkSearchBean seachBean){
			List<CaseBaseInfo> list = null;
	    	try{
	    		list = caseBaseInfoMapper.getCaseListBean(seachBean);	
	    	}catch(Exception e){
	    		e.printStackTrace();
	    		return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "失败", e.getMessage(), null);
	    	}
	    	return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "成功", "成功！", list.size());
	    }
}
