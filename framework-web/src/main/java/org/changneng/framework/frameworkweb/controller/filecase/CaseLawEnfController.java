package org.changneng.framework.frameworkweb.controller.filecase;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.entity.CaseLawUserSearch;
import org.changneng.framework.frameworkbusiness.entity.CheckUserChooseBean;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseLawEnforcement;
import org.changneng.framework.frameworkbusiness.service.filecase.CaseLawEnforcementService;
import org.changneng.framework.frameworkbusiness.service.filecase.GetUserInfoService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
  * 二期新增对接功能，对接需要选择 * 主要执法人员信息 
  * <p>Title:CaseLawEnfController </p>
  * <p>Description: </p>
  * <p>Company: </p> 
  * <AUTHOR>
 */
@RequestMapping("/caseLawEnf")
@Controller
public class CaseLawEnfController {

	private static Logger logger = LogManager.getLogger(CaseLawEnfController.class.getName());
	
	@Autowired
	private CaseLawEnforcementService lawEnforcementService;
	
	@Autowired
	private GetUserInfoService getUserInfoService;
	
	
	/**
	 * 根据不同的模块，和业务ID查询信息
	 * @param infoId
	 * @param modelerType
	 * @return
	 * <AUTHOR>
	 */
	@PostMapping("/law-user-list")
	@ResponseBody
	public List<CaseLawEnforcement>  delPenaltyInfo(String infoId,String modelerType){
		List<CaseLawEnforcement> lawUserList = new ArrayList<>();
		try {
			if(!ChangnengUtil.isNull(infoId) && !ChangnengUtil.isNull(modelerType)){ // 如果有ID我就去关联他对应的人员信息
				lawUserList = lawEnforcementService.getCaseLawEnforcementByModelerType(modelerType, infoId);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("请求主要执法人员信息 信息错误！");
			return lawUserList;
		}
		return lawUserList;
	}
	
	/**
	 * 编辑执法人员*检查人模态框加载
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @param lawUserIds 选中用户的ID集合
	 * @return
	 */
	@RequestMapping(value = "/chick-user-page")
	public ModelAndView chickUserPage(Model model, HttpServletRequest request, HttpServletResponse response,String checkLawUserId) {
		ModelAndView view = new ModelAndView("filecase/lawEnforcement/lawUserPage");
		view.addObject("checkLawUserId", checkLawUserId);
		return view;
	}
	
	
	@RequestMapping(value = "/getDate", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<CheckUserChooseBean> goSpecialTaskList( HttpServletRequest request,HttpServletResponse response,
			CaseLawUserSearch searchBean,Integer pageNum,Integer pageSize,String caseId){
		//SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		// 案件用户的权限不能走当前用户的权限，只能走创建案件的人他的权限。也就是基本信息创建人
		SysUsers sysUsers = getUserInfoService.getUserInfo(caseId);
		PageBean<CheckUserChooseBean> list = null;
		try {
			 list = lawEnforcementService.getCheckUserChooseList(searchBean,pageNum,pageSize,sysUsers);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
		}
		return list;
	}
}
