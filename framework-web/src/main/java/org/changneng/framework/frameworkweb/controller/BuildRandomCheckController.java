package org.changneng.framework.frameworkweb.controller;



import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.entity.FilesMonitor;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.RandomTaskBean;
import org.changneng.framework.frameworkbusiness.entity.SysResources;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RLawEnforce;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RLawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.randomPublic.RMattersDatabase;
import org.changneng.framework.frameworkbusiness.service.BuildRandomCheckService;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/** 
* <AUTHOR> 
* @version 2018年7月30日 下午8:27:20 
* 双随机公开的第一个模块“建立随机抽查”
*/
@RequestMapping(value = "/buildRandomCheck")
@Controller
public class BuildRandomCheckController {
	private static Logger logger = LogManager.getLogger(BuildRandomCheckController.class);
	
	@Autowired
	private BuildRandomCheckService buildRandomCheckService;
	@Autowired
	private ICommonService commonService;
	/**
	 * 进入“建立随机抽查”主页面
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/toPage")
	public ModelAndView toPage( RandomTaskBean randomTaskBean,String back,Model model,HttpServletRequest request,
		      HttpServletResponse response){
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		//randomTaskBean.setSelectToggle(4);
		model.addAttribute("randomTaskBean", randomTaskBean);
		ModelAndView mav = new ModelAndView("randomTaskOpen/buildRAndomCkeck");
		mav.addObject("sysUser",sysUsers);
		randomTaskBean.setSelectToggle(0);
		List<SysResources> listSysResource=commonService.queryRolesSysResources(sysUsers.getUsername(), randomTaskBean.getMenuId());
		mav.addObject("listSysResource",listSysResource);
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			// ----------------------把行政区划按指定顺序传给前台------------------
			Map<String, String> linkedParams = new LinkedHashMap<>();
			for (String key : paramsMap.keySet()) {
				if (!"belongProvince".equals(key) && !"belongCity".equals(key)
						&& !"belongCountry".equals(key)) {
					linkedParams.put(key, paramsMap.get(key));
				}
			}
			linkedParams.put("belongProvince", paramsMap.get("belongProvince"));
			linkedParams.put("belongCity", paramsMap.get("belongCity"));
			linkedParams.put("belongCountry", paramsMap.get("belongCountry"));
			// 防止多开窗口param混乱
			if (!"/buildRandomCheck/toPage".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(linkedParams);
			mav.addObject("params", params);
		}
		return mav;
	}
	/**
	 * 页面事项列表
	 * @param issueSearch
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/mattersList", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<RMattersDatabase> goMattersList(RMattersDatabase rMattersDatabase,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		// 1.获取用户信息，判断用户权限
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		PageBean<RMattersDatabase> mattersDatabase = null;
		try {
			mattersDatabase = buildRandomCheckService.getMattersList(sysUsers, rMattersDatabase, pNum, pageSize1);
			
		} catch (Exception e) {
			e.printStackTrace();
			
		}
		return mattersDatabase;
	}
	/**
	 * 特定事项的详情
	 * @param rMattersDatabase
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/mattersDatabase", method = RequestMethod.POST)
	public ModelAndView goMattersDatabase(RandomTaskBean randomTaskBean,Model model,String id,HttpServletRequest request, HttpServletResponse response
			){
		ModelAndView mav = new ModelAndView("randomTaskOpen/matterDetails");
		RMattersDatabase rMattersDatabase = null;
		try {
			//事项基本信息
			rMattersDatabase = buildRandomCheckService.getMattersDatabase(id);
			//附件信息
			List<FilesMonitor> FilesMonitors = buildRandomCheckService.getRMattersFiles(id);
			model.addAttribute("rMattersDatabase", rMattersDatabase);
			model.addAttribute("FilesMonitors", FilesMonitors);
			randomTaskBean.setSelectToggle(0);
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			List<SysResources> listSysResource=commonService.queryRolesSysResources(sysUsers.getUsername(), randomTaskBean.getMenuId());
			mav.addObject("listSysResource",listSysResource);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 人员信息model
	 * @param model
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/goPersonalModal")
	public ModelAndView goPersonalModal(Model model,String matterId,HttpServletRequest request, HttpServletResponse response
			){
		try {
			ModelAndView mav = new ModelAndView("randomTaskOpen/personals");
			model.addAttribute("matterId", matterId);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		
	}
	/**
	 * 根据管理部门的id查找所有人员信息
	 * @param managementId
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/getSysUsers", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<SysUsers> getSysUsersByManagementId(String managementId,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		PageBean<SysUsers> sysUsers = null ;
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		 try {
			sysUsers = buildRandomCheckService.geSysUsersList(managementId, pNum, pageSize1);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return sysUsers;
	}
	/**
	 * 管理对象库modal
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/goObjectModal")
	public ModelAndView goObject(Model model,String id,HttpServletRequest request, HttpServletResponse response
			){
		ModelAndView mav = null;
		try {
				//事项基本信息
			RMattersDatabase rMattersDatabase = buildRandomCheckService.getMattersDatabase(id);
			StringBuffer sbId = new StringBuffer();
			List<RLawEnforce> list = buildRandomCheckService.selectRLawEnforceByMatterId(rMattersDatabase.getMatterId());
			if(list.size() > 0){
				for(RLawEnforce rLawEnforce : list){
					if(sbId.length() !=0){
						sbId.append(",").append(rLawEnforce.getPolluterId());
					}else{
						sbId.append(rLawEnforce.getPolluterId());
					}
				}
			}
			String ids = sbId.toString();
			if(rMattersDatabase.getMatterId().equals("1") || rMattersDatabase.getMatterId().equals("2")){
				
				mav = new ModelAndView("randomTaskOpen/MattersObjectModal");//MattersObjectModal
			}else{
				mav = new ModelAndView("randomTaskOpen/objectModal");
			}
			model.addAttribute("rMattersDatabase", rMattersDatabase);
			model.addAttribute("ids", ids);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 加载建设项目的执法对象（6）
	 * @param rLawEnforceObject
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/getRLawEnforceObject", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<RLawEnforceObject> getRLawEnforceObject(RLawEnforceObject rLawEnforceObject ,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		PageBean<RLawEnforceObject> lawEnforceObject = null ;
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		 try {
			 lawEnforceObject = buildRandomCheckService.getRLawEnforceObject(rLawEnforceObject, pNum, pageSize1);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return lawEnforceObject;
	}
	/**
	 * 加载本系统的所有执法对象（1、2、3、4、5）
	 * @param lawEnforceObject
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/getHistoryLawEnforceObject", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<LawEnforceObject> getHistoryLawEnforceObject(LawEnforceObject lawEnforceObject,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		PageBean<LawEnforceObject> lawEnforceObjectt = null ;
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		 try {
			 lawEnforceObjectt = buildRandomCheckService.getHistoryLawEnforceObject(lawEnforceObject, pNum, pageSize1);
		 } catch (Exception e) {
			
			e.printStackTrace();
		}
		return lawEnforceObjectt;
	}
	/**
	 * 将选定的执法对象保存到数据表
	 * @param request
	 * @param id
	 * @param matterId
	 * @return
	 */
	
	@RequestMapping(value="/saveRLawEnforce")
	@ResponseBody
	public ResponseJson saveRLawEnforce(HttpServletRequest request,String id,String matterId ){
	   try {
		   buildRandomCheckService.saveRLawEnforce(id, matterId);
           return new ResponseJson().success(HttpStatus.OK.toString(),"0000","查询成功","信息查询成功",null);
        } catch (Exception e) {
        	e.printStackTrace();
            return null;
        }
	}
	/**
	 * 执法对象随机抽取model
	 * @param model
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/goObjectCheckModal")
	public ModelAndView goObjectCheck(Model model,String id,HttpServletRequest request, HttpServletResponse response
			){
		ModelAndView mav = null;
		try {
				//事项基本信息
			RMattersDatabase rMattersDatabase = buildRandomCheckService.getMattersDatabase(id);
			List<RLawEnforce> rLawEnforce = buildRandomCheckService.getObject(rMattersDatabase.getMatterId());
			mav = new ModelAndView("randomTaskOpen/objectCheckModal");
			model.addAttribute("listSize", rLawEnforce.size());
			model.addAttribute("rMattersDatabase", rMattersDatabase);
			model.addAttribute("rLawEnforce", rLawEnforce);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 将随机抽取执法对象保存
	 * @param request
	 * @param id
	 * @param matterId
	 * @return
	 */
	@RequestMapping(value="/saveRandomObject")
	@ResponseBody
	public ResponseJson saveRandomObject(HttpServletRequest request,Integer selectNumber,String matterId ){
	   try {
		   buildRandomCheckService.saveRandomObject(selectNumber, matterId);
        } catch (Exception e) {
            e.printStackTrace();
        }
	   return new ResponseJson().success(HttpStatus.OK.toString(),"0000","抽取成功","成功",null);
	}
	/**
	 * 抽取对象总数
	 * @param model
	 * @param matterId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/goMatterNumber")
	public ModelAndView goMatterNumber(Model model,String matterId,HttpServletRequest request, HttpServletResponse response
			){
		ModelAndView mav = null;
		try {
			if(matterId.equals("1") || matterId.equals("2")){
				mav = new ModelAndView("randomTaskOpen/objectNumbermt");
			}else{
				mav = new ModelAndView("randomTaskOpen/objectNumbermtOld");	
			}
			model.addAttribute("matterId", matterId);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		
	}
	/**
	 * 根据事项id查询已抽取的执法对象(id为1，2   加载建设项目对象)
	 * @param rLawEnforceObject
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/matterDatabaseNum", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<RLawEnforceObject> getMatterDatabaseNum(String matterId ,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		PageBean<RLawEnforceObject> lawEnforceObject = null ;
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		 try {
			 lawEnforceObject = buildRandomCheckService.getRLawEnforceObjectNum(matterId, pNum, pageSize1);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return lawEnforceObject;
	}
	/**
	 * 根据事项id查询已抽取的执法对象(id为1，2   加载建设项目对象)
	 * @param rLawEnforceObject
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/getOldObjectNum", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<LawEnforceObject> getOldObjectNum(String matterId ,HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize){
		PageBean<LawEnforceObject> lawEnforceObject = null ;
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize1 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize1 = Integer.parseInt(pageSize);
		}
		 try {
			 lawEnforceObject = buildRandomCheckService.getHistoryLawEnforceObjectNum(matterId, pNum, pageSize1);
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return lawEnforceObject;
	}
}
