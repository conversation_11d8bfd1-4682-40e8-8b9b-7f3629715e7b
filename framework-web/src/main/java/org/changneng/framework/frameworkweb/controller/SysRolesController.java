package org.changneng.framework.frameworkweb.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.dao.SysModulesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysRolesMapper;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkbusiness.service.ISysRolesService;
import org.changneng.framework.frameworkbusiness.service.SystemCodingService;
import org.changneng.framework.frameworkcore.utils.Const;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ViewExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;


@Controller
@RequestMapping(value="/sysRole")
public class SysRolesController {

	private static Logger logger = LogManager.getLogger(SysRolesController.class.getName());
	
	@Autowired
	public ISysRolesService roleService;
	
	@Autowired
	private ICommonService commonService;

	@Autowired
	private SysModulesMapper sysModulesMapper;

	
	@Autowired
	private SystemCodingService systemCodingService;
	@Autowired
	private SysRolesMapper sysRolesMapper;
	/**
	 * 角色管理主页-跳转
	 * @param request
	 * @return
	 */
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	@RequestMapping(value="/roleMain",method=RequestMethod.POST)
	public ModelAndView skipToRoleMain(HttpServletRequest request){
		ModelAndView mav=new ModelAndView("system/userMg/roleMgMain");
		logger.info("角色管理");
		//跳转页数
		int currentPage = 1;
		if(request.getParameter("currPage")!=null&&!"".equals(request.getParameter("currPage"))){
			Pattern pattern = Pattern.compile("[0-9]*"); 
			Matcher isNum = pattern.matcher(request.getParameter("currPage"));
			   if(isNum.matches()){
				   currentPage = Integer.parseInt(request.getParameter("currPage")); 
			   } 
		};
		
		//每页显示数量
		int pageSize = 10;
		if(request.getParameter("pageSize")!=null&&!"".equals(request.getParameter("pageSize"))){
			Pattern pattern = Pattern.compile("[0-9]*"); 
			Matcher isNum = pattern.matcher(request.getParameter("pageSize"));
			   if(isNum.matches()){
				   pageSize = Integer.parseInt(request.getParameter("pageSize")); 
			   } 
		};

		//用户信息
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		
		//快速查询参数
		SysRoles sysrole = new SysRoles();
		
		String areacode = "";
		String pro_code = request.getParameter("role_province");
		String city_code = request.getParameter("role_city");
		String coun_code = request.getParameter("role_country");
		if(coun_code!=null&&!"".equals(coun_code)){
			areacode= coun_code;
		}else{
			if(city_code!=null&&!"".equals(city_code)){
				areacode= city_code;
			}else{
				if(pro_code!=null&&!"".equals(pro_code)){
					areacode= pro_code;
				}else{
					areacode=sysUsers.getBelongAreaId();
				}
			}
		}
		mav.addObject("areacode",areacode);
		sysrole.setRoleAreacode(areacode);
		
		String role_name = request.getParameter("rolerealname");
		sysrole.setRoleName(role_name);
		mav.addObject("serachform",sysrole);
		
		
		
		try {
			//行政区信息
			Area area=commonService.queryAreaByAreacode(sysUsers.getBelongAreaId());
			mav.addObject("tarea", area);
			mav.addAllObjects(commonService.queryTareaByLevelAndCode(area));
			
			//列表数据
			PageBean<SysRoles> roles_list = roleService.querySysRolesList(sysrole, currentPage,pageSize);

			
			mav.addObject("rolesList",roles_list);
			
		} catch (Exception e) {
			
			e.printStackTrace();
		}
		return mav;
	}
	
	/**
	 * 查询用户列表
	 * @param search
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/roleList",method = RequestMethod.POST)
	@ResponseBody
	public 	PageBean<SysRoles> queryUserList(SysRoles sysrole,HttpServletRequest request,HttpServletResponse response){
		
		//跳转页数
		int currentPage = 1;
		if(request.getParameter("currPage")!=null&&!"".equals(request.getParameter("currPage"))){
			Pattern pattern = Pattern.compile("[0-9]*"); 
			Matcher isNum = pattern.matcher(request.getParameter("currPage"));
			   if(isNum.matches()){
				   currentPage = Integer.parseInt(request.getParameter("currPage")); 
			   } 
		};
		
		//用户信息
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		if(sysUsers.getIsSysadmin()!=1){
            List<SysModules> roleModuleByUsername = sysModulesMapper.getRoleModuleByUsername(sysUsers.getUsername());
            boolean nameExists = roleModuleByUsername.stream()
                    .anyMatch(SysModules -> SysModules.getModuleName().equals("角色管理"));

            if (!nameExists) {
                return null;
            }
        }



		//每页显示数量
		int pageSize = 10;
		if(request.getParameter("pageSize")!=null&&!"".equals(request.getParameter("pageSize"))){
			Pattern pattern = Pattern.compile("[0-9]*"); 
			Matcher isNum = pattern.matcher(request.getParameter("pageSize"));
			   if(isNum.matches()){
				   pageSize = Integer.parseInt(request.getParameter("pageSize")); 
			   } 
		};
		
		
		String areacode = "";
		String pro_code = request.getParameter("role_province");
		String city_code = request.getParameter("role_city");
		String coun_code = request.getParameter("role_country");
		if(coun_code!=null&&!"".equals(coun_code)){
			areacode= coun_code;
		}else{
			if(city_code!=null&&!"".equals(city_code)){
				areacode= city_code;
			}else{
				if(pro_code!=null&&!"".equals(pro_code)){
					areacode= pro_code;
				}else{
					areacode=sysUsers.getBelongAreaId();
				}
			}
		}
		sysrole.setRoleAreacode(areacode);
		
		String role_name = request.getParameter("rolerealname");
		sysrole.setRoleName(role_name);
		
		return roleService.querySysRolesList(sysrole, currentPage,pageSize);
	}
	
	/**
	 * 新增保存角色
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/addrole",method=RequestMethod.POST)
	@SysLogPoint(businessType=businessType.ADD_ROLE,dbOptType=dbType.ADD)
	@ResponseBody
	public Map<String,String> addRoel(@Validated SysRoles sysrole,BindingResult result,HttpServletRequest request){
		HashMap<String, String> retMap = new HashMap<String, String>();
		
		String areacode = "";
		String pro_code = request.getParameter("province_code");
		String city_code = request.getParameter("city_code");
		String coun_code = request.getParameter("country_code");
		if(coun_code!=null&&!"".equals(coun_code)){
			areacode= coun_code;
		}else{
			if(city_code!=null&&!"".equals(city_code)){
				areacode= city_code;
			}else{
				if(pro_code!=null&&!"".equals(pro_code)){
					areacode= pro_code;
				}
			}
		}
		if(!"".equals(areacode)){
			sysrole.setRoleAreacode(areacode);
		}
		
		//用户信息
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		
		int res = 0;
		
		try {
			if(!result.hasErrors()){
				if(sysrole.getRoleId()!=null&&!"".equals(sysrole.getRoleId())){
					
					res = roleService.editRole(sysrole);
				}else{//新增
					String role_num = systemCodingService.getSysRoleCoding(sysUsers.getBelongAreaId());//角色编号
					sysrole.setRoleNum(role_num);
					sysrole.setRoleName("ROLE_"+role_num);
					res = roleService.addRole(sysrole);
				}
				
				
				if(res==1){//保存成功
					retMap.put("type", Const.RESULT_SUCCESS);
				}else{//保存失败
					retMap.put("type", Const.RESULT_ERROR);
					retMap.put("message","保存数据失败");
				}
			}else{
				retMap.put("type",Const.RESULT_ERROR);
				retMap.put("message",result.getFieldError().getDefaultMessage());
			}
				
		} catch (Exception e) {
			e.printStackTrace();
			retMap.put("type", Const.RESULT_ERROR);
			retMap.put("message","后台出现异常");
		}
		
		return retMap;
	}
	
	
	/**
	 *打开角色编辑模态框
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/editRolePage")
	public ModelAndView goCemUnEIApro(HttpServletRequest request){
		
		ModelAndView mav=new ModelAndView("system/userMg/editRoleModal");
		try{
			String roleID=request.getParameter("roleID");
			
			if(!"".equals(roleID)&&roleID!=null){
				
				SysRoles sysrole = roleService.queryRoleById(roleID);
				
				//用户信息
				SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
				
				//行政区信息
				Area area=commonService.queryAreaByAreacode(sysUsers.getBelongAreaId());
				mav.addAllObjects(commonService.queryTareaByLevelAndCode(area));
				
				mav.addObject("Role", sysrole);
				
			}
			
		}catch(Exception e){
			e.printStackTrace();
		}
		return mav;
	}
	
	
	/**
	 * 删除角色
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/delrole",method=RequestMethod.POST)
	@SysLogPoint(businessType=businessType.DELETE_ROLE,dbOptType=dbType.DELETE)
	@ResponseBody
	public Map<String,String> delRoel(HttpServletRequest request){
		HashMap<String, String> retMap = new HashMap<String, String>();
		
		String roleID = request.getParameter("roleID");
		
		int res = 0;
		try {
			res = roleService.updateRoleState(roleID);
			
			if(res==1){//删除成功
				retMap.put("type", "success");
			}else if(res==-1){//存在关联用户
				retMap.put("type", "no");
			}else{//删除失败
				retMap.put("type", "error");
			}
		} catch (Exception e) {
			retMap.put("type", "error");
			e.printStackTrace();
		}
		
		return retMap;
	}
	
	/**
	 * 查询角色
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/queryrole")
	@ResponseBody
	public Map<String,Object> queryRoels(HttpServletRequest request){
		HashMap<String, Object> resMap = new HashMap<String, Object>();
		
		String roleID = request.getParameter("roleID");
		
		try {
			SysRoles sysrole = roleService.queryRoleById(roleID);
			resMap.put("data", JacksonUtils.toJsonString(sysrole));
			resMap.put("type", "success");
		} catch (Exception e) {
			e.printStackTrace();
			resMap.put("type", "error");
		}
		
		return resMap;
	}
	
	
	/**
	 * 加载权限树
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/getPowers")
	@ResponseBody
	public Map<String,Object> queryAllPowerForZtree(HttpServletRequest request){
		HashMap<String, Object> resMap = new HashMap<String, Object>();
		String roleID = request.getParameter("roleID");
		try {
			
			List<ZTreeNodeBean> ztreeData = roleService.loadPowerTree(roleID);
			
			String zNodes =JacksonUtils.toJsonString(ztreeData);
			
			resMap.put("data", zNodes);
			resMap.put("type", "success");
		} catch (Exception e) {
			resMap.put("type", "error");
			e.printStackTrace();
		}
		
		return resMap;
	}
	
	
	
	/**
	 * 根据角色ID保存其权限
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/setPowers",method=RequestMethod.POST)
	@ResponseBody
	public Map<String,String> setPowersForRoel(HttpServletRequest request){
		HashMap<String, String> retMap = new HashMap<String, String>();
		String RoleID =request.getParameter("roleID");
		String poweIDs = request.getParameter("powerIDs");
		if(RoleID!=null&&!"".equals(RoleID)){
			try {
				int res = roleService.setPowersForRoels(RoleID,poweIDs);
				if(res==1){
					retMap.put("type", "success");
				}else{
					retMap.put("type", "error");
				}
			} catch (Exception e) {
				retMap.put("type", "error");
				e.printStackTrace();
			}
		}else{
			retMap.put("type", "exception");
		}
		return retMap;
	}
	
	/**
     * 地区级联查询
     * @param request
     * @return
     */
	@RequestMapping(value="/cascaded")
	@ResponseBody
	public Map<String,Object> cascaded(HttpServletRequest request){
		HashMap<String,Object> resMap = new HashMap<String,Object>();
	   try {
           List<Area> tareaList=commonService.queryCascaded(request.getParameter("code"));
           resMap.put("result", "success");
           resMap.put("data", tareaList);
        } catch (Exception e) {
            resMap.put("result", "error");
        }
	   
	   return resMap;
	}
	
    @RequestMapping(value = "/exportRoles")
    public ModelAndView exportUnitJZBL(String roleName,String belongAreaId,HttpServletRequest request,HSSFWorkbook workbook,HttpServletResponse response){
    	try {
    		ViewExcel viewExcel = new ViewExcel();
    		Map<String, Object> model = new  HashMap<String, Object>();
    		//表头
    		String[] columnNames  = {"角色编号","应用范围","角色名称","角色描述"};
    		model.put("columnNames", columnNames);
    		//list集合对应的值
    		String[] dbColumnNames  = {"roleNum", "departName", "roleRealName", "roleDesc"};
    		model.put("dbColumnNames",dbColumnNames );
    		//用户信息
    		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
    		SysRoles sysrole = new SysRoles();
    		sysrole.setRoleName(roleName);
    		sysrole.setRoleAreacode(belongAreaId);
    		List<SysRoles> list  = roleService.queryRolesList(sysrole);
    		model.put("list",list );
    		//excel文件的名称
    		model.put("excelName", "角色信息"+DateUtil.getDateTime("yyyyMMddHHmmss")+".xls");
    		//excel 文件的sheet
    		model.put("sheetName", "sheet1");
    		//标记序号
    		model.put("flag", true);
			viewExcel.buildExcelDocument(model , workbook, request, response);
			return new ModelAndView(new ViewExcel(), model);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
    }
}
