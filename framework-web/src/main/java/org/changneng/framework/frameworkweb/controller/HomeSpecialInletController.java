package org.changneng.framework.frameworkweb.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.ActionlRequireFiless;
import org.changneng.framework.frameworkbusiness.entity.HomeSpecialInlet;
import org.changneng.framework.frameworkbusiness.entity.SceneItemDatabase;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TaskPermission;
import org.changneng.framework.frameworkbusiness.service.HomeSpecialInletService;
import org.changneng.framework.frameworkbusiness.service.SpecialTaskService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 首页专项入口配置
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "HomeSpecialInlet")
public class HomeSpecialInletController {
	@Autowired
	private HomeSpecialInletService homeSpecialInletService;
	@Autowired
	private SpecialTaskService specialTaskService;
	/**
	 * 设置首页快捷入口页面
	 * @param request
	 * @param response
	 * @param m
	 * @param back
	 * @return
	 */
	@RequestMapping("/toPage")
	public ModelAndView toPage(HttpServletRequest request, HttpServletResponse response, Model m) {
		ModelAndView mav = new ModelAndView("special/homeSpecialInlet/toPage");
		try {
			List<HomeSpecialInlet> homeSpecialList = homeSpecialInletService.selectHomeSpecialList();
			if(!ChangnengUtil.isNull(homeSpecialList)) {
				String params = JacksonUtils.toJsonString(homeSpecialList);
				mav.addObject("homeSpecialList", params);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 保存设置的快捷入口信息
	 * @param homeSpecialInletList
	 * @return
	 */
	@RequestMapping(value = "saveInfo", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson saveInfo(@RequestBody List<HomeSpecialInlet> homeSpecialInletList) {
		ResponseJson json = new ResponseJson();
		try {
			homeSpecialInletService.saveInfo(homeSpecialInletList);
			json.success("200", "200", "保存成功", "保存成功", null);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			json.failure("500", "500", "系统错误，保存失败", e.getMessage(), null);
			//e.printStackTrace();
		}
		return json;
	}
	/**
	 * 选择专项行动 公共模态窗   需要的两个参数  页面中专项行动id、name 在页面中的id 
	 * @param pageState  页面状态  主要用于设置专项单选或多选  2多选  否则为单选
	 * @param idMark   传入页面要绑定的id（页面元素id）
	 * @param nameMark 传入页面要绑定的name（页面元素id）
	 * @param nameMark 传入页面要绑定的type（页面元素id）
	 * @param request
	 * @param response
	 * @param m
	 * @return
	 */
	@RequestMapping(value = "chooseSpecialModel")
	public ModelAndView chooseSpecialModel(String pageState,String idMark,String nameMark,String typeMark,HttpServletRequest request, HttpServletResponse response, Model m) {
		ModelAndView mav = new ModelAndView("special/homeSpecialInlet/model/chooseSpecial");
		
		mav.addObject("pageState", pageState);			
		mav.addObject("idMark", idMark);			
		mav.addObject("nameMark", nameMark);			
		mav.addObject("typeMark", typeMark);			

		return mav;
	}
	/**
	 * 模态窗查询列表
	 * @param request
	 * @param response
	 * @param specialTask
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "chooseSpecialList", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson chooseSpecialList(HttpServletRequest request,HttpServletResponse response,
			SceneSysSpecialModel specialTask,Integer pageNum,Integer pageSize) {
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			PageBean<SceneSysSpecialModel> pageBean = specialTaskService.specialTaskListforModel(specialTask,pageNum,pageSize,sysUser);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务信息成功！", pageBean);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	/**
	 * 附件预览model
	 * @param actionId
	 * @param request
	 * @param response
	 * @param m
	 * @return
	 */
	@RequestMapping(value = "attachmentPreviewer")
	public ModelAndView attachmentPreviewer(String actionId,HttpServletRequest request, HttpServletResponse response, Model m) {
		ModelAndView mav =  new ModelAndView("special/homeSpecialInlet/model/attachmentPreviewer");
		try {
			//找到该专项行动信息的附件路径，没有则返回空
			ActionlRequireFiless actionlRequireFiless = homeSpecialInletService.selectAttachmentByActionId(actionId);
			if(!ChangnengUtil.isNull(actionlRequireFiless)) {
				mav.addObject("fileInfo",JacksonUtils.toJsonString(actionlRequireFiless));
				mav.addObject("id", actionlRequireFiless.getId());
				mav.addObject("fileType", actionlRequireFiless.getFileType());
				mav.addObject("fileUrl", actionlRequireFiless.getFileUrl());
			}
		 } catch (Exception e) {
			 mav = new ModelAndView("error/404");
			e.printStackTrace();
		}
		return mav;
	}
	/**
	 * 验证选择的专项行动是否被关联过
	 * @param homeSpecialInletList
	 * @return
	 */
	@RequestMapping(value = "checkoutSpecial", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson checkoutSpecial(String strId,String id) {
		ResponseJson json = new ResponseJson();
		try {
			List<HomeSpecialInlet> homeSpecialInletList = homeSpecialInletService.selectBySceneSysspecailId(strId,id);
			if(homeSpecialInletList.isEmpty()) {
				json.success("200", "200", "验证成功", "验证成功！", null);				
			}else {
				json.failure("400", "400", "验证失败", "该专项行动已设置过快捷入口！", null);				
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			json.failure("500", "500", "系统错误，验证时发生错误", e.getMessage(), null);
			e.printStackTrace();
		}
		return json;
	}
	
	/**
	 * 专项行动模板页面跳转
	 * @param id 根据模版id查询该类型下的检查项
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/specialRefineTemplate")
	public ModelAndView addSysRefineTemplate(String id,Model model, HttpServletRequest request,
			HttpServletResponse response) {
		ModelAndView view = new ModelAndView("special/homeSpecialInlet/specialRefineTemplate");
		SceneSysSpecialModel sceneSysSpecialModel = homeSpecialInletService.selectSceneSysSecailById(id);
		view.addObject("sceneSysSpecialModel", sceneSysSpecialModel);
		return view;
	}
	/**
	 * 检查项输出
	 * 
	 * @param request
	 * @param response
	 * @param templateType 根据模版类型查询该类型下的检查项
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkItemList", method = RequestMethod.POST)
	@ResponseBody
	public List<SceneItemDatabase> checkItemList(String templateType,HttpServletRequest request, HttpServletResponse response) throws Exception {
		try {
			//查询该模板下的所有检查项
			List<SceneItemDatabase> sceneItemList = homeSpecialInletService.selectSceneItemByTemplateType(templateType);
			return sceneItemList;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 专项行动加载模版model
	 * @param request
	 * @param response
	 * @param m
	 * @return
	 */
	@RequestMapping(value = "specialTemplate")
	public ModelAndView specialTemplateModel(HttpServletRequest request, HttpServletResponse response, Model m) {
		ModelAndView mav = new ModelAndView("special/homeSpecialInlet/model/specialTemplateModel");
		return mav;
	}
	/**
	 * 专项行动model查询
	 * @param request
	 * @param response
	 * @param specialTask
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "specialTemplateList", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson specialTemplateList(HttpServletRequest request,HttpServletResponse response,
			SceneSysSpecialModel specialTask,Integer pageNum,Integer pageSize) {
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			PageBean<SceneSysSpecialModel> pageBean = homeSpecialInletService.selectSpecialTemplateList(specialTask,pageNum,pageSize,sysUser);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务模板成功！", pageBean);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	
	/**
	 * 保存模版的检查项信息
	 * @param homeSpecialInletList
	 * @return
	 */
	@RequestMapping(value = "saveTemplate", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson saveTemplate(String sceneSysItem,String templateType) {
		ResponseJson json = new ResponseJson();
		try {
			homeSpecialInletService.saveTempleteInfo(sceneSysItem, templateType);
			json.success("200", "200", "保存成功", "保存成功", null);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			json.failure("500", "500", "系统错误，保存失败", e.getMessage(), null);
			e.printStackTrace();
		}
		return json;
	}
	@RequestMapping(value="/toTaskBatchAssociate")
	public ModelAndView toCaseBatchAssociate(HttpServletRequest request,String specialActionId,String specialActionName,String applyAreaCode){
		ModelAndView modelAndView = new ModelAndView("special/task/taskBatchAssociationList");
		modelAndView.addObject("specialActionId", specialActionId);
		modelAndView.addObject("specialActionName", specialActionName);
		modelAndView.addObject("applyAreaCode", applyAreaCode);
		return modelAndView;
	}
	
	@RequestMapping(value="taskBatchAssociationList",method=RequestMethod.POST)
	@ResponseBody
	public PageBean<TaskPermission> caseBatchAssociationList(HttpServletRequest request,HttpServletResponse response,TaskPermission taskPermission){
		
		PageBean<TaskPermission> pageBean = null;
		try {
			pageBean = homeSpecialInletService.getTaskPermissionList(taskPermission);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return pageBean;
	}
	/**
	 * 批量关联\取消关联
	 * @param request
	 * @param response
	 * @param taskPermission
	 * @return
	 */
	@RequestMapping(value="batchAssociated",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson batchAssociated(HttpServletRequest request,HttpServletResponse response,String taskIds,String specialActionId,String isAssociated,String specialActionName){
		ResponseJson json = new ResponseJson();
		try {
			homeSpecialInletService.batchAssociated(taskIds, specialActionId, isAssociated,specialActionName);
			if(isAssociated.equals("0")) {
				json.success("200", "200", "批量关联成功", "批量关联成功", null);
			}else if (isAssociated.equals("1")) {
				json.success("200", "200", "批量取消成功", "批量取消成功", null);
			}
		} catch (Exception e) {
			e.printStackTrace();
			json.failure("500", "500", "系统错误，批量操作时发生错误", e.getMessage(), null);
		}
		return json;
	}
}
