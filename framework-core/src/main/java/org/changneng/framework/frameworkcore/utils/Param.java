package org.changneng.framework.frameworkcore.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Param {
	@JsonProperty
	private String FieldName;
	@JsonProperty
	private String Value;
	@JsonProperty
	private String Operator;
	@JsonIgnore
	public String getFieldName() {
		return FieldName;
	}
	@JsonIgnore
	public void setFieldName(String fieldName) {
		FieldName = fieldName;
	}
	@JsonIgnore
	public String getValue() {
		return Value;
	}
	@JsonIgnore
	public void setValue(String value) {
		Value = value;
	}
	@JsonIgnore
	public String getOperator() {
		return Operator;
	}
	@JsonIgnore
	public void setOperator(String operator) {
		Operator = operator;
	}
	
	
}
