#jdbc.driverClassName = oracle.jdbc.driver.OracleDriver

#jdbc.url = *****************************************
#jdbc.username = system
#jdbc.password = wangzm

#druid.initialSize=15
#druid.maxActive=20
#druid.minIdle=0

#jdbc.driverClassName=net.sourceforge.jtds.jdbc.Driver
#jdbc.url=***************************************************************************


#jdbc.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
#jdbc.url=***********************************************************************************
#jdbc.url2=jdbc\:sqlserver\://10.87.10.35\:1433;DatabaseName\=SpecialAction2016
#jdbc.username1=chn12369
#jdbc.username=sa
#jdbc.password=ChangNeng12369
#jdbc.password1=P@ssw0rd\=ChangNeng\!@\#^(


#jdbc.driverClassName=com.mysql.jdbc.Driver
#jdbc.url=jdbc\:mysql\://10.205.2.112\:3306/dalianbing_bs?useUnicode\=true&amp;characterEncoding\=utf8&amp;autoReconnect\=true&amp;failOverReadOnly\=false&allowMultiQueries=true
#?useUnicode=true&amp;characterEncoding=utf8&amp;autoReconnect=true&amp;failOverReadOnly=false
#jdbc.username=root
#jdbc.password=dlbdb12369
#jdbc.driverClassName=com.mysql.jdbc.Driver

#jdbc.url=jdbc\:mysql\://192.168.0.212\:3306/security?useUnicode\=true&amp;characterEncoding\=utf8&amp;autoReconnect\=true&amp;failOverReadOnly\=false&allowMultiQueries\=true


#?useUnicode=true&amp;characterEncoding=utf8&amp;autoReconnect=true&amp;failOverReadOnly=false
#jdbc.username=root
#jdbc.password=123456

jdbc.driverClassName=oracle.jdbc.driver.OracleDriver

#jdbc.url=*******************************************
#jdbc.url=********************************************
#jdbc.url=******************************************
jdbc.url=*********************************************************
jdbc.username=zxxd
jdbc.password=Chn1qaz_

#jdbc.username=CHNFJ
#jdbc.password=rootroot

#jdbc.username=basefj
#jdbc.password=rootroot

druid.initialSize=50
druid.maxActive=100
druid.minIdle=15

#jdbc.driverClass=com.mysql.jdbc.Driver
#jdbc.jdbcUrl = ********************************
#jdbc.user = root
#jdbc.password = 12345
#jdbc.miniPoolSize = 1
#jdbc.maxPoolSize = 20
#jdbc.initialPoolSize = 1
#jdbc.maxIdleTime = 25000
#jdbc.acquireIncrement = 1

#jdbc.acquireRetryAttempts = 30
#jdbc.acquireRetryDelay = 1000
#jdbc.testConnectionOnCheckin = true
#jdbc.automaticTestTable = c3p0TestTable
#jdbc.idleConnectionTestPeriod = 18000
#jdbc.checkoutTimeout=3000
