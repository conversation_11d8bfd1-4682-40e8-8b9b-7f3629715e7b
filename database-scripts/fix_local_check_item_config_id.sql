-- =====================================================
-- LOCAL_CHECK_ITEM表CONFIG_ITEM_ID字段修复脚本
-- 修复日期：2025-02-01
-- 修复内容：
-- 1. 新增ITEM_LEVEL_CODE字段存储层级标识码
-- 2. 修复CONFIG_ITEM_ID字段存储真实的CHECK_ITEM_CONFIG表主键ID
-- 3. 修复问题简述字段的缓存显示问题
-- =====================================================

-- 1. 新增ITEM_LEVEL_CODE字段
-- 用于存储层级标识码（如：0_0、0_1、2_1等）
ALTER TABLE LOCAL_CHECK_ITEM ADD ITEM_LEVEL_CODE VARCHAR2(50);

-- 添加字段注释
COMMENT ON COLUMN LOCAL_CHECK_ITEM.ITEM_LEVEL_CODE IS '检查项层级标识码（如：0_0、0_1、2_1等）';

-- 2. 更新CONFIG_ITEM_ID字段注释，明确其用途
COMMENT ON COLUMN LOCAL_CHECK_ITEM.CONFIG_ITEM_ID IS '关联CHECK_ITEM_CONFIG表的真实主键ID（外键关联）';

-- 3. 数据迁移脚本（可选执行）
-- 将现有CONFIG_ITEM_ID字段中的层级标识码迁移到ITEM_LEVEL_CODE字段
-- 注意：执行前请备份数据！

/*
-- 备份现有数据到临时表
CREATE TABLE LOCAL_CHECK_ITEM_BACKUP AS 
SELECT * FROM LOCAL_CHECK_ITEM WHERE FORM_TYPE = 1;

-- 将层级标识码从CONFIG_ITEM_ID迁移到ITEM_LEVEL_CODE
UPDATE LOCAL_CHECK_ITEM 
SET ITEM_LEVEL_CODE = CONFIG_ITEM_ID,
    CONFIG_ITEM_ID = NULL
WHERE FORM_TYPE = 1 
  AND CONFIG_ITEM_ID IS NOT NULL 
  AND REGEXP_LIKE(CONFIG_ITEM_ID, '^[0-9]+_[0-9]+$');

-- 验证迁移结果
SELECT 
    ID,
    CHECK_ITEM_NAME,
    CONFIG_ITEM_ID,
    ITEM_LEVEL_CODE,
    FORM_TYPE,
    PROBLEM_DESC
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1
ORDER BY CREATE_TIME DESC;
*/

-- 4. 创建索引优化查询性能
CREATE INDEX IDX_LOCAL_CHECK_ITEM_LEVEL_CODE ON LOCAL_CHECK_ITEM(ITEM_LEVEL_CODE);
CREATE INDEX IDX_LOCAL_CHECK_ITEM_CONFIG_ID ON LOCAL_CHECK_ITEM(CONFIG_ITEM_ID);

-- 5. 数据完整性检查脚本
-- 检查环境监管一件事数据的字段完整性
SELECT 
    '环境监管一件事数据统计' AS 检查项目,
    COUNT(*) AS 总数据量,
    COUNT(CONFIG_ITEM_ID) AS 有配置项ID数量,
    COUNT(ITEM_LEVEL_CODE) AS 有层级标识码数量,
    COUNT(CASE WHEN PROBLEM_DESC IS NOT NULL AND TRIM(PROBLEM_DESC) != '' THEN 1 END) AS 有问题简述数量
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1;

-- 检查层级标识码格式是否正确
SELECT 
    '层级标识码格式检查' AS 检查项目,
    COUNT(*) AS 总数量,
    COUNT(CASE WHEN REGEXP_LIKE(ITEM_LEVEL_CODE, '^[0-9]+_[0-9]+$') THEN 1 END) AS 格式正确数量,
    COUNT(CASE WHEN ITEM_LEVEL_CODE IS NOT NULL AND NOT REGEXP_LIKE(ITEM_LEVEL_CODE, '^[0-9]+_[0-9]+$') THEN 1 END) AS 格式错误数量
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1 AND ITEM_LEVEL_CODE IS NOT NULL;

-- 检查CONFIG_ITEM_ID是否为有效的UUID格式
SELECT 
    'CONFIG_ITEM_ID格式检查' AS 检查项目,
    COUNT(*) AS 总数量,
    COUNT(CASE WHEN LENGTH(CONFIG_ITEM_ID) = 32 THEN 1 END) AS UUID格式数量,
    COUNT(CASE WHEN CONFIG_ITEM_ID IS NOT NULL AND LENGTH(CONFIG_ITEM_ID) != 32 THEN 1 END) AS 非UUID格式数量
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1 AND CONFIG_ITEM_ID IS NOT NULL;

-- 6. 清理空字符串数据，修复缓存显示问题
-- 将空字符串的PROBLEM_DESC设置为NULL
UPDATE LOCAL_CHECK_ITEM 
SET PROBLEM_DESC = NULL 
WHERE PROBLEM_DESC IS NOT NULL 
  AND TRIM(PROBLEM_DESC) = '';

-- 将空字符串的ITEM_LEVEL_CODE设置为NULL
UPDATE LOCAL_CHECK_ITEM 
SET ITEM_LEVEL_CODE = NULL 
WHERE ITEM_LEVEL_CODE IS NOT NULL 
  AND TRIM(ITEM_LEVEL_CODE) = '';

-- 将空字符串的CONFIG_ITEM_ID设置为NULL
UPDATE LOCAL_CHECK_ITEM 
SET CONFIG_ITEM_ID = NULL 
WHERE CONFIG_ITEM_ID IS NOT NULL 
  AND TRIM(CONFIG_ITEM_ID) = '';

-- 提交更改
COMMIT;

-- 7. 验证修复结果
-- 查看最近的环境监管一件事数据
SELECT 
    ID,
    CHECK_ITEM_NAME,
    CONFIG_ITEM_ID,
    ITEM_LEVEL_CODE,
    PROBLEM_DESC,
    FORM_TYPE,
    CREATE_TIME
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1 
  AND CREATE_TIME >= SYSDATE - 30  -- 最近30天的数据
ORDER BY CREATE_TIME DESC;

-- 统计修复后的数据分布
SELECT 
    FORM_TYPE,
    COUNT(*) AS 数据总量,
    COUNT(CONFIG_ITEM_ID) AS 有配置项ID,
    COUNT(ITEM_LEVEL_CODE) AS 有层级标识码,
    COUNT(CASE WHEN PROBLEM_DESC IS NOT NULL THEN 1 END) AS 有问题简述
FROM LOCAL_CHECK_ITEM 
GROUP BY FORM_TYPE
ORDER BY FORM_TYPE;

-- =====================================================
-- 修复完成提示
-- =====================================================
SELECT '修复脚本执行完成！' AS 状态,
       '请检查上述查询结果确认数据完整性' AS 提示,
       SYSDATE AS 执行时间
FROM DUAL;
