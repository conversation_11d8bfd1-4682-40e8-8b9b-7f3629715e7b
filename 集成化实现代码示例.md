# 环境监管一件事功能集成化实现代码示例

## 📋 实现概述

本文档展示了如何将环境监管一件事的历史数据加载逻辑集成到现有的页面跳转接口中，而不是使用单独的数据加载接口。

## 🔧 Controller层集成实现

### 页面跳转接口 `/xcjc` 的集成逻辑

```java
@RequestMapping(value = "/xcjc", method = RequestMethod.POST)
@PutSessionValue
public ModelAndView localExamine(Model model, LawObjectTypeBean lawObj, HttpServletRequest request,
    HttpServletResponse response, @RequestParam(value = "localChickId", required = false) String localChickId,String menuId
) throws Exception {
    
    try {
        // 查询 "环境执法一件事"的初始化树 - 始终执行，无论哪种类型都需要
        List<CheckItemConfigTreeVO> checkItemTree = checkItemConfigService.getTreeStructure();
        model.addAttribute("checkItemTree", checkItemTree);

        // ... 其他初始化逻辑 ...

        if ("0".equals(lawObj.getParentUrl())) { 
            // 历史途径
            lawObjectList = zfdxManagerService.selectByTaskId(lawObj.getTaskId());
            // 查询主表数据
            localCheak = LocalExamineService.getLocalCheickItem(lawObj);
            
            if (localCheak != null && !"".equals(localCheak.getId())) {
                // 根据主表的 formType 字段判断检查项类型并处理历史数据
                if (Integer.valueOf(1).equals(localCheak.getFormType())) {
                    // 环境监管一件事逻辑：查询 FORM_TYPE=1 的历史数据
                    try {
                        List<LocalCheckItem> envSupervisionItems = LocalExamineService.loadEnvSupervisionItems(localCheak.getId());
                        // 将历史数据传递给前端用于表格回显
                        model.addAttribute("envSupervisionItems", envSupervisionItems);
                        model.addAttribute("localCheckItemStatus", envSupervisionItems.isEmpty() ? "0" : "1");
                        logger.info("历史途径-成功加载环境监管一件事数据，任务ID: {}, 数据条数: {}", lawObj.getTaskId(), envSupervisionItems.size());
                    } catch (Exception e) {
                        logger.error("历史途径-加载环境监管一件事数据失败，任务ID: {}", lawObj.getTaskId(), e);
                        model.addAttribute("localCheckItemStatus", "0");
                    }
                } else {
                    // 保持原有检查项逻辑不变
                    localCheckItem = LocalExamineService.getChickItemItem(lawObj, localCheak.getId());
                    if (localCheckItem == null || localCheckItem.size() == 0) {
                        model.addAttribute("localCheckItemStatus", "0");
                    }
                    model.addAttribute("localCheckItem", localCheckItem);
                }
            }
        } else {
            // 编辑模式
            // 查询主表数据
            localCheak = LocalExamineService.getLocalCheickItem(lawObj);
            
            // ... 新建记录初始化逻辑 ...
            
            // 根据主表的 formType 字段判断检查项类型并处理历史数据
            if (localCheak != null && Integer.valueOf(1).equals(localCheak.getFormType())) {
                // 环境监管一件事逻辑：查询 FORM_TYPE=1 的历史数据
                try {
                    List<LocalCheckItem> envSupervisionItems = LocalExamineService.loadEnvSupervisionItems(localCheak.getId());
                    // 将历史数据传递给前端用于表格回显
                    model.addAttribute("envSupervisionItems", envSupervisionItems);
                    logger.info("编辑模式-成功加载环境监管一件事数据，任务ID: {}, 数据条数: {}", lawObj.getTaskId(), envSupervisionItems.size());
                } catch (Exception e) {
                    logger.error("编辑模式-加载环境监管一件事数据失败，任务ID: {}", lawObj.getTaskId(), e);
                }
            } else {
                // 保持原有检查项逻辑不变 - 这里可以添加原有检查项的加载逻辑
                // 注意：原有检查项的加载逻辑在后续代码中处理
            }
        }
        
        return mav;
    } catch (Exception e) {
        e.printStackTrace();
    }
    return mav;
}
```

## 🎨 前端集成实现

### JavaScript历史数据加载函数

```javascript
// 加载环境监管一件事历史数据（从页面初始化数据中获取）
function loadEnvSupervisionHistoryData() {
    // 检查是否有服务端传递的历史数据
    <c:if test="${not empty envSupervisionItems}">
        try {
            var historyData = [];
            <c:forEach items="${envSupervisionItems}" var="item">
                historyData.push({
                    configItemId: '${item.configItemId}',
                    itemName: '${item.checkItemName}',
                    result: '${item.checkItemResult}',
                    problemDesc: '${item.problemDesc}' || ''
                });
            </c:forEach>

            if (historyData.length > 0) {
                console.log('从页面初始化数据中加载到历史数据:', historyData);

                // 恢复表单状态
                historyData.forEach(function(item) {
                    var configItemId = item.configItemId;
                    if (configItemId) {
                        // 查找匹配的单选按钮（configItemId格式为 parentIndex_childIndex）
                        var $targetRadio = $('input[type="radio"][name*="problem_"][name*="_' + configItemId + '"][value="' + item.result + '"]');
                        if ($targetRadio.length > 0) {
                            $targetRadio.prop('checked', true);
                            console.log('恢复单选按钮状态:', configItemId, '=', item.result);
                        }

                        // 恢复问题简述到localStorage
                        if (item.problemDesc) {
                            localStorage.setItem('problemDesc_' + configItemId, item.problemDesc);
                            console.log('恢复问题简述:', configItemId, '=', item.problemDesc);
                        }
                    }
                });

                // 如果有历史数据，自动显示环境监管一件事表单
                $('#envSupervisionForm').show();
                $('#envSupervisionBtn').text('隐藏"环境监管一件事"表单');

                console.log('环境监管一件事历史数据加载完成，共恢复', historyData.length, '条数据');
            }
        } catch (error) {
            console.error('处理环境监管一件事历史数据时发生错误:', error);
        }
    </c:if>
    
    <c:if test="${empty envSupervisionItems}">
        console.log('没有环境监管一件事历史数据');
    </c:if>
}
```

## 🔄 数据流程图

```
页面请求 → /xcjc接口
    ↓
查询主表数据 (LocalCheck)
    ↓
判断 formType 字段
    ↓
┌─────────────────┬─────────────────┐
│  formType = 1   │  formType ≠ 1   │
│  (环境监管一件事)  │   (原有检查项)    │
└─────────────────┴─────────────────┘
    ↓                     ↓
查询FORM_TYPE=1的        保持原有逻辑
历史数据                  不变
    ↓                     ↓
通过Model传递给前端       通过Model传递给前端
    ↓                     ↓
前端JavaScript从页面      前端使用原有逻辑
初始化数据中获取并回显     处理数据回显
```

## 🎯 关键优势

### 1. 性能优化
- **一次性加载**：页面初始化时一次性获取所有需要的数据
- **减少请求**：无需额外的AJAX请求获取历史数据
- **提升响应速度**：用户打开页面即可看到完整的历史数据

### 2. 架构简化
- **统一入口**：所有数据处理逻辑集中在一个接口中
- **减少复杂度**：移除了单独的数据加载接口
- **便于维护**：相关逻辑集中管理

### 3. 兼容性保证
- **条件化处理**：根据表单类型条件化执行不同逻辑
- **向后兼容**：完全保持原有功能不变
- **渐进式增强**：新功能作为扩展添加

## 📝 实现要点

1. **始终查询检查项配置**：`checkItemConfigService.getTreeStructure()` 无论哪种类型都需要执行
2. **条件化历史数据查询**：只有当 `formType=1` 时才查询环境监管一件事历史数据
3. **数据传递优化**：通过Model直接传递数据给前端，避免额外请求
4. **前端自动回显**：前端从页面初始化数据中获取历史数据并自动回显
5. **表单自动显示**：如果有历史数据，自动显示环境监管一件事表单

这种集成化的实现方式既保证了功能的完整性，又优化了性能和用户体验，同时保持了与原有系统的完全兼容。
