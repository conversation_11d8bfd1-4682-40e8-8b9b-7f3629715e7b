package org.changneng.framework.frameworkapi.controller;

import org.changneng.framework.frameworkapi.service.IComplaintService;
import org.changneng.framework.frameworkbusiness.entity.Complaint;
import org.changneng.framework.frameworkcore.utils.APIResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;

@Controller
@RequestMapping(value="/api/complaint")
public class ApiComplaintController {
    @Autowired
    private IComplaintService complaintService;

    //saveComplaint
    @PostMapping(value="/saveComplaint")
    @ResponseBody
    public APIResponseJson saveComplaint(@RequestBody Complaint complaint){
        try {
            //获取当前时间
            complaint.setCreateDate(new Date());
            complaintService.saveComplaint(complaint);
            return new APIResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(),"反馈成功","投诉反馈成功",null);
        }catch (Exception e){
            return new APIResponseJson().failure(HttpStatus.OK.toString(), SystemStatusCode.SAVE_FAILURE.toString(),"反馈失败","投诉反馈失败",null);
        }
    }
}
