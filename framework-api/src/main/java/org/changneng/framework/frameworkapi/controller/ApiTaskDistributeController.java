package org.changneng.framework.frameworkapi.controller;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.changneng.framework.frameworkapi.controller.common.CommSysLogUtils;
import org.changneng.framework.frameworkapi.service.IUserService;
import org.changneng.framework.frameworkapi.service.TaskDistributeService;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.entity.AppFileItem;
import org.changneng.framework.frameworkbusiness.entity.AppTask;
import org.changneng.framework.frameworkbusiness.entity.AppTaskFallback;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.LawObjectJsonBean;
import org.changneng.framework.frameworkbusiness.entity.RecordEvidence;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SpecialActionJson;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TableBean;
import org.changneng.framework.frameworkbusiness.entity.Task;
import org.changneng.framework.frameworkbusiness.entity.TaskRequireFiles;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.entity.VideoEvidence;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatCommit;
import org.changneng.framework.frameworkbusiness.service.ITmEvidenceCollectService;
import org.changneng.framework.frameworkbusiness.service.JcblService;
import org.changneng.framework.frameworkbusiness.service.TAreaService;
import org.changneng.framework.frameworkbusiness.service.TaskFlowService;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.APIResponseJson;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.Const;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value="/api/taskDistribute")
@Validated
public class ApiTaskDistributeController {
	
	  @Autowired
	  private TAreaService tAreaService;
	  @Autowired
	  private JcblService jcblService;
	  @Autowired
	  private TaskDistributeService taskDistributeService;  
	  @Autowired
	  private TaskFlowService taskFlowService;		
	  @Autowired
	  private ITmEvidenceCollectService tmEvidenceCollectService;
		
	  @Autowired
	  private SysUsersMapper sysUsersMapper;
		
	  @Autowired
	  private IUserService userService;
		
	  @Autowired
	  private CommSysLogUtils  sommSysLogUtils;
	/**
	 * 任务管理-任务分配
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 * @param status 直接发起 任务 1状态，和任务分配，直接发起返回用户信息
	 */
	@RequestMapping(value = "/taskDistrbutePage")
	public AppTask goRwfp(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value = "taskId", required = false) String taskId,
			@RequestParam(value = "objectId", required = false) String objectId,
			@RequestParam(value = "status", required = false) String status
			)
			throws Exception {
		AppTask appTask = new AppTask();
		AppTaskFallback task = new AppTaskFallback();
		try {
			String login_token=request.getHeader("login_token");
			SysUsers sysUsers = userService.getCurUserByLoginToken(login_token);
			if(!ChangnengUtil.isNull(taskId)){
			//	task = jcblService.taskByTaskId(taskId);
				AppTaskFallback task1 = taskDistributeService.selectAppTaskFallbackByTaskId(taskId);
				if(task1 ==null){
					Task taskByTaskId = jcblService.taskByTaskId(taskId);
					if(!ChangnengUtil.isNull(taskByTaskId)){
						BeanUtils.copyProperties(taskByTaskId, task);
					}
				}else{
					task =task1;
				}
				//V2.0.3 新增维护的字段
				Task tempTesk = jcblService.taskByTaskId(taskId);
				task.setLawEnforcementStartTime(tempTesk.getLawEnforcementStartTime());
				task.setLawEnforcementEndTime(tempTesk.getLawEnforcementEndTime());
				if("1".equals(task.getLawObjectType())){
					//企业
					task.setCardTypeName("统一社会信用代码");
				}else if("4".equals(task.getLawObjectType())){
					//自然保护区
					task.setCardTypeName("管理机构统一社会信用代码");
				}
				List<TaskRequireFiles> taskRequireFilesByTaskId = jcblService.getTaskRequireFilesByTaskId(taskId);
				task.setFilesList(taskRequireFilesByTaskId);
			}
			if(!ChangnengUtil.isNull(objectId)){
				//执法对象页面跳转过来的对象信息
				LawEnforceObject lawEnforObject = jcblService.lawEnforceObjectById(objectId);
				if(lawEnforObject != null){
					LawObjectJsonBean json = new LawObjectJsonBean();
					json =	changeDataLawObject(lawEnforObject,json);
					//执法对象id
					task.setLawObjectId(json.getId());
					//执法对象名称
					task.setLawObjectName(json.getObjectName());
					//执法对象证件类型
					task.setCardCode(json.getCardCode());
					task.setCardTypeCode(json.getCardTypeCode());
					task.setCardTypeName(json.getCardTypeName());
					task.setAddress(json.getAddress());
					task.setLinkman(json.getLinkMan());
					task.setLegalPhone(json.getLinkPhone());
					task.setLawObjectType(json.getTypeCode());
					//V2.0.3 新增维护的字段
					task.setXslw(lawEnforObject.getXslw());
					task.setSypwxkhyjsgfCode(lawEnforObject.getSypwxkhyjsgfCode());
					task.setSypwxkhyjsgfName(lawEnforObject.getSypwxkhyjsgfName());
					if("1".equals(task.getLawObjectType())){
						//企业
						task.setCardTypeName("统一社会信用代码");
					}else if("4".equals(task.getLawObjectType())){
						//自然保护区
						task.setCardTypeName("管理机构统一社会信用代码");
					}
					appTask.setGisCoordinateX(lawEnforObject.getGisCoordinateX());
					appTask.setGisCoordinateY(lawEnforObject.getGisCoordinateY());
					appTask.setGisCoordinateX84(lawEnforObject.getGisCoordinateX84());
					appTask.setGisCoordinateY84(lawEnforObject.getGisCoordinateY84());
				}
			}
			if("5".equals(task.getLawObjectType()) || "3".equals(task.getLawObjectType())){
				//个体和无主
				appTask.setCardTypeList(jcblService.TcDictionaryList("CARD_TYPE"));
			}else if("2".equals(task.getLawObjectType())){
				//个人
				appTask.setCardTypeList( jcblService.TcDictionaryList("IDENTITY_CARD"));
			}
			
			if("1".equals(status)){
				//直接发起，查询当前登录的检查人信息
				 // SysUsers sysUsers =sysUsersMapper.selectByPrimaryKey("4F54617AD273BFBFE055000000000001");
				  task.setChecUserIds(sysUsers.getId());
				  task.setChecUserNames(sysUsers.getLoginname());
				  //law_certificate_enabled执法证号是否启用
				  Integer lawCertificateEnabled = sysUsers.getLawCertificateEnabled();
				  //supervisio_certificate_enabled 监察证号是否启用
				  Integer supervisioCertificateEnabled = sysUsers.getSupervisioCertificateEnabled();
				  //执法证号
				  String lawEnforcId = sysUsers.getLawEnforcId();
				  //监察证号
				  String supervisionCertificateId = sysUsers.getSupervisionCertificateId();
				  //执法证号启用并且有执法证号
				  if(lawCertificateEnabled ==1 && !ChangnengUtil.isNull(lawEnforcId)){
					  task.setLawEnforcIds(lawEnforcId);
				  }
				  //执法证没用启用，监察证启用，并且有监察证号
				  if(supervisioCertificateEnabled ==1 &&  lawCertificateEnabled != 1 && !ChangnengUtil.isNull(supervisionCertificateId)){
					  task.setLawEnforcIds(supervisionCertificateId);
				  }
				  //执法，监察证号都没有启用
				  if(lawCertificateEnabled !=1 && supervisioCertificateEnabled != 1   ){
					  task.setLawEnforcIds("无");
				  }
				//  appTask.setSysUsers(sysUsers);
			}
			appTask.setTask(task);
			// 查询字典表中的监察类型
			List<TcDictionary> monitorTypeList = jcblService.TcDictionaryList("MONITOR_TYPE");
			List<TcDictionary> taskSourceList = taskFlowService.getTcDictionaryByTaskSource(Const.TASK_SOURCE, 1+"");
			// 移除掉  固定污染源随机抽查
			for (int i = 0; i < taskSourceList.size(); i++) {
				if("固定污染源随机抽查".equals(taskSourceList.get(i).getName())){
					taskSourceList.remove(i);
					break;
				}
			}
			
			List<SceneSysSpecialModel> sceneSysSpecialModelList = taskDistributeService.getSceneSysSpecialModel(sysUsers);
			for(SceneSysSpecialModel sceneSysSpecialModel :sceneSysSpecialModelList){
				sceneSysSpecialModel.setTaskLawObjectStatus("3");//特定任务
			}
			appTask.setMonitorTypeList(monitorTypeList);
			appTask.setTaskSourceList(taskSourceList);
			appTask.setSceneSysSpecialModelList(sceneSysSpecialModelList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return appTask;
	}
	/**
	 * specialTaskList
	 */
	@RequestMapping(value = "/specialTaskList")
	public ResponseJson specialTaskList(HttpServletRequest request, HttpServletResponse response){
		try {
			String login_token=request.getHeader("login_token");
			SysUsers sysUsers = userService.getCurUserByLoginToken(login_token);
			List<SceneSysSpecialModel> sceneSysSpecialModelList = taskDistributeService.getSceneSysSpecialModel(sysUsers);
			for(SceneSysSpecialModel sceneSysSpecialModel :sceneSysSpecialModelList){
				sceneSysSpecialModel.setTaskLawObjectStatus("3");//特定任务
			}
			return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功！","查询任务信息成功", sceneSysSpecialModelList);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.QUERY_FAILURE.toString(),"查询失败",e.getMessage(), null);
		}
	}
	/**
	 * 上传图片（任务办理发起和保存是同步）
	 * @param list
	 * @param taskId
	 * @param code
	 * @param name
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/savejcblFile", method = RequestMethod.POST)
	public ResponseJson taskManagerXczfZjzjSave(@RequestBody List<SysFiles> list,String taskId,HttpServletRequest request, HttpServletResponse response){
		try {
			jcblService.taskManagerXczfZjfjSave(taskId,list);
			return new ResponseJson().success(HttpStatus.OK.toString(),"000","上传成功","附件上传成功", null);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(),"000","上传失败","附件上传失败", null);
		}
	}
	/**
	 * 任务的保存功能 
	 * @param request
	 * @param response
	 * @param task
	 * @param bResult
	 * @param sysFileIds
	 * @param sysFileUrl
	 * @param sysFileType
	 * @param taskId
	 * @param taskFlowId
	 * @throws Exception
	 */
	@RequestMapping(value = "/saveTask")
	@CheckRepeatCommit
	public APIResponseJson saveTask(HttpServletRequest request,HttpServletResponse response,
			@Validated @RequestBody Task task,BindingResult bResult
			) throws Exception {
		if(!bResult.hasErrors()){
			try {
				String login_token=request.getHeader("login_token");
				SysUsers sysUser = userService.getCurUserByLoginToken(login_token);
				if(!ChangnengUtil.isNull(task.getSynchronizationStatus())){
					task.setUpdateObjectState(task.getSynchronizationStatus().toString());
				}
			    APIResponseJson josn = taskDistributeService.TaskDistributeaskSave(task, sysUser);
			    sommSysLogUtils.saveLogInfo(sysUser, businessType.LOGINTASK_DISTRIBUTE.getName(),dbType.ADD.getName(), "saveTask",dbType.ADD.getType());
			    return josn;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.PARAM_VALIDATE_FAILURE.toString(),"参数校验错误",bResult.getFieldError().getDefaultMessage(),null);
		}
		return null;
	}
	/**
	 * 保存并发起任务
	 * @param request
	 * @param response
	 * @param taskId
	 * @param sysFileIds
	 * @param sysFileUrl
	 * @param sysFileType
	 * @param taskFlowId
	 * @param task
	 * @param bResult
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/saveAndStartTask")
	@CheckRepeatCommit
	public APIResponseJson saveAndStartTask(HttpServletRequest request,
			HttpServletResponse response,
			@Validated  @RequestBody Task task,BindingResult bResult ) throws Exception {
			if(!bResult.hasErrors()){
				try {
					String login_token=request.getHeader("login_token");
					SysUsers sysUser = userService.getCurUserByLoginToken(login_token);
					if(!ChangnengUtil.isNull(task.getSynchronizationStatus())){
						task.setUpdateObjectState(task.getSynchronizationStatus().toString());
					}
					task.setIsAppHandle(1);//标记发起 为app状态
					APIResponseJson json = taskDistributeService.jcblTaskSaveAndStart(task,sysUser);
					sommSysLogUtils.saveLogInfo(sysUser, businessType.TASK_START.getName(),dbType.ADD.getName(), "saveAndStartTask",dbType.ADD.getType());
					return json;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}else{
				return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.PARAM_VALIDATE_FAILURE.toString(),"参数校验错误",bResult.getFieldError().getDefaultMessage(),null);
			}
			return null;
			
	}
	
	/**
	 * 直接现场执法保存
	 * @param request
	 * @param response
	 * @param task
	 * @param limitTimeTemp
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/saveLocaleLawTask")
	@CheckRepeatCommit
	public APIResponseJson saveLocaleLaw(HttpServletRequest request,HttpServletResponse response,
			@Validated @RequestBody Task task,BindingResult bResult ,
			@RequestParam(value = "gisCoordinateX", required = false) String gisCoordinateX,
			@RequestParam(value = "gisCoordinateY", required = false) String gisCoordinateY,
			@RequestParam(value = "gisCoordinateX84", required = false) String gisCoordinateX84,
			@RequestParam(value = "gisCoordinateY84", required = false) String gisCoordinateY84
			) throws Exception {
		if(!bResult.hasErrors()){
			String login_token=request.getHeader("login_token");
			SysUsers sysUser = userService.getCurUserByLoginToken(login_token);
			if(!ChangnengUtil.isNull(task.getSynchronizationStatus())){
				task.setUpdateObjectState(task.getSynchronizationStatus().toString());
			}
			if(!ChangnengUtil.isNull(task.getLawEnforcementStartTimeStr()) && !ChangnengUtil.isNull(task.getLawEnforcementEndTimeStr()) ) {
				if(!ChangnengUtil.isNull(task.getLawEnforcementStartTimeStr())){
					Date getCheckStartDateTemp = DateUtil.getSimpleFormate(task.getLawEnforcementStartTimeStr());
					task.setLawEnforcementStartTime(getCheckStartDateTemp);
				}
				if(!ChangnengUtil.isNull(task.getLawEnforcementEndTimeStr())){
					Date checkEndDate = DateUtil.getSimpleFormate(task.getLawEnforcementEndTimeStr());
					task.setLawEnforcementEndTime(checkEndDate);
				}
			}
			task.setIsAppHandle(1);//标记发起 为app状态
		    APIResponseJson json = taskDistributeService.jcblTaskSave(task,gisCoordinateX,gisCoordinateY,gisCoordinateX84,gisCoordinateY84,sysUser);
		    sommSysLogUtils.saveLogInfo(sysUser, businessType.START_LOCAL_LAW.getName(),dbType.ADD.getName(), "saveLocaleLaw",dbType.ADD.getType());
		    return json;
		}else{
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.PARAM_VALIDATE_FAILURE.toString(),"参数校验错误",bResult.getFieldError().getDefaultMessage(),null);
		}
	}
	
	/**
	 * 文件上传
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value="/updateFile")
	@ResponseBody
    public ResponseJson handleFormUpload(HttpServletRequest request,HttpServletResponse response,String uid) throws Exception {
			System.out.println("dsfsfds");
		  SysUsers sysUsers =sysUsersMapper.selectByPrimaryKey(uid);
		List<SysFiles> list=null;
		try {
			list = taskDistributeService.fileUpload(request, response,sysUsers);
			return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UP_SUCCESS.toString(),"上传成功","文件上传成功",list);
		} catch (BusinessException e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.DOWN_FAILURE.toString(),"上传失败","文件上传失败",list);
		}
    }
	
	/**
	 * 专项行动查询
	 * @param request
	 * @param response
	 * @param pageNum
	 * @param pageSize
	 * @return
	 * @throws ParseException
	 * @throws BusinessException 
	 */
	@RequestMapping(value = "/specialActionList", method = RequestMethod.POST)
	@ResponseBody
	public Object specialActionList(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value = "pageNum", required = false) String pageNum,
			@RequestParam(value = "pageSize", required = false) String pageSize,
			@RequestParam(value = "status", required = false) String status)
			throws ParseException, BusinessException {
		// 分页页数页数
		int pNum = 0;
		if (pageNum != null && !"".equals(pageNum)){
			pNum = Integer.parseInt(pageNum);	
		}
		int pageSize11 = 10;
		if (pageSize != null && !"".equals(pageSize)){
			pageSize11 = Integer.parseInt(pageSize);
		}
		try {
			String login_token=request.getHeader("login_token");
			SysUsers sysUser = userService.getCurUserByLoginToken(login_token);
			PageBean<SceneSysSpecialModel> pageBean = jcblService.specialActionList(pNum, pageSize11,status,sysUser);
			TableBean<SceneSysSpecialModel> table = new TableBean<SceneSysSpecialModel>();
			table.setTotal(pageBean.getTotal());
			List<SceneSysSpecialModel> list = pageBean.getList();
			table.setRecords(list);
			return table;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
		
	}
	
	
	/**
	 * AppTaskWrit
	 * 获取初始化 文书列表
	 */
	
	@RequestMapping(value="getTaskWrit")
	@ResponseBody
	public APIResponseJson getTaskWrit(String taskId){
		APIResponseJson json = new APIResponseJson();
		json = taskDistributeService.getTaskWrit(taskId);
		return json;
	}
	/**
	 * 文书列表 查看更多
	 * @param type 0现场检查表 1询问笔录 2 勘察笔录
	 * @param id 
	 * @return
	 */
	@RequestMapping(value="getAdjuctList")
	@ResponseBody
	public APIResponseJson getAdjuctList(String type,String id){
		APIResponseJson json = new APIResponseJson();
		json = taskDistributeService.getAdjuctList(type,id);
		return json;
	}
	/**
	 *  附件上传现场检查表 ,询问笔录
	 * @param uid 用户id
	 * @param taskId 任务id
	 * @param itemType 类型0现场检查表 1询问笔录
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/upChickItemAdjuct", method = RequestMethod.POST)
	@ResponseBody
	public APIResponseJson upChickItemAdjuct(String uid,String itemId,String taskId,String itemType,HttpServletRequest request, HttpServletResponse response){
		try {
			//tmEvidenceCollectService.taskManagerXczfZjfjSave(taskId,code,name,list );
			SysUsers sysUsers =sysUsersMapper.selectByPrimaryKey(uid);
			//name =URLDecoder.decode(request.getParameter("name"),"UTF-8");
			AppFileItem fileItemList = taskDistributeService.uptaskDistributeServiceAndAakRecrodAdjuct(request, response,taskId,itemType,itemId,sysUsers );
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(),"上传成功","上传照片证据信息成功",fileItemList);
		} catch (Exception e) {
			e.printStackTrace();
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.QUERY_FAILURE.toString(),"上传失败","上传照片证据信息失败",null);
		}
	}
	
	/**
	 * 文书删除附件
	 * @param ids id
	 * @param itemType 类型现场检查表为0 询问笔录为1 勘察笔录为2
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/deleteWritAjduct", method = RequestMethod.POST)
	public APIResponseJson deleteWritAjduct(String ids,String itemType,HttpServletRequest request, HttpServletResponse response){
		try {
			APIResponseJson json =new APIResponseJson();
			json =taskDistributeService.deleteWritAjduct(ids,itemType);
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.DELETE_SUCCESS.toString(),"删除成功","附件删除成功",json);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.DELETE_ERROR.toString(),"系统失败","附件删除失败",null);
		}
	}
	
	/**
	 * 勘察笔录附件上传
	 */
	@RequestMapping(value = "/upSurveyPicsAdjuct", method = RequestMethod.POST)
	@ResponseBody
	public APIResponseJson upSurveyPicsAdjuct(String uid,String taskId,String itemId,String fileTypeCode,HttpServletRequest request, HttpServletResponse response){
		try {
			//tmEvidenceCollectService.taskManagerXczfZjfjSave(taskId,code,name,list );
			SysUsers sysUsers =sysUsersMapper.selectByPrimaryKey(uid);
			//name =URLDecoder.decode(request.getParameter("name"),"UTF-8");
			AppFileItem fileItemList = taskDistributeService.upSurveyPicsAdjuct(request, response,taskId,fileTypeCode,itemId,sysUsers );
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(),"上传成功","上传照片证据信息成功",fileItemList);
		} catch (Exception e) {
			e.printStackTrace();
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.QUERY_FAILURE.toString(),"上传失败","上传照片证据信息失败",null);
		}
	}
	/**
	 * 删除任务附件
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/fileinfoDelete", method = RequestMethod.POST)
	public APIResponseJson fileinfoDelete(String id,HttpServletRequest request, HttpServletResponse response){
		try {
			jcblService.deletefileInfo(id);
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.DELETE_SUCCESS.toString(),"删除成功","附件删除成功",null);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.DELETE_ERROR.toString(),"系统失败","附件删除失败",null);
		}
	}
	
	public LawObjectJsonBean changeDataLawObject(LawEnforceObject lawEnforObject,
			LawObjectJsonBean json) {
		String typeCode = lawEnforObject.getTypeCode();
		
		String socialCreditCode = lawEnforObject.getSocialCreditCode();
		String legalPerson = lawEnforObject.getLegalPerson();
		String legalPhone = lawEnforObject.getLegalPhone();
		String address = lawEnforObject.getAddress();
		String objectName = lawEnforObject.getObjectName();
		String cardNumber = lawEnforObject.getCardNumber();
		String personcardTypeName = lawEnforObject.getPersoncardTypeName();
		String personcardTypeCode = lawEnforObject.getPersoncardTypeCode();
		String cardTypeName = lawEnforObject.getCardTypeName();
		String cardTypeCode = lawEnforObject.getCardTypeCode();
		String orgCode = lawEnforObject.getOrgCode();
		String gisCoordinateX = lawEnforObject.getGisCoordinateX();
		String gisCoordinateY = lawEnforObject.getGisCoordinateY();
		String chargePerson = lawEnforObject.getChargePerson();
		String chargePersonPhone= lawEnforObject.getChargePersonPhone();
		json.setGisCoordinateX(gisCoordinateX);
		json.setGisCoordinateY(gisCoordinateY);
		if("1".equals(typeCode)){
			//企业
					json.setId(lawEnforObject.getId());	//主键id
					json.setTypeCode("1");
					json.setGisCoordinateX(gisCoordinateX);
					json.setGisCoordinateY(gisCoordinateY);
					if(socialCreditCode!=null && !"".equals(socialCreditCode)){
						json.setCardCode(socialCreditCode);//证件号
					}
					if(chargePerson != null && !"".equals(chargePerson)){
						json.setLinkMan(chargePerson);//姓名
					}
					if(chargePersonPhone != null && !"".equals(chargePersonPhone)){
						json.setLinkPhone(chargePersonPhone);
						
					}
					if(address != null && !"".equals(address)){
						
						json.setAddress(address);	//地址
					}
					if(objectName != null && !"".equals(objectName)){
						json.setObjectName(objectName);//对象名称
					}
					
		}else if("2".equals(typeCode)){
			//个人	
			if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
			if(cardNumber != null &&!"".equals(cardNumber)){
				json.setCardCode(cardNumber);//证件号
			}
			if(legalPerson != null && !"".equals(legalPerson)){
				json.setLinkMan(legalPerson);//姓名
			}
			if(legalPhone != null && !"".equals(legalPhone)){
				json.setLinkPhone(legalPhone);
				
			}
			if(address != null && !"".equals(address)){
				
				json.setAddress(address);	//地址
			}
			if(personcardTypeName != null &&!"".equals(personcardTypeName)){
				
				json.setCardTypeName(personcardTypeName);//证件类型
			}
			if(personcardTypeCode != null && !"".equals(personcardTypeCode)){
				
				json.setCardTypeCode(personcardTypeCode);	//证件类型code
			}
			if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
					json.setId(lawEnforObject.getId());
					json.setTypeCode("2");
		}else if("3".equals(typeCode)){
			//个人，三无，小三产管理
			if(cardNumber!=null && !"".equals(cardNumber)){
				json.setCardCode(cardNumber);//证件号
			}
			if(legalPerson != null && !"".equals(legalPerson)){
				json.setLinkMan(legalPerson);//姓名
			}
			if(legalPhone != null && !"".equals(legalPhone)){
				json.setLinkPhone(legalPhone);
				
			}
			if(address != null && !"".equals(address)){
				
				json.setAddress(address);	//地址
			}
			if(cardTypeName!= null && !"".equals(cardTypeName)){
				json.setCardTypeName(cardTypeName);//证件类型
			}
			if(cardTypeCode!= null && !"".equals(cardTypeCode)){
				json.setCardTypeCode(cardTypeCode);	//证件类型code
			}
			if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
					json.setId(lawEnforObject.getId());
					json.setTypeCode("3");
		}else if("4".equals(typeCode)){
				//自然保护区
			if(orgCode!=null && !"".equals(orgCode)){
				json.setCardCode(orgCode);//证件号
			}	
			if(legalPerson != null && !"".equals(legalPerson)){
				json.setLinkMan(legalPerson);//姓名
			}
			if(legalPhone != null && !"".equals(legalPhone)){
				json.setLinkPhone(legalPhone);
			}if(address != null && !"".equals(address)){
				
				json.setAddress(address);	//地址
			}if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
					json.setId(lawEnforObject.getId());
					json.setTypeCode("4");
		}else if("6".equals(typeCode)){
			//水源地
			if(orgCode!=null && !"".equals(orgCode)){
				json.setCardCode(orgCode);//证件号
			}
			if(legalPerson != null && !"".equals(legalPerson)){
				json.setLinkMan(legalPerson);//姓名
			}
			if(legalPhone != null && !"".equals(legalPhone)){
				json.setLinkPhone(legalPhone);
			}if(address != null && !"".equals(address)){

				json.setAddress(address);	//地址
			}if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
			json.setId(lawEnforObject.getId());
			json.setTypeCode("6");
			json.setWaterSourceCode(lawEnforObject.getWaterSourceCode());
			json.setWaterSourceLevel(lawEnforObject.getWaterSourceLevel());
			json.setWaterSourceCategory(lawEnforObject.getWaterSourceCategory());
			json.setSaterIntake(lawEnforObject.getSaterIntake());
			json.setWaterSupply(lawEnforObject.getWaterSupply());
			json.setIsReserve(lawEnforObject.getIsReserve());
			json.setDelimitFileCode(lawEnforObject.getDelimitFileCode());
			json.setIsLandmark(lawEnforObject.getIsLandmark());
			json.setWebsite(lawEnforObject.getWebsite());
			json.setRemarks(lawEnforObject.getRemarks());
			json.setManageOrgName(lawEnforObject.getManageOrgName());
			json.setChargePerson(lawEnforObject.getChargePerson());
			json.setLegalPhone(lawEnforObject.getLegalPhone());

		}else if("5".equals(typeCode)){
			//无主对象
			if(cardNumber!=null && !"".equals(cardNumber)){
				json.setCardCode(cardNumber);//证件号
			}
			if(legalPerson != null && !"".equals(legalPerson)){
				json.setLinkMan(legalPerson);//姓名
			}
			if(legalPhone != null && !"".equals(legalPhone)){
				json.setLinkPhone(legalPhone);
				
			}
			if(address != null && !"".equals(address)){
				
				json.setAddress(address);	//地址
			}
			if(cardTypeName!= null && !"".equals(cardTypeName)){
				json.setCardTypeName(cardTypeName);//证件类型
			}
			if(cardTypeCode!= null && !"".equals(cardTypeCode)){
				json.setCardTypeCode(cardTypeCode);	//证件类型code
			}
			if(objectName != null && !"".equals(objectName)){
				json.setObjectName(objectName);//对象名称
			}
					json.setId(lawEnforObject.getId());
					json.setTypeCode("5");
		}else{
		}
		return json;
	}
}
