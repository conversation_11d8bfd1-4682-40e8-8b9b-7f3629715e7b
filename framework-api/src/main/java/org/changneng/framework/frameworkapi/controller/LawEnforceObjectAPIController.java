package org.changneng.framework.frameworkapi.controller;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.changneng.framework.frameworkapi.controller.common.CommSysLogUtils;
import org.changneng.framework.frameworkapi.service.ILawEnforceObjectAPIService;
import org.changneng.framework.frameworkapi.service.IUserService;
import org.changneng.framework.frameworkapi.service.TaskDistributeService;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.dao.TcDictionaryMapper;
import org.changneng.framework.frameworkbusiness.entity.Area;
import org.changneng.framework.frameworkbusiness.entity.ChickObjectBean;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.LawObjectTypeBean;
import org.changneng.framework.frameworkbusiness.entity.PassingInfo;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.Task;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkbusiness.service.JcblService;
import org.changneng.framework.frameworkbusiness.service.TaskFlowService;
import org.changneng.framework.frameworkbusiness.service.ZfdxManagerService;
import org.changneng.framework.frameworkcore.utils.APIResponseJson;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value="/api/lawEnforceObj")
@Validated
public class LawEnforceObjectAPIController {
	
	@Autowired
	private ILawEnforceObjectAPIService lawEnforceObjectAPIService;
	@Autowired
	private IUserService userService;
	@Autowired
	private ZfdxManagerService zfdxManagerService;
	
	@Autowired
	private ICommonService commonService;
	
	
	@Autowired
	private CommSysLogUtils  sommSysLogUtils;
	@Autowired
	private TaskFlowService taskFlowService;
	@Autowired
	private TaskDistributeService taskDistributeService;
	@Autowired
	private TcDictionaryMapper tcDictionaryMapper;
	@Autowired
	private JcblService jcblService;
	/***
	 * 查询附近执法对象信息接口
	 * @param token
	 * @param areaCode
	 * @param pageNum
	 * @param pageSize
	 * @param longitude
	 * @param latitude
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/queryLawEnforceObjList", method = RequestMethod.POST)
	public APIResponseJson queryLawEnforceObjList(@RequestParam(value="areaCode")@NotBlank(message = "区划代码不能为空" )String areaCode,
				@RequestParam(value="pageNum")@Min(value=1,message = "当前页数最小值为1")@NotNull(message = "当前页数不能为空" ) Integer pageNum,
				@RequestParam(value="pageSize")@Min(value=5,message = "每页显示的数量最小为5条")@Max(value=20,message = "每页显示的数量最大为20条")@NotNull(message = "每页显示的数量不能为空" ) Integer pageSize,
				@RequestParam(value="longitude",required=false)String longitude,@RequestParam(value="latitude",required=false) String latitude,
				@RequestParam(value="objectName")String objectName,@RequestParam(value="typeCode") String typeCode,
				@RequestParam(value="belongAreaId")String belongAreaId,@RequestParam(value="powerAreaId") String powerAreaId,
				HttpServletRequest request,HttpServletResponse response){
		
		try {
			PageBean<LawEnforceObjectWithBLOBs> pageList=lawEnforceObjectAPIService.queryLawEnforceObjList(areaCode,pageNum,pageSize,longitude,latitude,objectName,typeCode,belongAreaId,powerAreaId);
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询附近执法对象成功",pageList);
		} catch (Exception e) {
			e.printStackTrace();
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询附近执法对象错误",null);
		}
		
	}
	
	
	/***
	 * 查询附近执法对象信息接口1
	 * @param token
	 * @param areaCode
	 * @param pageNum
	 * @param pageSize
	 * @param longitude
	 * @param latitude
	 * @param objectName
	 * @param typeCode
	 * @param belongAreaId
	 * @param powerAreaId
	 * @param address 地址
	 * @param attributeName  查询专项属性
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/queryLawEnforceObjList1", method = RequestMethod.POST)
	public APIResponseJson queryLawEnforceObjList1(@RequestParam(value="areaCode")@NotBlank(message = "区划代码不能为空" )String areaCode,
				@RequestParam(value="pageNum")@Min(value=1,message = "当前页数最小值为1")@NotNull(message = "当前页数不能为空" ) Integer pageNum,
				@RequestParam(value="pageSize")@Min(value=5,message = "每页显示的数量最小为5条")@Max(value=20,message = "每页显示的数量最大为20条")@NotNull(message = "每页显示的数量不能为空" ) Integer pageSize,
				@RequestParam(value="longitude",required=false)String longitude,@RequestParam(value="latitude",required=false) String latitude,
				@RequestParam(value="objectName")String objectName,@RequestParam(value="typeCode") String typeCode,
				@RequestParam(value="belongAreaId")String belongAreaId,@RequestParam(value="powerAreaId") String powerAreaId,
				@RequestParam(value = "address", required = false) String address, @RequestParam(value = "attributeName", required = false) String attributeName, 
				HttpServletRequest request,HttpServletResponse response){
		
		try {
			PageBean<LawEnforceObjectWithBLOBs> pageList=lawEnforceObjectAPIService.queryLawEnforceObjList1(areaCode, pageNum, pageSize,
					longitude, latitude, objectName, typeCode, belongAreaId, powerAreaId, address, attributeName);
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询附近执法对象成功",pageList);
		} catch (Exception e) {
			e.printStackTrace();
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询附近执法对象错误",null);
		}
		
	}
	
	/**
	 * 查询执法对象详细信息基本接口
	 * @param token
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/queryLawEnforceObjById", method = RequestMethod.POST)
	public APIResponseJson queryLawEnforceObjById(@RequestParam(value="id")@NotBlank(message = "id不能为空" ) String id,
			HttpServletRequest request,HttpServletResponse response){
		try {
			LawEnforceObjectWithBLOBs lawObject = new LawEnforceObjectWithBLOBs();
			if (!"".equals(id) && id != null) {
				lawObject = zfdxManagerService.selectByPrimaryKey(id);
			}
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询执法对象详细信息成功",lawObject);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询执法对象详细信息错误",null);
		}
	}
	
	/**
	 * 查询执法信息接口
	 * @param token
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/queryZhiFaInfo", method = RequestMethod.POST)
	public APIResponseJson queryZhiFaInfo(@RequestParam(value="id")@NotBlank(message = "id不能为空" ) String id,HttpServletRequest request,HttpServletResponse response){
		try {
			ChickObjectBean  cob=lawEnforceObjectAPIService.detailedInformationObject(id,"","");
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询执法信息成功",cob);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询执法信息错误",null);
		}
	}
	
	/**
	 * 保存执法对象信息
	 * @param lawEnforceObject
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/lawEnforceObjSave", method = RequestMethod.POST)
	public  APIResponseJson  lawEnforceObjSave(@Valid @RequestBody LawEnforceObjectWithBLOBs record,BindingResult bindingResult,
			HttpServletRequest request,HttpServletResponse response){
		try {
			Area  area=commonService.queryTareaCityByCountryCode(record.getBelongAreaId());
			if(area!=null&&!"".equals(area)){
				record.setBelongAreaCityId(area.getCode());
				record.setBelongAreaCityName(area.getName());
			}
			SysUsers user=userService.getCurUserByLoginToken(request.getHeader("login_token"));
			ResponseJson rj=null;
			if(user!=null&&!"".equals(user)){
				rj=lawEnforceObjectAPIService.lawEnforceObjSave(record,user);
				sommSysLogUtils.saveLogInfo(user, businessType.ADD_LAW_OBJECT.getName(),dbType.ADD.getName(), "lawEnforceObjSave",dbType.ADD.getType());
			}else{
				return new APIResponseJson().failure(HttpStatus.BAD_REQUEST.toString(),SystemStatusCode.SAVE_FAILURE.toString(),"保存失败","保存执法对象信息失败,用户session已失效",null);
			}
			
			if(rj.getMeta().getResult().equals("success")){
				return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(),"保存成功","保存执法对象信息成功",rj.getData());
			}else{
				return new APIResponseJson().failure(HttpStatus.BAD_REQUEST.toString(),SystemStatusCode.SAVE_FAILURE.toString(),"保存失败","保存执法对象信息失败,"+rj.getMeta().getMessage(),null);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","保存执法对象信息错误",null);
		}
	}
	
	/**
	 * 执法对象修改接口
	 * @param lawObject
	 * @param token
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/lawEnforceObjUpdate", method = RequestMethod.POST)
	public APIResponseJson lawEnforceObjUpdate(@Valid @RequestBody LawEnforceObjectWithBLOBs record,BindingResult bindingResult,
			HttpServletRequest request,HttpServletResponse response){
		try {
			if(record.getId()!=null&&!"".equals(record.getId())){
				Area  area=commonService.queryTareaCityByCountryCode(record.getBelongAreaId());
				if(area!=null&&!"".equals(area)){
					record.setBelongAreaCityId(area.getCode());
					record.setBelongAreaCityName(area.getName());
				}
				SysUsers user=userService.getCurUserByLoginToken(request.getHeader("login_token"));
				
				ResponseJson json=null;
				if(user!=null&&!"".equals(user)){
					json=lawEnforceObjectAPIService.lawEnforceObjUpdate(record,user);
					sommSysLogUtils.saveLogInfo(user, businessType.ADD_LAW_OBJECT.getName(),dbType.ADD.getName(), "lawEnforceObjUpdate",dbType.ADD.getType());
				}else{
					return new APIResponseJson().failure(HttpStatus.BAD_REQUEST.toString(),SystemStatusCode.UPDATE_FAILURE.toString(),"修改失败","修改执法对象信息失败,用于session已经失效",null);
				}
				
				if(json.getMeta().getResult().equals("success")){
					return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(),"修改成功","修改执法对象信息成功",json.getData());
				}else{
					return new APIResponseJson().failure(HttpStatus.BAD_REQUEST.toString(),SystemStatusCode.UPDATE_FAILURE.toString(),"修改失败","修改执法对象信息失败,"+json.getMeta().getDetailMessage(),null);
				}
			}else{
				return new APIResponseJson().failure(HttpStatus.BAD_REQUEST.toString(),SystemStatusCode.UPDATE_FAILURE.toString(),"修改失败","修改执法对象信息失败,id不能为空",null);
			}
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","修改执法对象信息错误",null);
		}
	}
	
	/**
	 * @param token
	 * @param pi
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/passedLawEnforceObjById", method = RequestMethod.POST)
	public  APIResponseJson  passedLawEnforceObjById(@RequestParam(value="token")@NotBlank(message = "token不能为空" ) String token,
			@Valid  @RequestBody PassingInfo pi,HttpServletRequest request,HttpServletResponse response){
		lawEnforceObjectAPIService.passedLawEnforceObjById(pi);
		return null;
	}
	/**
	 * 根据传入类型返回字典列表
	 * @param selectType
	 * @param request
	 * @param response
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value="/getDictionaryListBySelectType", method = RequestMethod.POST)
	@ResponseBody
	public  APIResponseJson  getDictionaryListByTemp(@RequestParam(value="selectType")@NotBlank(message = "字典类型不能为空" )String selectType,
			HttpServletRequest request,HttpServletResponse response){
		try {
			List<TcDictionary> tcDictionaries = tcDictionaryMapper.getTcDictionaryListByCode(selectType, null);
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询数据字典成功",tcDictionaries);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询数据字典错误",null);
		}
	}
	
	/**
	 * 执法小结信息接口
	 * @param lawObject
	 * @param token
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/zfxj", method = RequestMethod.POST)
	public APIResponseJson zfxj(String taskId,	HttpServletRequest request,HttpServletResponse response){
		try {
			Task task = new Task();
			if(!ChangnengUtil.isNull(taskId)) {
				task = jcblService.lawEnforSummaryInfo(taskId);
				if(!ChangnengUtil.isNull(task)) {
					if(ChangnengUtil.isNull(task.getIsIllegalactName())) {
						task.setIsIllegalactName("1");						
					}
				}
			}
			return new APIResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(),"查询成功","查询执法小结成功",task);
		} catch (Exception e) {
			return new APIResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(),SystemStatusCode.SYSTEM_ERROR.toString(),"系统错误","查询执法小结信息错误",null);
		}
	}
	/**
	 * 保存执法小结
	 * @param lawEnforceObject
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/lawEnforSummarySave", method = RequestMethod.POST)
	@ResponseBody
	public APIResponseJson lawEnforSummarySave(@RequestBody Task task, HttpServletRequest request,HttpServletResponse response) {
		APIResponseJson json = new APIResponseJson();
		try {
			json = jcblService.lawEnforSummarySaveForAPP(task);
		} catch (Exception e) {
			json.failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.UPDATE_FAILURE.toString(), "系统错误，保存失败", e.getMessage(), null);
			e.printStackTrace();
		}
		return json;
	}
	
}
